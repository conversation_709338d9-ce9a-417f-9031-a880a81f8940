/*
 * WARNING: do not edit!
 * Generated by crypto/objects/obj_dat.pl
 *
 * Copyright 1995-2024 The OpenSSL Project Authors. All Rights Reserved.
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

/* Serialized OID's */
static const unsigned char so[6628] = {
    0x2A,0x86,0x48,0x86,0xF7,0x0D,                 /* [    0] OBJ_rsadsi */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,            /* [    6] OBJ_pkcs */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x05,       /* [   13] OBJ_md5 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x03,0x04,       /* [   21] OBJ_rc4 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x01,  /* [   29] OBJ_rsaEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x04,  /* [   38] OBJ_md5WithRSAEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x05,0x03,  /* [   47] OBJ_pbeWithMD5AndDES_CBC */
    0x55,                                          /* [   56] OBJ_X500 */
    0x55,0x04,                                     /* [   57] OBJ_X509 */
    0x55,0x04,0x03,                                /* [   59] OBJ_commonName */
    0x55,0x04,0x06,                                /* [   62] OBJ_countryName */
    0x55,0x04,0x07,                                /* [   65] OBJ_localityName */
    0x55,0x04,0x08,                                /* [   68] OBJ_stateOrProvinceName */
    0x55,0x04,0x0A,                                /* [   71] OBJ_organizationName */
    0x55,0x04,0x0B,                                /* [   74] OBJ_organizationalUnitName */
    0x55,0x08,0x01,0x01,                           /* [   77] OBJ_rsa */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x07,       /* [   81] OBJ_pkcs7 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x07,0x01,  /* [   89] OBJ_pkcs7_data */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x07,0x02,  /* [   98] OBJ_pkcs7_signed */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x07,0x03,  /* [  107] OBJ_pkcs7_enveloped */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x07,0x04,  /* [  116] OBJ_pkcs7_signedAndEnveloped */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x07,0x05,  /* [  125] OBJ_pkcs7_digest */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x07,0x06,  /* [  134] OBJ_pkcs7_encrypted */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x03,       /* [  143] OBJ_pkcs3 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x03,0x01,  /* [  151] OBJ_dhKeyAgreement */
    0x2B,0x0E,0x03,0x02,0x06,                      /* [  160] OBJ_des_ecb */
    0x2B,0x0E,0x03,0x02,0x09,                      /* [  165] OBJ_des_cfb64 */
    0x2B,0x0E,0x03,0x02,0x07,                      /* [  170] OBJ_des_cbc */
    0x2B,0x0E,0x03,0x02,0x11,                      /* [  175] OBJ_des_ede_ecb */
    0x2B,0x0E,0x03,0x02,0x12,                      /* [  180] OBJ_sha */
    0x2B,0x0E,0x03,0x02,0x0F,                      /* [  185] OBJ_shaWithRSAEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x03,0x07,       /* [  190] OBJ_des_ede3_cbc */
    0x2B,0x0E,0x03,0x02,0x08,                      /* [  198] OBJ_des_ofb64 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,       /* [  203] OBJ_pkcs9 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x01,  /* [  211] OBJ_pkcs9_emailAddress */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x02,  /* [  220] OBJ_pkcs9_unstructuredName */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x03,  /* [  229] OBJ_pkcs9_contentType */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x04,  /* [  238] OBJ_pkcs9_messageDigest */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x05,  /* [  247] OBJ_pkcs9_signingTime */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x06,  /* [  256] OBJ_pkcs9_countersignature */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x07,  /* [  265] OBJ_pkcs9_challengePassword */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x08,  /* [  274] OBJ_pkcs9_unstructuredAddress */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x09,  /* [  283] OBJ_pkcs9_extCertAttributes */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,            /* [  292] OBJ_netscape */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,       /* [  299] OBJ_netscape_cert_extension */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x02,       /* [  307] OBJ_netscape_data_type */
    0x2B,0x0E,0x03,0x02,0x1A,                      /* [  315] OBJ_sha1 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x05,  /* [  320] OBJ_sha1WithRSAEncryption */
    0x2B,0x0E,0x03,0x02,0x0D,                      /* [  329] OBJ_dsaWithSHA */
    0x2B,0x0E,0x03,0x02,0x0C,                      /* [  334] OBJ_dsa_2 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x05,0x0C,  /* [  339] OBJ_id_pbkdf2 */
    0x2B,0x0E,0x03,0x02,0x1B,                      /* [  348] OBJ_dsaWithSHA1_2 */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x01,  /* [  353] OBJ_netscape_cert_type */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x02,  /* [  362] OBJ_netscape_base_url */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x03,  /* [  371] OBJ_netscape_revocation_url */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x04,  /* [  380] OBJ_netscape_ca_revocation_url */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x07,  /* [  389] OBJ_netscape_renewal_url */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x08,  /* [  398] OBJ_netscape_ca_policy_url */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x0C,  /* [  407] OBJ_netscape_ssl_server_name */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x01,0x0D,  /* [  416] OBJ_netscape_comment */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x02,0x05,  /* [  425] OBJ_netscape_cert_sequence */
    0x55,0x1D,                                     /* [  434] OBJ_id_ce */
    0x55,0x1D,0x0E,                                /* [  436] OBJ_subject_key_identifier */
    0x55,0x1D,0x0F,                                /* [  439] OBJ_key_usage */
    0x55,0x1D,0x10,                                /* [  442] OBJ_private_key_usage_period */
    0x55,0x1D,0x11,                                /* [  445] OBJ_subject_alt_name */
    0x55,0x1D,0x12,                                /* [  448] OBJ_issuer_alt_name */
    0x55,0x1D,0x13,                                /* [  451] OBJ_basic_constraints */
    0x55,0x1D,0x14,                                /* [  454] OBJ_crl_number */
    0x55,0x1D,0x20,                                /* [  457] OBJ_certificate_policies */
    0x55,0x1D,0x23,                                /* [  460] OBJ_authority_key_identifier */
    0x55,0x04,0x2A,                                /* [  463] OBJ_givenName */
    0x55,0x04,0x04,                                /* [  466] OBJ_surname */
    0x55,0x04,0x2B,                                /* [  469] OBJ_initials */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x2C,  /* [  472] OBJ_uniqueIdentifier */
    0x55,0x1D,0x1F,                                /* [  482] OBJ_crl_distribution_points */
    0x2B,0x0E,0x03,0x02,0x03,                      /* [  485] OBJ_md5WithRSA */
    0x55,0x04,0x05,                                /* [  490] OBJ_serialNumber */
    0x55,0x04,0x0C,                                /* [  493] OBJ_title */
    0x55,0x04,0x0D,                                /* [  496] OBJ_description */
    0x2A,0x86,0x48,0xCE,0x38,0x04,0x03,            /* [  499] OBJ_dsaWithSHA1 */
    0x2B,0x0E,0x03,0x02,0x1D,                      /* [  506] OBJ_sha1WithRSA */
    0x2A,0x86,0x48,0xCE,0x38,0x04,0x01,            /* [  511] OBJ_dsa */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x03,0x08,       /* [  518] OBJ_rc5_cbc */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x03,0x08,  /* [  526] OBJ_zlib_compression */
    0x55,0x1D,0x25,                                /* [  537] OBJ_ext_key_usage */
    0x2B,0x06,0x01,0x05,0x05,0x07,                 /* [  540] OBJ_id_pkix */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,            /* [  546] OBJ_id_kp */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x01,       /* [  553] OBJ_server_auth */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x02,       /* [  561] OBJ_client_auth */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x03,       /* [  569] OBJ_code_sign */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x04,       /* [  577] OBJ_email_protect */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x08,       /* [  585] OBJ_time_stamp */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x02,0x01,0x15,  /* [  593] OBJ_ms_code_ind */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x02,0x01,0x16,  /* [  603] OBJ_ms_code_com */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x0A,0x03,0x01,  /* [  613] OBJ_ms_ctl_sign */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x0A,0x03,0x03,  /* [  623] OBJ_ms_sgc */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x0A,0x03,0x04,  /* [  633] OBJ_ms_efs */
    0x60,0x86,0x48,0x01,0x86,0xF8,0x42,0x04,0x01,  /* [  643] OBJ_ns_sgc */
    0x55,0x1D,0x1B,                                /* [  652] OBJ_delta_crl */
    0x55,0x1D,0x15,                                /* [  655] OBJ_crl_reason */
    0x55,0x1D,0x18,                                /* [  658] OBJ_invalidity_date */
    0x2B,0x65,0x01,0x04,0x01,                      /* [  661] OBJ_sxnet */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x01,0x01,  /* [  666] OBJ_pbe_WithSHA1And128BitRC4 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x01,0x02,  /* [  676] OBJ_pbe_WithSHA1And40BitRC4 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x01,0x03,  /* [  686] OBJ_pbe_WithSHA1And3_Key_TripleDES_CBC */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x01,0x04,  /* [  696] OBJ_pbe_WithSHA1And2_Key_TripleDES_CBC */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x0A,0x01,0x01,  /* [  706] OBJ_keyBag */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x0A,0x01,0x02,  /* [  717] OBJ_pkcs8ShroudedKeyBag */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x0A,0x01,0x03,  /* [  728] OBJ_certBag */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x0A,0x01,0x04,  /* [  739] OBJ_crlBag */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x0A,0x01,0x05,  /* [  750] OBJ_secretBag */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x0C,0x0A,0x01,0x06,  /* [  761] OBJ_safeContentsBag */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x14,  /* [  772] OBJ_friendlyName */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x15,  /* [  781] OBJ_localKeyID */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x16,0x01,  /* [  790] OBJ_x509Certificate */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x16,0x02,  /* [  800] OBJ_sdsiCertificate */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x17,0x01,  /* [  810] OBJ_x509Crl */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x05,0x0D,  /* [  820] OBJ_pbes2 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x05,0x0E,  /* [  829] OBJ_pbmac1 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x07,       /* [  838] OBJ_hmacWithSHA1 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x02,0x01,       /* [  846] OBJ_id_qt_cps */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x02,0x02,       /* [  854] OBJ_id_qt_unotice */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x0F,  /* [  862] OBJ_SMIMECapabilities */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x05,0x0A,  /* [  871] OBJ_pbeWithSHA1AndDES_CBC */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x02,0x01,0x0E,  /* [  880] OBJ_ms_ext_req */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x0E,  /* [  890] OBJ_ext_req */
    0x55,0x04,0x29,                                /* [  899] OBJ_name */
    0x55,0x04,0x2E,                                /* [  902] OBJ_dnQualifier */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,            /* [  905] OBJ_id_pe */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,            /* [  912] OBJ_id_ad */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x01,       /* [  919] OBJ_info_access */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,       /* [  927] OBJ_ad_OCSP */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x02,       /* [  935] OBJ_ad_ca_issuers */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x09,       /* [  943] OBJ_OCSP_sign */
    0x2A,                                          /* [  951] OBJ_member_body */
    0x2A,0x86,0x48,                                /* [  952] OBJ_ISO_US */
    0x2A,0x86,0x48,0xCE,0x38,                      /* [  955] OBJ_X9_57 */
    0x2A,0x86,0x48,0xCE,0x38,0x04,                 /* [  960] OBJ_X9cm */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,       /* [  966] OBJ_pkcs1 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x05,       /* [  974] OBJ_pkcs5 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,  /* [  982] OBJ_SMIME */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,  /* [  991] OBJ_id_smime_mod */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,  /* [ 1001] OBJ_id_smime_ct */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,  /* [ 1011] OBJ_id_smime_aa */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x03,  /* [ 1021] OBJ_id_smime_alg */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x04,  /* [ 1031] OBJ_id_smime_cd */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x05,  /* [ 1041] OBJ_id_smime_spq */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x06,  /* [ 1051] OBJ_id_smime_cti */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x01,  /* [ 1061] OBJ_id_smime_mod_cms */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x02,  /* [ 1072] OBJ_id_smime_mod_ess */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x03,  /* [ 1083] OBJ_id_smime_mod_oid */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x04,  /* [ 1094] OBJ_id_smime_mod_msg_v3 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x05,  /* [ 1105] OBJ_id_smime_mod_ets_eSignature_88 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x06,  /* [ 1116] OBJ_id_smime_mod_ets_eSignature_97 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x07,  /* [ 1127] OBJ_id_smime_mod_ets_eSigPolicy_88 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x00,0x08,  /* [ 1138] OBJ_id_smime_mod_ets_eSigPolicy_97 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x01,  /* [ 1149] OBJ_id_smime_ct_receipt */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x02,  /* [ 1160] OBJ_id_smime_ct_authData */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x03,  /* [ 1171] OBJ_id_smime_ct_publishCert */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x04,  /* [ 1182] OBJ_id_smime_ct_TSTInfo */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x05,  /* [ 1193] OBJ_id_smime_ct_TDTInfo */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x06,  /* [ 1204] OBJ_id_smime_ct_contentInfo */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x07,  /* [ 1215] OBJ_id_smime_ct_DVCSRequestData */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x08,  /* [ 1226] OBJ_id_smime_ct_DVCSResponseData */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x01,  /* [ 1237] OBJ_id_smime_aa_receiptRequest */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x02,  /* [ 1248] OBJ_id_smime_aa_securityLabel */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x03,  /* [ 1259] OBJ_id_smime_aa_mlExpandHistory */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x04,  /* [ 1270] OBJ_id_smime_aa_contentHint */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x05,  /* [ 1281] OBJ_id_smime_aa_msgSigDigest */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x06,  /* [ 1292] OBJ_id_smime_aa_encapContentType */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x07,  /* [ 1303] OBJ_id_smime_aa_contentIdentifier */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x08,  /* [ 1314] OBJ_id_smime_aa_macValue */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x09,  /* [ 1325] OBJ_id_smime_aa_equivalentLabels */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x0A,  /* [ 1336] OBJ_id_smime_aa_contentReference */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x0B,  /* [ 1347] OBJ_id_smime_aa_encrypKeyPref */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x0C,  /* [ 1358] OBJ_id_smime_aa_signingCertificate */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x0D,  /* [ 1369] OBJ_id_smime_aa_smimeEncryptCerts */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x0E,  /* [ 1380] OBJ_id_smime_aa_timeStampToken */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x0F,  /* [ 1391] OBJ_id_smime_aa_ets_sigPolicyId */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x10,  /* [ 1402] OBJ_id_smime_aa_ets_commitmentType */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x11,  /* [ 1413] OBJ_id_smime_aa_ets_signerLocation */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x12,  /* [ 1424] OBJ_id_smime_aa_ets_signerAttr */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x13,  /* [ 1435] OBJ_id_smime_aa_ets_otherSigCert */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x14,  /* [ 1446] OBJ_id_smime_aa_ets_contentTimestamp */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x15,  /* [ 1457] OBJ_id_smime_aa_ets_CertificateRefs */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x16,  /* [ 1468] OBJ_id_smime_aa_ets_RevocationRefs */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x17,  /* [ 1479] OBJ_id_smime_aa_ets_certValues */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x18,  /* [ 1490] OBJ_id_smime_aa_ets_revocationValues */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x19,  /* [ 1501] OBJ_id_smime_aa_ets_escTimeStamp */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x1A,  /* [ 1512] OBJ_id_smime_aa_ets_certCRLTimestamp */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x1B,  /* [ 1523] OBJ_id_smime_aa_ets_archiveTimeStamp */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x1C,  /* [ 1534] OBJ_id_smime_aa_signatureType */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x1D,  /* [ 1545] OBJ_id_smime_aa_dvcs_dvc */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x03,0x01,  /* [ 1556] OBJ_id_smime_alg_ESDHwith3DES */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x03,0x03,  /* [ 1567] OBJ_id_smime_alg_3DESwrap */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x03,0x05,  /* [ 1578] OBJ_id_smime_alg_ESDH */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x03,0x06,  /* [ 1589] OBJ_id_smime_alg_CMS3DESwrap */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x04,0x01,  /* [ 1600] OBJ_id_smime_cd_ldap */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x05,0x01,  /* [ 1611] OBJ_id_smime_spq_ets_sqt_uri */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x05,0x02,  /* [ 1622] OBJ_id_smime_spq_ets_sqt_unotice */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x06,0x01,  /* [ 1633] OBJ_id_smime_cti_ets_proofOfOrigin */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x06,0x02,  /* [ 1644] OBJ_id_smime_cti_ets_proofOfReceipt */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x06,0x03,  /* [ 1655] OBJ_id_smime_cti_ets_proofOfDelivery */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x06,0x04,  /* [ 1666] OBJ_id_smime_cti_ets_proofOfSender */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x06,0x05,  /* [ 1677] OBJ_id_smime_cti_ets_proofOfApproval */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x06,0x06,  /* [ 1688] OBJ_id_smime_cti_ets_proofOfCreation */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,            /* [ 1699] OBJ_id_pkix_mod */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x02,            /* [ 1706] OBJ_id_qt */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,            /* [ 1713] OBJ_id_it */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,            /* [ 1720] OBJ_id_pkip */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x06,            /* [ 1727] OBJ_id_alg */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,            /* [ 1734] OBJ_id_cmc */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x08,            /* [ 1741] OBJ_id_on */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x09,            /* [ 1748] OBJ_id_pda */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0A,            /* [ 1755] OBJ_id_aca */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0B,            /* [ 1762] OBJ_id_qcs */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0C,            /* [ 1769] OBJ_id_cct */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x01,       /* [ 1776] OBJ_id_pkix1_explicit_88 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x02,       /* [ 1784] OBJ_id_pkix1_implicit_88 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x03,       /* [ 1792] OBJ_id_pkix1_explicit_93 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x04,       /* [ 1800] OBJ_id_pkix1_implicit_93 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x05,       /* [ 1808] OBJ_id_mod_crmf */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x06,       /* [ 1816] OBJ_id_mod_cmc */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x07,       /* [ 1824] OBJ_id_mod_kea_profile_88 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x08,       /* [ 1832] OBJ_id_mod_kea_profile_93 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x09,       /* [ 1840] OBJ_id_mod_cmp */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x0A,       /* [ 1848] OBJ_id_mod_qualified_cert_88 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x0B,       /* [ 1856] OBJ_id_mod_qualified_cert_93 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x0C,       /* [ 1864] OBJ_id_mod_attribute_cert */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x0D,       /* [ 1872] OBJ_id_mod_timestamp_protocol */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x0E,       /* [ 1880] OBJ_id_mod_ocsp */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x0F,       /* [ 1888] OBJ_id_mod_dvcs */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x00,0x10,       /* [ 1896] OBJ_id_mod_cmp2000 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x02,       /* [ 1904] OBJ_biometricInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x03,       /* [ 1912] OBJ_qcStatements */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x04,       /* [ 1920] OBJ_ac_auditEntity */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x05,       /* [ 1928] OBJ_ac_targeting */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x06,       /* [ 1936] OBJ_aaControls */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x07,       /* [ 1944] OBJ_sbgp_ipAddrBlock */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x08,       /* [ 1952] OBJ_sbgp_autonomousSysNum */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x09,       /* [ 1960] OBJ_sbgp_routerIdentifier */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x02,0x03,       /* [ 1968] OBJ_textNotice */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x05,       /* [ 1976] OBJ_ipsecEndSystem */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x06,       /* [ 1984] OBJ_ipsecTunnel */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x07,       /* [ 1992] OBJ_ipsecUser */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x0A,       /* [ 2000] OBJ_dvcs */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x01,       /* [ 2008] OBJ_id_it_caProtEncCert */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x02,       /* [ 2016] OBJ_id_it_signKeyPairTypes */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x03,       /* [ 2024] OBJ_id_it_encKeyPairTypes */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x04,       /* [ 2032] OBJ_id_it_preferredSymmAlg */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x05,       /* [ 2040] OBJ_id_it_caKeyUpdateInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x06,       /* [ 2048] OBJ_id_it_currentCRL */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x07,       /* [ 2056] OBJ_id_it_unsupportedOIDs */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x08,       /* [ 2064] OBJ_id_it_subscriptionRequest */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x09,       /* [ 2072] OBJ_id_it_subscriptionResponse */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x0A,       /* [ 2080] OBJ_id_it_keyPairParamReq */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x0B,       /* [ 2088] OBJ_id_it_keyPairParamRep */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x0C,       /* [ 2096] OBJ_id_it_revPassphrase */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x0D,       /* [ 2104] OBJ_id_it_implicitConfirm */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x0E,       /* [ 2112] OBJ_id_it_confirmWaitTime */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x0F,       /* [ 2120] OBJ_id_it_origPKIMessage */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x01,       /* [ 2128] OBJ_id_regCtrl */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x02,       /* [ 2136] OBJ_id_regInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x01,0x01,  /* [ 2144] OBJ_id_regCtrl_regToken */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x01,0x02,  /* [ 2153] OBJ_id_regCtrl_authenticator */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x01,0x03,  /* [ 2162] OBJ_id_regCtrl_pkiPublicationInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x01,0x04,  /* [ 2171] OBJ_id_regCtrl_pkiArchiveOptions */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x01,0x05,  /* [ 2180] OBJ_id_regCtrl_oldCertID */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x01,0x06,  /* [ 2189] OBJ_id_regCtrl_protocolEncrKey */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x02,0x01,  /* [ 2198] OBJ_id_regInfo_utf8Pairs */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x05,0x02,0x02,  /* [ 2207] OBJ_id_regInfo_certReq */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x06,0x01,       /* [ 2216] OBJ_id_alg_des40 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x06,0x02,       /* [ 2224] OBJ_id_alg_noSignature */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x06,0x03,       /* [ 2232] OBJ_id_alg_dh_sig_hmac_sha1 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x06,0x04,       /* [ 2240] OBJ_id_alg_dh_pop */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x01,       /* [ 2248] OBJ_id_cmc_statusInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x02,       /* [ 2256] OBJ_id_cmc_identification */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x03,       /* [ 2264] OBJ_id_cmc_identityProof */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x04,       /* [ 2272] OBJ_id_cmc_dataReturn */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x05,       /* [ 2280] OBJ_id_cmc_transactionId */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x06,       /* [ 2288] OBJ_id_cmc_senderNonce */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x07,       /* [ 2296] OBJ_id_cmc_recipientNonce */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x08,       /* [ 2304] OBJ_id_cmc_addExtensions */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x09,       /* [ 2312] OBJ_id_cmc_encryptedPOP */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x0A,       /* [ 2320] OBJ_id_cmc_decryptedPOP */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x0B,       /* [ 2328] OBJ_id_cmc_lraPOPWitness */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x0F,       /* [ 2336] OBJ_id_cmc_getCert */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x10,       /* [ 2344] OBJ_id_cmc_getCRL */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x11,       /* [ 2352] OBJ_id_cmc_revokeRequest */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x12,       /* [ 2360] OBJ_id_cmc_regInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x13,       /* [ 2368] OBJ_id_cmc_responseInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x15,       /* [ 2376] OBJ_id_cmc_queryPending */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x16,       /* [ 2384] OBJ_id_cmc_popLinkRandom */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x17,       /* [ 2392] OBJ_id_cmc_popLinkWitness */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x07,0x18,       /* [ 2400] OBJ_id_cmc_confirmCertAcceptance */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x08,0x01,       /* [ 2408] OBJ_id_on_personalData */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x09,0x01,       /* [ 2416] OBJ_id_pda_dateOfBirth */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x09,0x02,       /* [ 2424] OBJ_id_pda_placeOfBirth */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x09,0x03,       /* [ 2432] OBJ_id_pda_gender */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x09,0x04,       /* [ 2440] OBJ_id_pda_countryOfCitizenship */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x09,0x05,       /* [ 2448] OBJ_id_pda_countryOfResidence */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0A,0x01,       /* [ 2456] OBJ_id_aca_authenticationInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0A,0x02,       /* [ 2464] OBJ_id_aca_accessIdentity */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0A,0x03,       /* [ 2472] OBJ_id_aca_chargingIdentity */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0A,0x04,       /* [ 2480] OBJ_id_aca_group */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0A,0x05,       /* [ 2488] OBJ_id_aca_role */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0B,0x01,       /* [ 2496] OBJ_id_qcs_pkixQCSyntax_v1 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0C,0x01,       /* [ 2504] OBJ_id_cct_crs */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0C,0x02,       /* [ 2512] OBJ_id_cct_PKIData */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0C,0x03,       /* [ 2520] OBJ_id_cct_PKIResponse */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x03,       /* [ 2528] OBJ_ad_timeStamping */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x04,       /* [ 2536] OBJ_ad_dvcs */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x01,  /* [ 2544] OBJ_id_pkix_OCSP_basic */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x02,  /* [ 2553] OBJ_id_pkix_OCSP_Nonce */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x03,  /* [ 2562] OBJ_id_pkix_OCSP_CrlID */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x04,  /* [ 2571] OBJ_id_pkix_OCSP_acceptableResponses */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x05,  /* [ 2580] OBJ_id_pkix_OCSP_noCheck */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x06,  /* [ 2589] OBJ_id_pkix_OCSP_archiveCutoff */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x07,  /* [ 2598] OBJ_id_pkix_OCSP_serviceLocator */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x08,  /* [ 2607] OBJ_id_pkix_OCSP_extendedStatus */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x09,  /* [ 2616] OBJ_id_pkix_OCSP_valid */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x0A,  /* [ 2625] OBJ_id_pkix_OCSP_path */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x01,0x0B,  /* [ 2634] OBJ_id_pkix_OCSP_trustRoot */
    0x2B,0x0E,0x03,0x02,                           /* [ 2643] OBJ_algorithm */
    0x2B,0x0E,0x03,0x02,0x0B,                      /* [ 2647] OBJ_rsaSignature */
    0x55,0x08,                                     /* [ 2652] OBJ_X500algorithms */
    0x2B,                                          /* [ 2654] OBJ_org */
    0x2B,0x06,                                     /* [ 2655] OBJ_dod */
    0x2B,0x06,0x01,                                /* [ 2657] OBJ_iana */
    0x2B,0x06,0x01,0x01,                           /* [ 2660] OBJ_Directory */
    0x2B,0x06,0x01,0x02,                           /* [ 2664] OBJ_Management */
    0x2B,0x06,0x01,0x03,                           /* [ 2668] OBJ_Experimental */
    0x2B,0x06,0x01,0x04,                           /* [ 2672] OBJ_Private */
    0x2B,0x06,0x01,0x05,                           /* [ 2676] OBJ_Security */
    0x2B,0x06,0x01,0x06,                           /* [ 2680] OBJ_SNMPv2 */
    0x2B,0x06,0x01,0x07,                           /* [ 2684] OBJ_Mail */
    0x2B,0x06,0x01,0x04,0x01,                      /* [ 2688] OBJ_Enterprises */
    0x2B,0x06,0x01,0x04,0x01,0x8B,0x3A,0x82,0x58,  /* [ 2693] OBJ_dcObject */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x19,  /* [ 2702] OBJ_domainComponent */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x0D,  /* [ 2712] OBJ_Domain */
    0x55,0x01,0x05,                                /* [ 2722] OBJ_selected_attribute_types */
    0x55,0x01,0x05,0x37,                           /* [ 2725] OBJ_clearance */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x0A,       /* [ 2729] OBJ_ac_proxying */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x0B,       /* [ 2737] OBJ_sinfo_access */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0A,0x06,       /* [ 2745] OBJ_id_aca_encAttrs */
    0x55,0x04,0x48,                                /* [ 2753] OBJ_role */
    0x55,0x1D,0x24,                                /* [ 2756] OBJ_policy_constraints */
    0x55,0x1D,0x37,                                /* [ 2759] OBJ_target_information */
    0x55,0x1D,0x38,                                /* [ 2762] OBJ_no_rev_avail */
    0x2A,0x86,0x48,0xCE,0x3D,                      /* [ 2765] OBJ_ansi_X9_62 */
    0x2A,0x86,0x48,0xCE,0x3D,0x01,0x01,            /* [ 2770] OBJ_X9_62_prime_field */
    0x2A,0x86,0x48,0xCE,0x3D,0x01,0x02,            /* [ 2777] OBJ_X9_62_characteristic_two_field */
    0x2A,0x86,0x48,0xCE,0x3D,0x02,0x01,            /* [ 2784] OBJ_X9_62_id_ecPublicKey */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x01,0x01,       /* [ 2791] OBJ_X9_62_prime192v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x01,0x02,       /* [ 2799] OBJ_X9_62_prime192v2 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x01,0x03,       /* [ 2807] OBJ_X9_62_prime192v3 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x01,0x04,       /* [ 2815] OBJ_X9_62_prime239v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x01,0x05,       /* [ 2823] OBJ_X9_62_prime239v2 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x01,0x06,       /* [ 2831] OBJ_X9_62_prime239v3 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x01,0x07,       /* [ 2839] OBJ_X9_62_prime256v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x04,0x01,            /* [ 2847] OBJ_ecdsa_with_SHA1 */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x11,0x01,  /* [ 2854] OBJ_ms_csp_name */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x01,  /* [ 2863] OBJ_aes_128_ecb */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x02,  /* [ 2872] OBJ_aes_128_cbc */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x03,  /* [ 2881] OBJ_aes_128_ofb128 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x04,  /* [ 2890] OBJ_aes_128_cfb128 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x15,  /* [ 2899] OBJ_aes_192_ecb */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x16,  /* [ 2908] OBJ_aes_192_cbc */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x17,  /* [ 2917] OBJ_aes_192_ofb128 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x18,  /* [ 2926] OBJ_aes_192_cfb128 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x29,  /* [ 2935] OBJ_aes_256_ecb */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x2A,  /* [ 2944] OBJ_aes_256_cbc */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x2B,  /* [ 2953] OBJ_aes_256_ofb128 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x2C,  /* [ 2962] OBJ_aes_256_cfb128 */
    0x55,0x1D,0x17,                                /* [ 2971] OBJ_hold_instruction_code */
    0x2A,0x86,0x48,0xCE,0x38,0x02,0x01,            /* [ 2974] OBJ_hold_instruction_none */
    0x2A,0x86,0x48,0xCE,0x38,0x02,0x02,            /* [ 2981] OBJ_hold_instruction_call_issuer */
    0x2A,0x86,0x48,0xCE,0x38,0x02,0x03,            /* [ 2988] OBJ_hold_instruction_reject */
    0x09,                                          /* [ 2995] OBJ_data */
    0x09,0x92,0x26,                                /* [ 2996] OBJ_pss */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,            /* [ 2999] OBJ_ucl */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,       /* [ 3006] OBJ_pilot */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,  /* [ 3014] OBJ_pilotAttributeType */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x03,  /* [ 3023] OBJ_pilotAttributeSyntax */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,  /* [ 3032] OBJ_pilotObjectClass */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x0A,  /* [ 3041] OBJ_pilotGroups */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x03,0x04,  /* [ 3050] OBJ_iA5StringSyntax */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x03,0x05,  /* [ 3060] OBJ_caseIgnoreIA5StringSyntax */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x03,  /* [ 3070] OBJ_pilotObject */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x04,  /* [ 3080] OBJ_pilotPerson */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x05,  /* [ 3090] OBJ_account */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x06,  /* [ 3100] OBJ_document */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x07,  /* [ 3110] OBJ_room */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x09,  /* [ 3120] OBJ_documentSeries */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x0E,  /* [ 3130] OBJ_rFC822localPart */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x0F,  /* [ 3140] OBJ_dNSDomain */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x11,  /* [ 3150] OBJ_domainRelatedObject */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x12,  /* [ 3160] OBJ_friendlyCountry */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x13,  /* [ 3170] OBJ_simpleSecurityObject */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x14,  /* [ 3180] OBJ_pilotOrganization */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x15,  /* [ 3190] OBJ_pilotDSA */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x04,0x16,  /* [ 3200] OBJ_qualityLabelledData */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x01,  /* [ 3210] OBJ_userId */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x02,  /* [ 3220] OBJ_textEncodedORAddress */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x03,  /* [ 3230] OBJ_rfc822Mailbox */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x04,  /* [ 3240] OBJ_info */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x05,  /* [ 3250] OBJ_favouriteDrink */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x06,  /* [ 3260] OBJ_roomNumber */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x07,  /* [ 3270] OBJ_photo */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x08,  /* [ 3280] OBJ_userClass */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x09,  /* [ 3290] OBJ_host */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x0A,  /* [ 3300] OBJ_manager */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x0B,  /* [ 3310] OBJ_documentIdentifier */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x0C,  /* [ 3320] OBJ_documentTitle */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x0D,  /* [ 3330] OBJ_documentVersion */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x0E,  /* [ 3340] OBJ_documentAuthor */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x0F,  /* [ 3350] OBJ_documentLocation */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x14,  /* [ 3360] OBJ_homeTelephoneNumber */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x15,  /* [ 3370] OBJ_secretary */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x16,  /* [ 3380] OBJ_otherMailbox */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x17,  /* [ 3390] OBJ_lastModifiedTime */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x18,  /* [ 3400] OBJ_lastModifiedBy */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x1A,  /* [ 3410] OBJ_aRecord */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x1B,  /* [ 3420] OBJ_pilotAttributeType27 */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x1C,  /* [ 3430] OBJ_mXRecord */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x1D,  /* [ 3440] OBJ_nSRecord */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x1E,  /* [ 3450] OBJ_sOARecord */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x1F,  /* [ 3460] OBJ_cNAMERecord */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x25,  /* [ 3470] OBJ_associatedDomain */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x26,  /* [ 3480] OBJ_associatedName */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x27,  /* [ 3490] OBJ_homePostalAddress */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x28,  /* [ 3500] OBJ_personalTitle */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x29,  /* [ 3510] OBJ_mobileTelephoneNumber */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x2A,  /* [ 3520] OBJ_pagerTelephoneNumber */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x2B,  /* [ 3530] OBJ_friendlyCountryName */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x2D,  /* [ 3540] OBJ_organizationalStatus */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x2E,  /* [ 3550] OBJ_janetMailbox */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x2F,  /* [ 3560] OBJ_mailPreferenceOption */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x30,  /* [ 3570] OBJ_buildingName */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x31,  /* [ 3580] OBJ_dSAQuality */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x32,  /* [ 3590] OBJ_singleLevelQuality */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x33,  /* [ 3600] OBJ_subtreeMinimumQuality */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x34,  /* [ 3610] OBJ_subtreeMaximumQuality */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x35,  /* [ 3620] OBJ_personalSignature */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x36,  /* [ 3630] OBJ_dITRedirect */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x37,  /* [ 3640] OBJ_audio */
    0x09,0x92,0x26,0x89,0x93,0xF2,0x2C,0x64,0x01,0x38,  /* [ 3650] OBJ_documentPublisher */
    0x55,0x04,0x2D,                                /* [ 3660] OBJ_x500UniqueIdentifier */
    0x2B,0x06,0x01,0x07,0x01,                      /* [ 3663] OBJ_mime_mhs */
    0x2B,0x06,0x01,0x07,0x01,0x01,                 /* [ 3668] OBJ_mime_mhs_headings */
    0x2B,0x06,0x01,0x07,0x01,0x02,                 /* [ 3674] OBJ_mime_mhs_bodies */
    0x2B,0x06,0x01,0x07,0x01,0x01,0x01,            /* [ 3680] OBJ_id_hex_partial_message */
    0x2B,0x06,0x01,0x07,0x01,0x01,0x02,            /* [ 3687] OBJ_id_hex_multipart_message */
    0x55,0x04,0x2C,                                /* [ 3694] OBJ_generationQualifier */
    0x55,0x04,0x41,                                /* [ 3697] OBJ_pseudonym */
    0x67,0x2A,                                     /* [ 3700] OBJ_id_set */
    0x67,0x2A,0x00,                                /* [ 3702] OBJ_set_ctype */
    0x67,0x2A,0x01,                                /* [ 3705] OBJ_set_msgExt */
    0x67,0x2A,0x03,                                /* [ 3708] OBJ_set_attr */
    0x67,0x2A,0x05,                                /* [ 3711] OBJ_set_policy */
    0x67,0x2A,0x07,                                /* [ 3714] OBJ_set_certExt */
    0x67,0x2A,0x08,                                /* [ 3717] OBJ_set_brand */
    0x67,0x2A,0x00,0x00,                           /* [ 3720] OBJ_setct_PANData */
    0x67,0x2A,0x00,0x01,                           /* [ 3724] OBJ_setct_PANToken */
    0x67,0x2A,0x00,0x02,                           /* [ 3728] OBJ_setct_PANOnly */
    0x67,0x2A,0x00,0x03,                           /* [ 3732] OBJ_setct_OIData */
    0x67,0x2A,0x00,0x04,                           /* [ 3736] OBJ_setct_PI */
    0x67,0x2A,0x00,0x05,                           /* [ 3740] OBJ_setct_PIData */
    0x67,0x2A,0x00,0x06,                           /* [ 3744] OBJ_setct_PIDataUnsigned */
    0x67,0x2A,0x00,0x07,                           /* [ 3748] OBJ_setct_HODInput */
    0x67,0x2A,0x00,0x08,                           /* [ 3752] OBJ_setct_AuthResBaggage */
    0x67,0x2A,0x00,0x09,                           /* [ 3756] OBJ_setct_AuthRevReqBaggage */
    0x67,0x2A,0x00,0x0A,                           /* [ 3760] OBJ_setct_AuthRevResBaggage */
    0x67,0x2A,0x00,0x0B,                           /* [ 3764] OBJ_setct_CapTokenSeq */
    0x67,0x2A,0x00,0x0C,                           /* [ 3768] OBJ_setct_PInitResData */
    0x67,0x2A,0x00,0x0D,                           /* [ 3772] OBJ_setct_PI_TBS */
    0x67,0x2A,0x00,0x0E,                           /* [ 3776] OBJ_setct_PResData */
    0x67,0x2A,0x00,0x10,                           /* [ 3780] OBJ_setct_AuthReqTBS */
    0x67,0x2A,0x00,0x11,                           /* [ 3784] OBJ_setct_AuthResTBS */
    0x67,0x2A,0x00,0x12,                           /* [ 3788] OBJ_setct_AuthResTBSX */
    0x67,0x2A,0x00,0x13,                           /* [ 3792] OBJ_setct_AuthTokenTBS */
    0x67,0x2A,0x00,0x14,                           /* [ 3796] OBJ_setct_CapTokenData */
    0x67,0x2A,0x00,0x15,                           /* [ 3800] OBJ_setct_CapTokenTBS */
    0x67,0x2A,0x00,0x16,                           /* [ 3804] OBJ_setct_AcqCardCodeMsg */
    0x67,0x2A,0x00,0x17,                           /* [ 3808] OBJ_setct_AuthRevReqTBS */
    0x67,0x2A,0x00,0x18,                           /* [ 3812] OBJ_setct_AuthRevResData */
    0x67,0x2A,0x00,0x19,                           /* [ 3816] OBJ_setct_AuthRevResTBS */
    0x67,0x2A,0x00,0x1A,                           /* [ 3820] OBJ_setct_CapReqTBS */
    0x67,0x2A,0x00,0x1B,                           /* [ 3824] OBJ_setct_CapReqTBSX */
    0x67,0x2A,0x00,0x1C,                           /* [ 3828] OBJ_setct_CapResData */
    0x67,0x2A,0x00,0x1D,                           /* [ 3832] OBJ_setct_CapRevReqTBS */
    0x67,0x2A,0x00,0x1E,                           /* [ 3836] OBJ_setct_CapRevReqTBSX */
    0x67,0x2A,0x00,0x1F,                           /* [ 3840] OBJ_setct_CapRevResData */
    0x67,0x2A,0x00,0x20,                           /* [ 3844] OBJ_setct_CredReqTBS */
    0x67,0x2A,0x00,0x21,                           /* [ 3848] OBJ_setct_CredReqTBSX */
    0x67,0x2A,0x00,0x22,                           /* [ 3852] OBJ_setct_CredResData */
    0x67,0x2A,0x00,0x23,                           /* [ 3856] OBJ_setct_CredRevReqTBS */
    0x67,0x2A,0x00,0x24,                           /* [ 3860] OBJ_setct_CredRevReqTBSX */
    0x67,0x2A,0x00,0x25,                           /* [ 3864] OBJ_setct_CredRevResData */
    0x67,0x2A,0x00,0x26,                           /* [ 3868] OBJ_setct_PCertReqData */
    0x67,0x2A,0x00,0x27,                           /* [ 3872] OBJ_setct_PCertResTBS */
    0x67,0x2A,0x00,0x28,                           /* [ 3876] OBJ_setct_BatchAdminReqData */
    0x67,0x2A,0x00,0x29,                           /* [ 3880] OBJ_setct_BatchAdminResData */
    0x67,0x2A,0x00,0x2A,                           /* [ 3884] OBJ_setct_CardCInitResTBS */
    0x67,0x2A,0x00,0x2B,                           /* [ 3888] OBJ_setct_MeAqCInitResTBS */
    0x67,0x2A,0x00,0x2C,                           /* [ 3892] OBJ_setct_RegFormResTBS */
    0x67,0x2A,0x00,0x2D,                           /* [ 3896] OBJ_setct_CertReqData */
    0x67,0x2A,0x00,0x2E,                           /* [ 3900] OBJ_setct_CertReqTBS */
    0x67,0x2A,0x00,0x2F,                           /* [ 3904] OBJ_setct_CertResData */
    0x67,0x2A,0x00,0x30,                           /* [ 3908] OBJ_setct_CertInqReqTBS */
    0x67,0x2A,0x00,0x31,                           /* [ 3912] OBJ_setct_ErrorTBS */
    0x67,0x2A,0x00,0x32,                           /* [ 3916] OBJ_setct_PIDualSignedTBE */
    0x67,0x2A,0x00,0x33,                           /* [ 3920] OBJ_setct_PIUnsignedTBE */
    0x67,0x2A,0x00,0x34,                           /* [ 3924] OBJ_setct_AuthReqTBE */
    0x67,0x2A,0x00,0x35,                           /* [ 3928] OBJ_setct_AuthResTBE */
    0x67,0x2A,0x00,0x36,                           /* [ 3932] OBJ_setct_AuthResTBEX */
    0x67,0x2A,0x00,0x37,                           /* [ 3936] OBJ_setct_AuthTokenTBE */
    0x67,0x2A,0x00,0x38,                           /* [ 3940] OBJ_setct_CapTokenTBE */
    0x67,0x2A,0x00,0x39,                           /* [ 3944] OBJ_setct_CapTokenTBEX */
    0x67,0x2A,0x00,0x3A,                           /* [ 3948] OBJ_setct_AcqCardCodeMsgTBE */
    0x67,0x2A,0x00,0x3B,                           /* [ 3952] OBJ_setct_AuthRevReqTBE */
    0x67,0x2A,0x00,0x3C,                           /* [ 3956] OBJ_setct_AuthRevResTBE */
    0x67,0x2A,0x00,0x3D,                           /* [ 3960] OBJ_setct_AuthRevResTBEB */
    0x67,0x2A,0x00,0x3E,                           /* [ 3964] OBJ_setct_CapReqTBE */
    0x67,0x2A,0x00,0x3F,                           /* [ 3968] OBJ_setct_CapReqTBEX */
    0x67,0x2A,0x00,0x40,                           /* [ 3972] OBJ_setct_CapResTBE */
    0x67,0x2A,0x00,0x41,                           /* [ 3976] OBJ_setct_CapRevReqTBE */
    0x67,0x2A,0x00,0x42,                           /* [ 3980] OBJ_setct_CapRevReqTBEX */
    0x67,0x2A,0x00,0x43,                           /* [ 3984] OBJ_setct_CapRevResTBE */
    0x67,0x2A,0x00,0x44,                           /* [ 3988] OBJ_setct_CredReqTBE */
    0x67,0x2A,0x00,0x45,                           /* [ 3992] OBJ_setct_CredReqTBEX */
    0x67,0x2A,0x00,0x46,                           /* [ 3996] OBJ_setct_CredResTBE */
    0x67,0x2A,0x00,0x47,                           /* [ 4000] OBJ_setct_CredRevReqTBE */
    0x67,0x2A,0x00,0x48,                           /* [ 4004] OBJ_setct_CredRevReqTBEX */
    0x67,0x2A,0x00,0x49,                           /* [ 4008] OBJ_setct_CredRevResTBE */
    0x67,0x2A,0x00,0x4A,                           /* [ 4012] OBJ_setct_BatchAdminReqTBE */
    0x67,0x2A,0x00,0x4B,                           /* [ 4016] OBJ_setct_BatchAdminResTBE */
    0x67,0x2A,0x00,0x4C,                           /* [ 4020] OBJ_setct_RegFormReqTBE */
    0x67,0x2A,0x00,0x4D,                           /* [ 4024] OBJ_setct_CertReqTBE */
    0x67,0x2A,0x00,0x4E,                           /* [ 4028] OBJ_setct_CertReqTBEX */
    0x67,0x2A,0x00,0x4F,                           /* [ 4032] OBJ_setct_CertResTBE */
    0x67,0x2A,0x00,0x50,                           /* [ 4036] OBJ_setct_CRLNotificationTBS */
    0x67,0x2A,0x00,0x51,                           /* [ 4040] OBJ_setct_CRLNotificationResTBS */
    0x67,0x2A,0x00,0x52,                           /* [ 4044] OBJ_setct_BCIDistributionTBS */
    0x67,0x2A,0x01,0x01,                           /* [ 4048] OBJ_setext_genCrypt */
    0x67,0x2A,0x01,0x03,                           /* [ 4052] OBJ_setext_miAuth */
    0x67,0x2A,0x01,0x04,                           /* [ 4056] OBJ_setext_pinSecure */
    0x67,0x2A,0x01,0x05,                           /* [ 4060] OBJ_setext_pinAny */
    0x67,0x2A,0x01,0x07,                           /* [ 4064] OBJ_setext_track2 */
    0x67,0x2A,0x01,0x08,                           /* [ 4068] OBJ_setext_cv */
    0x67,0x2A,0x05,0x00,                           /* [ 4072] OBJ_set_policy_root */
    0x67,0x2A,0x07,0x00,                           /* [ 4076] OBJ_setCext_hashedRoot */
    0x67,0x2A,0x07,0x01,                           /* [ 4080] OBJ_setCext_certType */
    0x67,0x2A,0x07,0x02,                           /* [ 4084] OBJ_setCext_merchData */
    0x67,0x2A,0x07,0x03,                           /* [ 4088] OBJ_setCext_cCertRequired */
    0x67,0x2A,0x07,0x04,                           /* [ 4092] OBJ_setCext_tunneling */
    0x67,0x2A,0x07,0x05,                           /* [ 4096] OBJ_setCext_setExt */
    0x67,0x2A,0x07,0x06,                           /* [ 4100] OBJ_setCext_setQualf */
    0x67,0x2A,0x07,0x07,                           /* [ 4104] OBJ_setCext_PGWYcapabilities */
    0x67,0x2A,0x07,0x08,                           /* [ 4108] OBJ_setCext_TokenIdentifier */
    0x67,0x2A,0x07,0x09,                           /* [ 4112] OBJ_setCext_Track2Data */
    0x67,0x2A,0x07,0x0A,                           /* [ 4116] OBJ_setCext_TokenType */
    0x67,0x2A,0x07,0x0B,                           /* [ 4120] OBJ_setCext_IssuerCapabilities */
    0x67,0x2A,0x03,0x00,                           /* [ 4124] OBJ_setAttr_Cert */
    0x67,0x2A,0x03,0x01,                           /* [ 4128] OBJ_setAttr_PGWYcap */
    0x67,0x2A,0x03,0x02,                           /* [ 4132] OBJ_setAttr_TokenType */
    0x67,0x2A,0x03,0x03,                           /* [ 4136] OBJ_setAttr_IssCap */
    0x67,0x2A,0x03,0x00,0x00,                      /* [ 4140] OBJ_set_rootKeyThumb */
    0x67,0x2A,0x03,0x00,0x01,                      /* [ 4145] OBJ_set_addPolicy */
    0x67,0x2A,0x03,0x02,0x01,                      /* [ 4150] OBJ_setAttr_Token_EMV */
    0x67,0x2A,0x03,0x02,0x02,                      /* [ 4155] OBJ_setAttr_Token_B0Prime */
    0x67,0x2A,0x03,0x03,0x03,                      /* [ 4160] OBJ_setAttr_IssCap_CVM */
    0x67,0x2A,0x03,0x03,0x04,                      /* [ 4165] OBJ_setAttr_IssCap_T2 */
    0x67,0x2A,0x03,0x03,0x05,                      /* [ 4170] OBJ_setAttr_IssCap_Sig */
    0x67,0x2A,0x03,0x03,0x03,0x01,                 /* [ 4175] OBJ_setAttr_GenCryptgrm */
    0x67,0x2A,0x03,0x03,0x04,0x01,                 /* [ 4181] OBJ_setAttr_T2Enc */
    0x67,0x2A,0x03,0x03,0x04,0x02,                 /* [ 4187] OBJ_setAttr_T2cleartxt */
    0x67,0x2A,0x03,0x03,0x05,0x01,                 /* [ 4193] OBJ_setAttr_TokICCsig */
    0x67,0x2A,0x03,0x03,0x05,0x02,                 /* [ 4199] OBJ_setAttr_SecDevSig */
    0x67,0x2A,0x08,0x01,                           /* [ 4205] OBJ_set_brand_IATA_ATA */
    0x67,0x2A,0x08,0x1E,                           /* [ 4209] OBJ_set_brand_Diners */
    0x67,0x2A,0x08,0x22,                           /* [ 4213] OBJ_set_brand_AmericanExpress */
    0x67,0x2A,0x08,0x23,                           /* [ 4217] OBJ_set_brand_JCB */
    0x67,0x2A,0x08,0x04,                           /* [ 4221] OBJ_set_brand_Visa */
    0x67,0x2A,0x08,0x05,                           /* [ 4225] OBJ_set_brand_MasterCard */
    0x67,0x2A,0x08,0xAE,0x7B,                      /* [ 4229] OBJ_set_brand_Novus */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x03,0x0A,       /* [ 4234] OBJ_des_cdmf */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x06,  /* [ 4242] OBJ_rsaOAEPEncryptionSET */
    0x67,                                          /* [ 4251] OBJ_international_organizations */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x14,0x02,0x02,  /* [ 4252] OBJ_ms_smartcard_login */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x14,0x02,0x03,  /* [ 4262] OBJ_ms_upn */
    0x55,0x04,0x09,                                /* [ 4272] OBJ_streetAddress */
    0x55,0x04,0x11,                                /* [ 4275] OBJ_postalCode */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x15,            /* [ 4278] OBJ_id_ppl */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x0E,       /* [ 4285] OBJ_proxyCertInfo */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x15,0x00,       /* [ 4293] OBJ_id_ppl_anyLanguage */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x15,0x01,       /* [ 4301] OBJ_id_ppl_inheritAll */
    0x55,0x1D,0x1E,                                /* [ 4309] OBJ_name_constraints */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x15,0x02,       /* [ 4312] OBJ_Independent */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x0B,  /* [ 4320] OBJ_sha256WithRSAEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x0C,  /* [ 4329] OBJ_sha384WithRSAEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x0D,  /* [ 4338] OBJ_sha512WithRSAEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x0E,  /* [ 4347] OBJ_sha224WithRSAEncryption */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x01,  /* [ 4356] OBJ_sha256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x02,  /* [ 4365] OBJ_sha384 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x03,  /* [ 4374] OBJ_sha512 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x04,  /* [ 4383] OBJ_sha224 */
    0x2B,                                          /* [ 4392] OBJ_identified_organization */
    0x2B,0x81,0x04,                                /* [ 4393] OBJ_certicom_arc */
    0x67,0x2B,                                     /* [ 4396] OBJ_wap */
    0x67,0x2B,0x01,                                /* [ 4398] OBJ_wap_wsg */
    0x2A,0x86,0x48,0xCE,0x3D,0x01,0x02,0x03,       /* [ 4401] OBJ_X9_62_id_characteristic_two_basis */
    0x2A,0x86,0x48,0xCE,0x3D,0x01,0x02,0x03,0x01,  /* [ 4409] OBJ_X9_62_onBasis */
    0x2A,0x86,0x48,0xCE,0x3D,0x01,0x02,0x03,0x02,  /* [ 4418] OBJ_X9_62_tpBasis */
    0x2A,0x86,0x48,0xCE,0x3D,0x01,0x02,0x03,0x03,  /* [ 4427] OBJ_X9_62_ppBasis */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x01,       /* [ 4436] OBJ_X9_62_c2pnb163v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x02,       /* [ 4444] OBJ_X9_62_c2pnb163v2 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x03,       /* [ 4452] OBJ_X9_62_c2pnb163v3 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x04,       /* [ 4460] OBJ_X9_62_c2pnb176v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x05,       /* [ 4468] OBJ_X9_62_c2tnb191v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x06,       /* [ 4476] OBJ_X9_62_c2tnb191v2 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x07,       /* [ 4484] OBJ_X9_62_c2tnb191v3 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x08,       /* [ 4492] OBJ_X9_62_c2onb191v4 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x09,       /* [ 4500] OBJ_X9_62_c2onb191v5 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x0A,       /* [ 4508] OBJ_X9_62_c2pnb208w1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x0B,       /* [ 4516] OBJ_X9_62_c2tnb239v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x0C,       /* [ 4524] OBJ_X9_62_c2tnb239v2 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x0D,       /* [ 4532] OBJ_X9_62_c2tnb239v3 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x0E,       /* [ 4540] OBJ_X9_62_c2onb239v4 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x0F,       /* [ 4548] OBJ_X9_62_c2onb239v5 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x10,       /* [ 4556] OBJ_X9_62_c2pnb272w1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x11,       /* [ 4564] OBJ_X9_62_c2pnb304w1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x12,       /* [ 4572] OBJ_X9_62_c2tnb359v1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x13,       /* [ 4580] OBJ_X9_62_c2pnb368w1 */
    0x2A,0x86,0x48,0xCE,0x3D,0x03,0x00,0x14,       /* [ 4588] OBJ_X9_62_c2tnb431r1 */
    0x2B,0x81,0x04,0x00,0x06,                      /* [ 4596] OBJ_secp112r1 */
    0x2B,0x81,0x04,0x00,0x07,                      /* [ 4601] OBJ_secp112r2 */
    0x2B,0x81,0x04,0x00,0x1C,                      /* [ 4606] OBJ_secp128r1 */
    0x2B,0x81,0x04,0x00,0x1D,                      /* [ 4611] OBJ_secp128r2 */
    0x2B,0x81,0x04,0x00,0x09,                      /* [ 4616] OBJ_secp160k1 */
    0x2B,0x81,0x04,0x00,0x08,                      /* [ 4621] OBJ_secp160r1 */
    0x2B,0x81,0x04,0x00,0x1E,                      /* [ 4626] OBJ_secp160r2 */
    0x2B,0x81,0x04,0x00,0x1F,                      /* [ 4631] OBJ_secp192k1 */
    0x2B,0x81,0x04,0x00,0x20,                      /* [ 4636] OBJ_secp224k1 */
    0x2B,0x81,0x04,0x00,0x21,                      /* [ 4641] OBJ_secp224r1 */
    0x2B,0x81,0x04,0x00,0x0A,                      /* [ 4646] OBJ_secp256k1 */
    0x2B,0x81,0x04,0x00,0x22,                      /* [ 4651] OBJ_secp384r1 */
    0x2B,0x81,0x04,0x00,0x23,                      /* [ 4656] OBJ_secp521r1 */
    0x2B,0x81,0x04,0x00,0x04,                      /* [ 4661] OBJ_sect113r1 */
    0x2B,0x81,0x04,0x00,0x05,                      /* [ 4666] OBJ_sect113r2 */
    0x2B,0x81,0x04,0x00,0x16,                      /* [ 4671] OBJ_sect131r1 */
    0x2B,0x81,0x04,0x00,0x17,                      /* [ 4676] OBJ_sect131r2 */
    0x2B,0x81,0x04,0x00,0x01,                      /* [ 4681] OBJ_sect163k1 */
    0x2B,0x81,0x04,0x00,0x02,                      /* [ 4686] OBJ_sect163r1 */
    0x2B,0x81,0x04,0x00,0x0F,                      /* [ 4691] OBJ_sect163r2 */
    0x2B,0x81,0x04,0x00,0x18,                      /* [ 4696] OBJ_sect193r1 */
    0x2B,0x81,0x04,0x00,0x19,                      /* [ 4701] OBJ_sect193r2 */
    0x2B,0x81,0x04,0x00,0x1A,                      /* [ 4706] OBJ_sect233k1 */
    0x2B,0x81,0x04,0x00,0x1B,                      /* [ 4711] OBJ_sect233r1 */
    0x2B,0x81,0x04,0x00,0x03,                      /* [ 4716] OBJ_sect239k1 */
    0x2B,0x81,0x04,0x00,0x10,                      /* [ 4721] OBJ_sect283k1 */
    0x2B,0x81,0x04,0x00,0x11,                      /* [ 4726] OBJ_sect283r1 */
    0x2B,0x81,0x04,0x00,0x24,                      /* [ 4731] OBJ_sect409k1 */
    0x2B,0x81,0x04,0x00,0x25,                      /* [ 4736] OBJ_sect409r1 */
    0x2B,0x81,0x04,0x00,0x26,                      /* [ 4741] OBJ_sect571k1 */
    0x2B,0x81,0x04,0x00,0x27,                      /* [ 4746] OBJ_sect571r1 */
    0x67,0x2B,0x01,0x04,0x01,                      /* [ 4751] OBJ_wap_wsg_idm_ecid_wtls1 */
    0x67,0x2B,0x01,0x04,0x03,                      /* [ 4756] OBJ_wap_wsg_idm_ecid_wtls3 */
    0x67,0x2B,0x01,0x04,0x04,                      /* [ 4761] OBJ_wap_wsg_idm_ecid_wtls4 */
    0x67,0x2B,0x01,0x04,0x05,                      /* [ 4766] OBJ_wap_wsg_idm_ecid_wtls5 */
    0x67,0x2B,0x01,0x04,0x06,                      /* [ 4771] OBJ_wap_wsg_idm_ecid_wtls6 */
    0x67,0x2B,0x01,0x04,0x07,                      /* [ 4776] OBJ_wap_wsg_idm_ecid_wtls7 */
    0x67,0x2B,0x01,0x04,0x08,                      /* [ 4781] OBJ_wap_wsg_idm_ecid_wtls8 */
    0x67,0x2B,0x01,0x04,0x09,                      /* [ 4786] OBJ_wap_wsg_idm_ecid_wtls9 */
    0x67,0x2B,0x01,0x04,0x0A,                      /* [ 4791] OBJ_wap_wsg_idm_ecid_wtls10 */
    0x67,0x2B,0x01,0x04,0x0B,                      /* [ 4796] OBJ_wap_wsg_idm_ecid_wtls11 */
    0x67,0x2B,0x01,0x04,0x0C,                      /* [ 4801] OBJ_wap_wsg_idm_ecid_wtls12 */
    0x55,0x1D,0x20,0x00,                           /* [ 4806] OBJ_any_policy */
    0x55,0x1D,0x21,                                /* [ 4810] OBJ_policy_mappings */
    0x55,0x1D,0x36,                                /* [ 4813] OBJ_inhibit_any_policy */
    0x55,0x1D,0x09,                                /* [ 4816] OBJ_subject_directory_attributes */
    0x55,0x1D,0x1C,                                /* [ 4819] OBJ_issuing_distribution_point */
    0x55,0x1D,0x1D,                                /* [ 4822] OBJ_certificate_issuer */
    0x2B,0x06,0x01,0x05,0x05,0x08,0x01,0x01,       /* [ 4825] OBJ_hmac_md5 */
    0x2B,0x06,0x01,0x05,0x05,0x08,0x01,0x02,       /* [ 4833] OBJ_hmac_sha1 */
    0x2A,0x86,0x48,0x86,0xF6,0x7D,0x07,0x42,0x0D,  /* [ 4841] OBJ_id_PasswordBasedMAC */
    0x2A,0x86,0x48,0x86,0xF6,0x7D,0x07,0x42,0x1E,  /* [ 4850] OBJ_id_DHBasedMac */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x10,       /* [ 4859] OBJ_id_it_suppLangTags */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x05,       /* [ 4867] OBJ_caRepository */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x09,  /* [ 4875] OBJ_id_smime_ct_compressedData */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x1B,  /* [ 4886] OBJ_id_ct_asciiTextWithCRLF */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x05,  /* [ 4897] OBJ_id_aes128_wrap */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x19,  /* [ 4906] OBJ_id_aes192_wrap */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x2D,  /* [ 4915] OBJ_id_aes256_wrap */
    0x2A,0x86,0x48,0xCE,0x3D,0x04,0x02,            /* [ 4924] OBJ_ecdsa_with_Recommended */
    0x2A,0x86,0x48,0xCE,0x3D,0x04,0x03,            /* [ 4931] OBJ_ecdsa_with_Specified */
    0x2A,0x86,0x48,0xCE,0x3D,0x04,0x03,0x01,       /* [ 4938] OBJ_ecdsa_with_SHA224 */
    0x2A,0x86,0x48,0xCE,0x3D,0x04,0x03,0x02,       /* [ 4946] OBJ_ecdsa_with_SHA256 */
    0x2A,0x86,0x48,0xCE,0x3D,0x04,0x03,0x03,       /* [ 4954] OBJ_ecdsa_with_SHA384 */
    0x2A,0x86,0x48,0xCE,0x3D,0x04,0x03,0x04,       /* [ 4962] OBJ_ecdsa_with_SHA512 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x06,       /* [ 4970] OBJ_hmacWithMD5 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x08,       /* [ 4978] OBJ_hmacWithSHA224 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x09,       /* [ 4986] OBJ_hmacWithSHA256 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x0A,       /* [ 4994] OBJ_hmacWithSHA384 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x0B,       /* [ 5002] OBJ_hmacWithSHA512 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x01,  /* [ 5010] OBJ_dsa_with_SHA224 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x02,  /* [ 5019] OBJ_dsa_with_SHA256 */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x11,0x02,  /* [ 5028] OBJ_LocalKeySet */
    0x55,0x1D,0x2E,                                /* [ 5037] OBJ_freshest_crl */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x08,0x03,       /* [ 5040] OBJ_id_on_permanentIdentifier */
    0x55,0x04,0x0E,                                /* [ 5048] OBJ_searchGuide */
    0x55,0x04,0x0F,                                /* [ 5051] OBJ_businessCategory */
    0x55,0x04,0x10,                                /* [ 5054] OBJ_postalAddress */
    0x55,0x04,0x12,                                /* [ 5057] OBJ_postOfficeBox */
    0x55,0x04,0x13,                                /* [ 5060] OBJ_physicalDeliveryOfficeName */
    0x55,0x04,0x14,                                /* [ 5063] OBJ_telephoneNumber */
    0x55,0x04,0x15,                                /* [ 5066] OBJ_telexNumber */
    0x55,0x04,0x16,                                /* [ 5069] OBJ_teletexTerminalIdentifier */
    0x55,0x04,0x17,                                /* [ 5072] OBJ_facsimileTelephoneNumber */
    0x55,0x04,0x18,                                /* [ 5075] OBJ_x121Address */
    0x55,0x04,0x19,                                /* [ 5078] OBJ_internationaliSDNNumber */
    0x55,0x04,0x1A,                                /* [ 5081] OBJ_registeredAddress */
    0x55,0x04,0x1B,                                /* [ 5084] OBJ_destinationIndicator */
    0x55,0x04,0x1C,                                /* [ 5087] OBJ_preferredDeliveryMethod */
    0x55,0x04,0x1D,                                /* [ 5090] OBJ_presentationAddress */
    0x55,0x04,0x1E,                                /* [ 5093] OBJ_supportedApplicationContext */
    0x55,0x04,0x1F,                                /* [ 5096] OBJ_member */
    0x55,0x04,0x20,                                /* [ 5099] OBJ_owner */
    0x55,0x04,0x21,                                /* [ 5102] OBJ_roleOccupant */
    0x55,0x04,0x22,                                /* [ 5105] OBJ_seeAlso */
    0x55,0x04,0x23,                                /* [ 5108] OBJ_userPassword */
    0x55,0x04,0x24,                                /* [ 5111] OBJ_userCertificate */
    0x55,0x04,0x25,                                /* [ 5114] OBJ_cACertificate */
    0x55,0x04,0x26,                                /* [ 5117] OBJ_authorityRevocationList */
    0x55,0x04,0x27,                                /* [ 5120] OBJ_certificateRevocationList */
    0x55,0x04,0x28,                                /* [ 5123] OBJ_crossCertificatePair */
    0x55,0x04,0x2F,                                /* [ 5126] OBJ_enhancedSearchGuide */
    0x55,0x04,0x30,                                /* [ 5129] OBJ_protocolInformation */
    0x55,0x04,0x31,                                /* [ 5132] OBJ_distinguishedName */
    0x55,0x04,0x32,                                /* [ 5135] OBJ_uniqueMember */
    0x55,0x04,0x33,                                /* [ 5138] OBJ_houseIdentifier */
    0x55,0x04,0x34,                                /* [ 5141] OBJ_supportedAlgorithms */
    0x55,0x04,0x35,                                /* [ 5144] OBJ_deltaRevocationList */
    0x55,0x04,0x36,                                /* [ 5147] OBJ_dmdName */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x03,0x09,  /* [ 5150] OBJ_id_alg_PWRI_KEK */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x06,  /* [ 5161] OBJ_aes_128_gcm */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x07,  /* [ 5170] OBJ_aes_128_ccm */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x08,  /* [ 5179] OBJ_id_aes128_wrap_pad */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x1A,  /* [ 5188] OBJ_aes_192_gcm */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x1B,  /* [ 5197] OBJ_aes_192_ccm */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x1C,  /* [ 5206] OBJ_id_aes192_wrap_pad */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x2E,  /* [ 5215] OBJ_aes_256_gcm */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x2F,  /* [ 5224] OBJ_aes_256_ccm */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x01,0x30,  /* [ 5233] OBJ_id_aes256_wrap_pad */
    0x55,0x1D,0x25,0x00,                           /* [ 5242] OBJ_anyExtendedKeyUsage */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x08,  /* [ 5246] OBJ_mgf1 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x0A,  /* [ 5255] OBJ_rsassaPss */
    0x2B,0x6F,0x02,0x8C,0x53,0x00,0x01,0x01,       /* [ 5264] OBJ_aes_128_xts */
    0x2B,0x6F,0x02,0x8C,0x53,0x00,0x01,0x02,       /* [ 5272] OBJ_aes_256_xts */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x07,  /* [ 5280] OBJ_rsaesOaep */
    0x2A,0x86,0x48,0xCE,0x3E,0x02,0x01,            /* [ 5289] OBJ_dhpublicnumber */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x01,  /* [ 5296] OBJ_brainpoolP160r1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x02,  /* [ 5305] OBJ_brainpoolP160t1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x03,  /* [ 5314] OBJ_brainpoolP192r1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x04,  /* [ 5323] OBJ_brainpoolP192t1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x05,  /* [ 5332] OBJ_brainpoolP224r1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x06,  /* [ 5341] OBJ_brainpoolP224t1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x07,  /* [ 5350] OBJ_brainpoolP256r1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x08,  /* [ 5359] OBJ_brainpoolP256t1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x09,  /* [ 5368] OBJ_brainpoolP320r1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x0A,  /* [ 5377] OBJ_brainpoolP320t1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x0B,  /* [ 5386] OBJ_brainpoolP384r1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x0C,  /* [ 5395] OBJ_brainpoolP384t1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x0D,  /* [ 5404] OBJ_brainpoolP512r1 */
    0x2B,0x24,0x03,0x03,0x02,0x08,0x01,0x01,0x0E,  /* [ 5413] OBJ_brainpoolP512t1 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x09,  /* [ 5422] OBJ_pSpecified */
    0x2B,0x81,0x05,0x10,0x86,0x48,0x3F,0x00,0x02,  /* [ 5431] OBJ_dhSinglePass_stdDH_sha1kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0B,0x00,                 /* [ 5440] OBJ_dhSinglePass_stdDH_sha224kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0B,0x01,                 /* [ 5446] OBJ_dhSinglePass_stdDH_sha256kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0B,0x02,                 /* [ 5452] OBJ_dhSinglePass_stdDH_sha384kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0B,0x03,                 /* [ 5458] OBJ_dhSinglePass_stdDH_sha512kdf_scheme */
    0x2B,0x81,0x05,0x10,0x86,0x48,0x3F,0x00,0x03,  /* [ 5464] OBJ_dhSinglePass_cofactorDH_sha1kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0E,0x00,                 /* [ 5473] OBJ_dhSinglePass_cofactorDH_sha224kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0E,0x01,                 /* [ 5479] OBJ_dhSinglePass_cofactorDH_sha256kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0E,0x02,                 /* [ 5485] OBJ_dhSinglePass_cofactorDH_sha384kdf_scheme */
    0x2B,0x81,0x04,0x01,0x0E,0x03,                 /* [ 5491] OBJ_dhSinglePass_cofactorDH_sha512kdf_scheme */
    0x2B,0x06,0x01,0x04,0x01,0xD6,0x79,0x02,0x04,0x02,  /* [ 5497] OBJ_ct_precert_scts */
    0x2B,0x06,0x01,0x04,0x01,0xD6,0x79,0x02,0x04,0x03,  /* [ 5507] OBJ_ct_precert_poison */
    0x2B,0x06,0x01,0x04,0x01,0xD6,0x79,0x02,0x04,0x04,  /* [ 5517] OBJ_ct_precert_signer */
    0x2B,0x06,0x01,0x04,0x01,0xD6,0x79,0x02,0x04,0x05,  /* [ 5527] OBJ_ct_cert_scts */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x3C,0x02,0x01,0x01,  /* [ 5537] OBJ_jurisdictionLocalityName */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x3C,0x02,0x01,0x02,  /* [ 5548] OBJ_jurisdictionStateOrProvinceName */
    0x2B,0x06,0x01,0x04,0x01,0x82,0x37,0x3C,0x02,0x01,0x03,  /* [ 5559] OBJ_jurisdictionCountryName */
    0x2B,0x06,0x01,0x04,0x01,0xDA,0x47,0x04,0x0B,  /* [ 5570] OBJ_id_scrypt */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x18,       /* [ 5579] OBJ_tlsfeature */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x11,       /* [ 5587] OBJ_ipsec_IKE */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x12,       /* [ 5595] OBJ_capwapAC */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x13,       /* [ 5603] OBJ_capwapWTP */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x15,       /* [ 5611] OBJ_sshClient */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x16,       /* [ 5619] OBJ_sshServer */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x17,       /* [ 5627] OBJ_sendRouter */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x18,       /* [ 5635] OBJ_sendProxiedRouter */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x19,       /* [ 5643] OBJ_sendOwner */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x1A,       /* [ 5651] OBJ_sendProxiedOwner */
    0x2B,0x06,0x01,0x05,0x02,0x03,                 /* [ 5659] OBJ_id_pkinit */
    0x2B,0x06,0x01,0x05,0x02,0x03,0x04,            /* [ 5665] OBJ_pkInitClientAuth */
    0x2B,0x06,0x01,0x05,0x02,0x03,0x05,            /* [ 5672] OBJ_pkInitKDC */
    0x2B,0x65,0x6E,                                /* [ 5679] OBJ_X25519 */
    0x2B,0x65,0x6F,                                /* [ 5682] OBJ_X448 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x13,  /* [ 5685] OBJ_id_smime_ct_contentCollection */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x17,  /* [ 5696] OBJ_id_smime_ct_authEnvelopedData */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x1C,  /* [ 5707] OBJ_id_ct_xml */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x02,0x2F,  /* [ 5718] OBJ_id_smime_aa_signingCertificateV2 */
    0x2B,0x65,0x70,                                /* [ 5729] OBJ_ED25519 */
    0x2B,0x65,0x71,                                /* [ 5732] OBJ_ED448 */
    0x55,0x04,0x61,                                /* [ 5735] OBJ_organizationIdentifier */
    0x55,0x04,0x62,                                /* [ 5738] OBJ_countryCode3c */
    0x55,0x04,0x63,                                /* [ 5741] OBJ_countryCode3n */
    0x55,0x04,0x64,                                /* [ 5744] OBJ_dnsName */
    0x2B,0x24,0x08,0x03,0x03,                      /* [ 5747] OBJ_x509ExtAdmission */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x05,  /* [ 5752] OBJ_sha512_224 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x06,  /* [ 5761] OBJ_sha512_256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x07,  /* [ 5770] OBJ_sha3_224 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x08,  /* [ 5779] OBJ_sha3_256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x09,  /* [ 5788] OBJ_sha3_384 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x0A,  /* [ 5797] OBJ_sha3_512 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x0B,  /* [ 5806] OBJ_shake128 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x0C,  /* [ 5815] OBJ_shake256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x0D,  /* [ 5824] OBJ_hmac_sha3_224 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x0E,  /* [ 5833] OBJ_hmac_sha3_256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x0F,  /* [ 5842] OBJ_hmac_sha3_384 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x10,  /* [ 5851] OBJ_hmac_sha3_512 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x03,  /* [ 5860] OBJ_dsa_with_SHA384 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x04,  /* [ 5869] OBJ_dsa_with_SHA512 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x05,  /* [ 5878] OBJ_dsa_with_SHA3_224 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x06,  /* [ 5887] OBJ_dsa_with_SHA3_256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x07,  /* [ 5896] OBJ_dsa_with_SHA3_384 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x08,  /* [ 5905] OBJ_dsa_with_SHA3_512 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x09,  /* [ 5914] OBJ_ecdsa_with_SHA3_224 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x0A,  /* [ 5923] OBJ_ecdsa_with_SHA3_256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x0B,  /* [ 5932] OBJ_ecdsa_with_SHA3_384 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x0C,  /* [ 5941] OBJ_ecdsa_with_SHA3_512 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x0D,  /* [ 5950] OBJ_RSA_SHA3_224 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x0E,  /* [ 5959] OBJ_RSA_SHA3_256 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x0F,  /* [ 5968] OBJ_RSA_SHA3_384 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x03,0x10,  /* [ 5977] OBJ_RSA_SHA3_512 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x1B,       /* [ 5986] OBJ_cmcCA */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x1C,       /* [ 5994] OBJ_cmcRA */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x01,       /* [ 6002] OBJ_sm4_ecb */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x02,       /* [ 6010] OBJ_sm4_cbc */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x03,       /* [ 6018] OBJ_sm4_ofb128 */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x05,       /* [ 6026] OBJ_sm4_cfb1 */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x04,       /* [ 6034] OBJ_sm4_cfb128 */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x06,       /* [ 6042] OBJ_sm4_cfb8 */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x07,       /* [ 6050] OBJ_sm4_ctr */
    0x2A,0x81,0x1C,                                /* [ 6058] OBJ_ISO_CN */
    0x2A,0x81,0x1C,0xCF,0x55,                      /* [ 6061] OBJ_oscca */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,                 /* [ 6066] OBJ_sm_scheme */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x83,0x11,       /* [ 6072] OBJ_sm3 */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x83,0x78,       /* [ 6080] OBJ_sm3WithRSAEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x0F,  /* [ 6088] OBJ_sha512_224WithRSAEncryption */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x01,0x10,  /* [ 6097] OBJ_sha512_256WithRSAEncryption */
    0x2A,0x86,0x24,                                /* [ 6106] OBJ_ISO_UA */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,            /* [ 6109] OBJ_ua_pki */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,  /* [ 6116] OBJ_dstu4145le */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x01,0x01,  /* [ 6127] OBJ_dstu4145be */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x00,  /* [ 6140] OBJ_uacurve0 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x01,  /* [ 6153] OBJ_uacurve1 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x02,  /* [ 6166] OBJ_uacurve2 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x03,  /* [ 6179] OBJ_uacurve3 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x04,  /* [ 6192] OBJ_uacurve4 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x05,  /* [ 6205] OBJ_uacurve5 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x06,  /* [ 6218] OBJ_uacurve6 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x07,  /* [ 6231] OBJ_uacurve7 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x08,  /* [ 6244] OBJ_uacurve8 */
    0x2A,0x86,0x24,0x02,0x01,0x01,0x01,0x01,0x03,0x01,0x01,0x02,0x09,  /* [ 6257] OBJ_uacurve9 */
    0x2B,0x6F,                                     /* [ 6270] OBJ_ieee */
    0x2B,0x6F,0x02,0x8C,0x53,                      /* [ 6272] OBJ_ieee_siswg */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x82,0x2D,       /* [ 6277] OBJ_sm2 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x0C,       /* [ 6285] OBJ_hmacWithSHA512_224 */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x02,0x0D,       /* [ 6293] OBJ_hmacWithSHA512_256 */
    0x28,0xCC,0x45,0x03,0x04,                      /* [ 6301] OBJ_gmac */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x13,  /* [ 6306] OBJ_kmac128 */
    0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x14,  /* [ 6315] OBJ_kmac256 */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x83,0x75,       /* [ 6324] OBJ_SM2_with_SM3 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x08,0x09,       /* [ 6332] OBJ_id_on_SmtpUTF8Mailbox */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x08,0x05,       /* [ 6340] OBJ_XmppAddr */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x08,0x07,       /* [ 6348] OBJ_SRVName */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x08,0x08,       /* [ 6356] OBJ_NAIRealm */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x1D,       /* [ 6364] OBJ_cmcArchive */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x1E,       /* [ 6372] OBJ_id_kp_bgpsec_router */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x1F,       /* [ 6380] OBJ_id_kp_BrandIndicatorforMessageIdentification */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x03,0x20,       /* [ 6388] OBJ_cmKGA */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x11,       /* [ 6396] OBJ_id_it_caCerts */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x12,       /* [ 6404] OBJ_id_it_rootCaKeyUpdate */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x04,0x13,       /* [ 6412] OBJ_id_it_certReqTemplate */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x18,  /* [ 6420] OBJ_id_ct_routeOriginAuthz */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x1A,  /* [ 6431] OBJ_id_ct_rpkiManifest */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x23,  /* [ 6442] OBJ_id_ct_rpkiGhostbusters */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x24,  /* [ 6453] OBJ_id_ct_resourceTaggedAttest */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0E,            /* [ 6464] OBJ_id_cp */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x1C,       /* [ 6471] OBJ_sbgp_ipAddrBlockv2 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x01,0x1D,       /* [ 6479] OBJ_sbgp_autonomousSysNumv2 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0E,0x02,       /* [ 6487] OBJ_ipAddr_asNumber */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x0E,0x03,       /* [ 6495] OBJ_ipAddr_asNumberv2 */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x0A,       /* [ 6503] OBJ_rpkiManifest */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x0B,       /* [ 6511] OBJ_signedObject */
    0x2B,0x06,0x01,0x05,0x05,0x07,0x30,0x0D,       /* [ 6519] OBJ_rpkiNotify */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x2F,  /* [ 6527] OBJ_id_ct_geofeedCSVwithCRLF */
    0x2A,0x86,0x48,0x86,0xF7,0x0D,0x01,0x09,0x10,0x01,0x30,  /* [ 6538] OBJ_id_ct_signedChecklist */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x81,0x49,       /* [ 6549] OBJ_zuc */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x86,0x21,       /* [ 6557] OBJ_zuc_128_eea3 */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x08,       /* [ 6565] OBJ_sm4_gcm */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x68,0x09,       /* [ 6573] OBJ_sm4_ccm */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x86,0x22,       /* [ 6581] OBJ_zuc_128_eia3 */
    0x2B,0x06,0x01,0x04,0x01,0x82,0xDA,0x4B,0x2C,  /* [ 6589] OBJ_delegation_usage */
    0x2A,0x81,0x1C,0xCF,0x55,0x01,0x83,0x11,0x03,0x01,  /* [ 6598] OBJ_hmacWithSM3 */
    0x60,0x86,0x48,0x01,0x86,0xF9,0x66,            /* [ 6608] OBJ_oracle */
    0x60,0x86,0x48,0x01,0x86,0xF9,0x66,0xAD,0xCA,0x7B,0x01,0x01,  /* [ 6615] OBJ_oracle_jdk_trustedkeyusage */
};

#define NUM_NID 1281
static const ASN1_OBJECT nid_objs[NUM_NID] = {
    {"UNDEF", "undefined", NID_undef},
    {"rsadsi", "RSA Data Security, Inc.", NID_rsadsi, 6, &so[0]},
    {"pkcs", "RSA Data Security, Inc. PKCS", NID_pkcs, 7, &so[6]},
    { NULL, NULL, NID_undef },
    {"MD5", "md5", NID_md5, 8, &so[13]},
    {"RC4", "rc4", NID_rc4, 8, &so[21]},
    {"rsaEncryption", "rsaEncryption", NID_rsaEncryption, 9, &so[29]},
    { NULL, NULL, NID_undef },
    {"RSA-MD5", "md5WithRSAEncryption", NID_md5WithRSAEncryption, 9, &so[38]},
    { NULL, NULL, NID_undef },
    {"PBE-MD5-DES", "pbeWithMD5AndDES-CBC", NID_pbeWithMD5AndDES_CBC, 9, &so[47]},
    {"X500", "directory services (X.500)", NID_X500, 1, &so[56]},
    {"X509", "X509", NID_X509, 2, &so[57]},
    {"CN", "commonName", NID_commonName, 3, &so[59]},
    {"C", "countryName", NID_countryName, 3, &so[62]},
    {"L", "localityName", NID_localityName, 3, &so[65]},
    {"ST", "stateOrProvinceName", NID_stateOrProvinceName, 3, &so[68]},
    {"O", "organizationName", NID_organizationName, 3, &so[71]},
    {"OU", "organizationalUnitName", NID_organizationalUnitName, 3, &so[74]},
    {"RSA", "rsa", NID_rsa, 4, &so[77]},
    {"pkcs7", "pkcs7", NID_pkcs7, 8, &so[81]},
    {"pkcs7-data", "pkcs7-data", NID_pkcs7_data, 9, &so[89]},
    {"pkcs7-signedData", "pkcs7-signedData", NID_pkcs7_signed, 9, &so[98]},
    {"pkcs7-envelopedData", "pkcs7-envelopedData", NID_pkcs7_enveloped, 9, &so[107]},
    {"pkcs7-signedAndEnvelopedData", "pkcs7-signedAndEnvelopedData", NID_pkcs7_signedAndEnveloped, 9, &so[116]},
    {"pkcs7-digestData", "pkcs7-digestData", NID_pkcs7_digest, 9, &so[125]},
    {"pkcs7-encryptedData", "pkcs7-encryptedData", NID_pkcs7_encrypted, 9, &so[134]},
    {"pkcs3", "pkcs3", NID_pkcs3, 8, &so[143]},
    {"dhKeyAgreement", "dhKeyAgreement", NID_dhKeyAgreement, 9, &so[151]},
    {"DES-ECB", "des-ecb", NID_des_ecb, 5, &so[160]},
    {"DES-CFB", "des-cfb", NID_des_cfb64, 5, &so[165]},
    {"DES-CBC", "des-cbc", NID_des_cbc, 5, &so[170]},
    {"DES-EDE", "des-ede", NID_des_ede_ecb, 5, &so[175]},
    {"DES-EDE3", "des-ede3", NID_des_ede3_ecb},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"SHA", "sha", NID_sha, 5, &so[180]},
    {"RSA-SHA", "shaWithRSAEncryption", NID_shaWithRSAEncryption, 5, &so[185]},
    {"DES-EDE-CBC", "des-ede-cbc", NID_des_ede_cbc},
    {"DES-EDE3-CBC", "des-ede3-cbc", NID_des_ede3_cbc, 8, &so[190]},
    {"DES-OFB", "des-ofb", NID_des_ofb64, 5, &so[198]},
    { NULL, NULL, NID_undef },
    {"pkcs9", "pkcs9", NID_pkcs9, 8, &so[203]},
    {"emailAddress", "emailAddress", NID_pkcs9_emailAddress, 9, &so[211]},
    {"unstructuredName", "unstructuredName", NID_pkcs9_unstructuredName, 9, &so[220]},
    {"contentType", "contentType", NID_pkcs9_contentType, 9, &so[229]},
    {"messageDigest", "messageDigest", NID_pkcs9_messageDigest, 9, &so[238]},
    {"signingTime", "signingTime", NID_pkcs9_signingTime, 9, &so[247]},
    {"countersignature", "countersignature", NID_pkcs9_countersignature, 9, &so[256]},
    {"challengePassword", "challengePassword", NID_pkcs9_challengePassword, 9, &so[265]},
    {"unstructuredAddress", "unstructuredAddress", NID_pkcs9_unstructuredAddress, 9, &so[274]},
    {"extendedCertificateAttributes", "extendedCertificateAttributes", NID_pkcs9_extCertAttributes, 9, &so[283]},
    {"Netscape", "Netscape Communications Corp.", NID_netscape, 7, &so[292]},
    {"nsCertExt", "Netscape Certificate Extension", NID_netscape_cert_extension, 8, &so[299]},
    {"nsDataType", "Netscape Data Type", NID_netscape_data_type, 8, &so[307]},
    {"DES-EDE-CFB", "des-ede-cfb", NID_des_ede_cfb64},
    {"DES-EDE3-CFB", "des-ede3-cfb", NID_des_ede3_cfb64},
    {"DES-EDE-OFB", "des-ede-ofb", NID_des_ede_ofb64},
    {"DES-EDE3-OFB", "des-ede3-ofb", NID_des_ede3_ofb64},
    {"SHA1", "sha1", NID_sha1, 5, &so[315]},
    {"RSA-SHA1", "sha1WithRSAEncryption", NID_sha1WithRSAEncryption, 9, &so[320]},
    {"DSA-SHA", "dsaWithSHA", NID_dsaWithSHA, 5, &so[329]},
    {"DSA-old", "dsaEncryption-old", NID_dsa_2, 5, &so[334]},
    { NULL, NULL, NID_undef },
    {"PBKDF2", "PBKDF2", NID_id_pbkdf2, 9, &so[339]},
    {"DSA-SHA1-old", "dsaWithSHA1-old", NID_dsaWithSHA1_2, 5, &so[348]},
    {"nsCertType", "Netscape Cert Type", NID_netscape_cert_type, 9, &so[353]},
    {"nsBaseUrl", "Netscape Base Url", NID_netscape_base_url, 9, &so[362]},
    {"nsRevocationUrl", "Netscape Revocation Url", NID_netscape_revocation_url, 9, &so[371]},
    {"nsCaRevocationUrl", "Netscape CA Revocation Url", NID_netscape_ca_revocation_url, 9, &so[380]},
    {"nsRenewalUrl", "Netscape Renewal Url", NID_netscape_renewal_url, 9, &so[389]},
    {"nsCaPolicyUrl", "Netscape CA Policy Url", NID_netscape_ca_policy_url, 9, &so[398]},
    {"nsSslServerName", "Netscape SSL Server Name", NID_netscape_ssl_server_name, 9, &so[407]},
    {"nsComment", "Netscape Comment", NID_netscape_comment, 9, &so[416]},
    {"nsCertSequence", "Netscape Certificate Sequence", NID_netscape_cert_sequence, 9, &so[425]},
    {"DESX-CBC", "desx-cbc", NID_desx_cbc},
    {"id-ce", "id-ce", NID_id_ce, 2, &so[434]},
    {"subjectKeyIdentifier", "X509v3 Subject Key Identifier", NID_subject_key_identifier, 3, &so[436]},
    {"keyUsage", "X509v3 Key Usage", NID_key_usage, 3, &so[439]},
    {"privateKeyUsagePeriod", "X509v3 Private Key Usage Period", NID_private_key_usage_period, 3, &so[442]},
    {"subjectAltName", "X509v3 Subject Alternative Name", NID_subject_alt_name, 3, &so[445]},
    {"issuerAltName", "X509v3 Issuer Alternative Name", NID_issuer_alt_name, 3, &so[448]},
    {"basicConstraints", "X509v3 Basic Constraints", NID_basic_constraints, 3, &so[451]},
    {"crlNumber", "X509v3 CRL Number", NID_crl_number, 3, &so[454]},
    {"certificatePolicies", "X509v3 Certificate Policies", NID_certificate_policies, 3, &so[457]},
    {"authorityKeyIdentifier", "X509v3 Authority Key Identifier", NID_authority_key_identifier, 3, &so[460]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"RC4-40", "rc4-40", NID_rc4_40},
    { NULL, NULL, NID_undef },
    {"GN", "givenName", NID_givenName, 3, &so[463]},
    {"SN", "surname", NID_surname, 3, &so[466]},
    {"initials", "initials", NID_initials, 3, &so[469]},
    {"uid", "uniqueIdentifier", NID_uniqueIdentifier, 10, &so[472]},
    {"crlDistributionPoints", "X509v3 CRL Distribution Points", NID_crl_distribution_points, 3, &so[482]},
    {"RSA-NP-MD5", "md5WithRSA", NID_md5WithRSA, 5, &so[485]},
    {"serialNumber", "serialNumber", NID_serialNumber, 3, &so[490]},
    {"title", "title", NID_title, 3, &so[493]},
    {"description", "description", NID_description, 3, &so[496]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"DSA-SHA1", "dsaWithSHA1", NID_dsaWithSHA1, 7, &so[499]},
    {"MD5-SHA1", "md5-sha1", NID_md5_sha1},
    {"RSA-SHA1-2", "sha1WithRSA", NID_sha1WithRSA, 5, &so[506]},
    {"DSA", "dsaEncryption", NID_dsa, 7, &so[511]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"RC5-CBC", "rc5-cbc", NID_rc5_cbc, 8, &so[518]},
    {"RC5-ECB", "rc5-ecb", NID_rc5_ecb},
    {"RC5-CFB", "rc5-cfb", NID_rc5_cfb64},
    {"RC5-OFB", "rc5-ofb", NID_rc5_ofb64},
    { NULL, NULL, NID_undef },
    {"ZLIB", "zlib compression", NID_zlib_compression, 11, &so[526]},
    {"extendedKeyUsage", "X509v3 Extended Key Usage", NID_ext_key_usage, 3, &so[537]},
    {"PKIX", "PKIX", NID_id_pkix, 6, &so[540]},
    {"id-kp", "id-kp", NID_id_kp, 7, &so[546]},
    {"serverAuth", "TLS Web Server Authentication", NID_server_auth, 8, &so[553]},
    {"clientAuth", "TLS Web Client Authentication", NID_client_auth, 8, &so[561]},
    {"codeSigning", "Code Signing", NID_code_sign, 8, &so[569]},
    {"emailProtection", "E-mail Protection", NID_email_protect, 8, &so[577]},
    {"timeStamping", "Time Stamping", NID_time_stamp, 8, &so[585]},
    {"msCodeInd", "Microsoft Individual Code Signing", NID_ms_code_ind, 10, &so[593]},
    {"msCodeCom", "Microsoft Commercial Code Signing", NID_ms_code_com, 10, &so[603]},
    {"msCTLSign", "Microsoft Trust List Signing", NID_ms_ctl_sign, 10, &so[613]},
    {"msSGC", "Microsoft Server Gated Crypto", NID_ms_sgc, 10, &so[623]},
    {"msEFS", "Microsoft Encrypted File System", NID_ms_efs, 10, &so[633]},
    {"nsSGC", "Netscape Server Gated Crypto", NID_ns_sgc, 9, &so[643]},
    {"deltaCRL", "X509v3 Delta CRL Indicator", NID_delta_crl, 3, &so[652]},
    {"CRLReason", "X509v3 CRL Reason Code", NID_crl_reason, 3, &so[655]},
    {"invalidityDate", "Invalidity Date", NID_invalidity_date, 3, &so[658]},
    {"SXNetID", "Strong Extranet ID", NID_sxnet, 5, &so[661]},
    {"PBE-SHA1-RC4-128", "pbeWithSHA1And128BitRC4", NID_pbe_WithSHA1And128BitRC4, 10, &so[666]},
    {"PBE-SHA1-RC4-40", "pbeWithSHA1And40BitRC4", NID_pbe_WithSHA1And40BitRC4, 10, &so[676]},
    {"PBE-SHA1-3DES", "pbeWithSHA1And3-KeyTripleDES-CBC", NID_pbe_WithSHA1And3_Key_TripleDES_CBC, 10, &so[686]},
    {"PBE-SHA1-2DES", "pbeWithSHA1And2-KeyTripleDES-CBC", NID_pbe_WithSHA1And2_Key_TripleDES_CBC, 10, &so[696]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"keyBag", "keyBag", NID_keyBag, 11, &so[706]},
    {"pkcs8ShroudedKeyBag", "pkcs8ShroudedKeyBag", NID_pkcs8ShroudedKeyBag, 11, &so[717]},
    {"certBag", "certBag", NID_certBag, 11, &so[728]},
    {"crlBag", "crlBag", NID_crlBag, 11, &so[739]},
    {"secretBag", "secretBag", NID_secretBag, 11, &so[750]},
    {"safeContentsBag", "safeContentsBag", NID_safeContentsBag, 11, &so[761]},
    {"friendlyName", "friendlyName", NID_friendlyName, 9, &so[772]},
    {"localKeyID", "localKeyID", NID_localKeyID, 9, &so[781]},
    {"x509Certificate", "x509Certificate", NID_x509Certificate, 10, &so[790]},
    {"sdsiCertificate", "sdsiCertificate", NID_sdsiCertificate, 10, &so[800]},
    {"x509Crl", "x509Crl", NID_x509Crl, 10, &so[810]},
    {"PBES2", "PBES2", NID_pbes2, 9, &so[820]},
    {"PBMAC1", "PBMAC1", NID_pbmac1, 9, &so[829]},
    {"hmacWithSHA1", "hmacWithSHA1", NID_hmacWithSHA1, 8, &so[838]},
    {"id-qt-cps", "Policy Qualifier CPS", NID_id_qt_cps, 8, &so[846]},
    {"id-qt-unotice", "Policy Qualifier User Notice", NID_id_qt_unotice, 8, &so[854]},
    { NULL, NULL, NID_undef },
    {"SMIME-CAPS", "S/MIME Capabilities", NID_SMIMECapabilities, 9, &so[862]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"PBE-SHA1-DES", "pbeWithSHA1AndDES-CBC", NID_pbeWithSHA1AndDES_CBC, 9, &so[871]},
    {"msExtReq", "Microsoft Extension Request", NID_ms_ext_req, 10, &so[880]},
    {"extReq", "Extension Request", NID_ext_req, 9, &so[890]},
    {"name", "name", NID_name, 3, &so[899]},
    {"dnQualifier", "dnQualifier", NID_dnQualifier, 3, &so[902]},
    {"id-pe", "id-pe", NID_id_pe, 7, &so[905]},
    {"id-ad", "id-ad", NID_id_ad, 7, &so[912]},
    {"authorityInfoAccess", "Authority Information Access", NID_info_access, 8, &so[919]},
    {"OCSP", "OCSP", NID_ad_OCSP, 8, &so[927]},
    {"caIssuers", "CA Issuers", NID_ad_ca_issuers, 8, &so[935]},
    {"OCSPSigning", "OCSP Signing", NID_OCSP_sign, 8, &so[943]},
    {"ISO", "iso", NID_iso},
    {"member-body", "ISO Member Body", NID_member_body, 1, &so[951]},
    {"ISO-US", "ISO US Member Body", NID_ISO_US, 3, &so[952]},
    {"X9-57", "X9.57", NID_X9_57, 5, &so[955]},
    {"X9cm", "X9.57 CM ?", NID_X9cm, 6, &so[960]},
    {"pkcs1", "pkcs1", NID_pkcs1, 8, &so[966]},
    {"pkcs5", "pkcs5", NID_pkcs5, 8, &so[974]},
    {"SMIME", "S/MIME", NID_SMIME, 9, &so[982]},
    {"id-smime-mod", "id-smime-mod", NID_id_smime_mod, 10, &so[991]},
    {"id-smime-ct", "id-smime-ct", NID_id_smime_ct, 10, &so[1001]},
    {"id-smime-aa", "id-smime-aa", NID_id_smime_aa, 10, &so[1011]},
    {"id-smime-alg", "id-smime-alg", NID_id_smime_alg, 10, &so[1021]},
    {"id-smime-cd", "id-smime-cd", NID_id_smime_cd, 10, &so[1031]},
    {"id-smime-spq", "id-smime-spq", NID_id_smime_spq, 10, &so[1041]},
    {"id-smime-cti", "id-smime-cti", NID_id_smime_cti, 10, &so[1051]},
    {"id-smime-mod-cms", "id-smime-mod-cms", NID_id_smime_mod_cms, 11, &so[1061]},
    {"id-smime-mod-ess", "id-smime-mod-ess", NID_id_smime_mod_ess, 11, &so[1072]},
    {"id-smime-mod-oid", "id-smime-mod-oid", NID_id_smime_mod_oid, 11, &so[1083]},
    {"id-smime-mod-msg-v3", "id-smime-mod-msg-v3", NID_id_smime_mod_msg_v3, 11, &so[1094]},
    {"id-smime-mod-ets-eSignature-88", "id-smime-mod-ets-eSignature-88", NID_id_smime_mod_ets_eSignature_88, 11, &so[1105]},
    {"id-smime-mod-ets-eSignature-97", "id-smime-mod-ets-eSignature-97", NID_id_smime_mod_ets_eSignature_97, 11, &so[1116]},
    {"id-smime-mod-ets-eSigPolicy-88", "id-smime-mod-ets-eSigPolicy-88", NID_id_smime_mod_ets_eSigPolicy_88, 11, &so[1127]},
    {"id-smime-mod-ets-eSigPolicy-97", "id-smime-mod-ets-eSigPolicy-97", NID_id_smime_mod_ets_eSigPolicy_97, 11, &so[1138]},
    {"id-smime-ct-receipt", "id-smime-ct-receipt", NID_id_smime_ct_receipt, 11, &so[1149]},
    {"id-smime-ct-authData", "id-smime-ct-authData", NID_id_smime_ct_authData, 11, &so[1160]},
    {"id-smime-ct-publishCert", "id-smime-ct-publishCert", NID_id_smime_ct_publishCert, 11, &so[1171]},
    {"id-smime-ct-TSTInfo", "id-smime-ct-TSTInfo", NID_id_smime_ct_TSTInfo, 11, &so[1182]},
    {"id-smime-ct-TDTInfo", "id-smime-ct-TDTInfo", NID_id_smime_ct_TDTInfo, 11, &so[1193]},
    {"id-smime-ct-contentInfo", "id-smime-ct-contentInfo", NID_id_smime_ct_contentInfo, 11, &so[1204]},
    {"id-smime-ct-DVCSRequestData", "id-smime-ct-DVCSRequestData", NID_id_smime_ct_DVCSRequestData, 11, &so[1215]},
    {"id-smime-ct-DVCSResponseData", "id-smime-ct-DVCSResponseData", NID_id_smime_ct_DVCSResponseData, 11, &so[1226]},
    {"id-smime-aa-receiptRequest", "id-smime-aa-receiptRequest", NID_id_smime_aa_receiptRequest, 11, &so[1237]},
    {"id-smime-aa-securityLabel", "id-smime-aa-securityLabel", NID_id_smime_aa_securityLabel, 11, &so[1248]},
    {"id-smime-aa-mlExpandHistory", "id-smime-aa-mlExpandHistory", NID_id_smime_aa_mlExpandHistory, 11, &so[1259]},
    {"id-smime-aa-contentHint", "id-smime-aa-contentHint", NID_id_smime_aa_contentHint, 11, &so[1270]},
    {"id-smime-aa-msgSigDigest", "id-smime-aa-msgSigDigest", NID_id_smime_aa_msgSigDigest, 11, &so[1281]},
    {"id-smime-aa-encapContentType", "id-smime-aa-encapContentType", NID_id_smime_aa_encapContentType, 11, &so[1292]},
    {"id-smime-aa-contentIdentifier", "id-smime-aa-contentIdentifier", NID_id_smime_aa_contentIdentifier, 11, &so[1303]},
    {"id-smime-aa-macValue", "id-smime-aa-macValue", NID_id_smime_aa_macValue, 11, &so[1314]},
    {"id-smime-aa-equivalentLabels", "id-smime-aa-equivalentLabels", NID_id_smime_aa_equivalentLabels, 11, &so[1325]},
    {"id-smime-aa-contentReference", "id-smime-aa-contentReference", NID_id_smime_aa_contentReference, 11, &so[1336]},
    {"id-smime-aa-encrypKeyPref", "id-smime-aa-encrypKeyPref", NID_id_smime_aa_encrypKeyPref, 11, &so[1347]},
    {"id-smime-aa-signingCertificate", "id-smime-aa-signingCertificate", NID_id_smime_aa_signingCertificate, 11, &so[1358]},
    {"id-smime-aa-smimeEncryptCerts", "id-smime-aa-smimeEncryptCerts", NID_id_smime_aa_smimeEncryptCerts, 11, &so[1369]},
    {"id-smime-aa-timeStampToken", "id-smime-aa-timeStampToken", NID_id_smime_aa_timeStampToken, 11, &so[1380]},
    {"id-smime-aa-ets-sigPolicyId", "id-smime-aa-ets-sigPolicyId", NID_id_smime_aa_ets_sigPolicyId, 11, &so[1391]},
    {"id-smime-aa-ets-commitmentType", "id-smime-aa-ets-commitmentType", NID_id_smime_aa_ets_commitmentType, 11, &so[1402]},
    {"id-smime-aa-ets-signerLocation", "id-smime-aa-ets-signerLocation", NID_id_smime_aa_ets_signerLocation, 11, &so[1413]},
    {"id-smime-aa-ets-signerAttr", "id-smime-aa-ets-signerAttr", NID_id_smime_aa_ets_signerAttr, 11, &so[1424]},
    {"id-smime-aa-ets-otherSigCert", "id-smime-aa-ets-otherSigCert", NID_id_smime_aa_ets_otherSigCert, 11, &so[1435]},
    {"id-smime-aa-ets-contentTimestamp", "id-smime-aa-ets-contentTimestamp", NID_id_smime_aa_ets_contentTimestamp, 11, &so[1446]},
    {"id-smime-aa-ets-CertificateRefs", "id-smime-aa-ets-CertificateRefs", NID_id_smime_aa_ets_CertificateRefs, 11, &so[1457]},
    {"id-smime-aa-ets-RevocationRefs", "id-smime-aa-ets-RevocationRefs", NID_id_smime_aa_ets_RevocationRefs, 11, &so[1468]},
    {"id-smime-aa-ets-certValues", "id-smime-aa-ets-certValues", NID_id_smime_aa_ets_certValues, 11, &so[1479]},
    {"id-smime-aa-ets-revocationValues", "id-smime-aa-ets-revocationValues", NID_id_smime_aa_ets_revocationValues, 11, &so[1490]},
    {"id-smime-aa-ets-escTimeStamp", "id-smime-aa-ets-escTimeStamp", NID_id_smime_aa_ets_escTimeStamp, 11, &so[1501]},
    {"id-smime-aa-ets-certCRLTimestamp", "id-smime-aa-ets-certCRLTimestamp", NID_id_smime_aa_ets_certCRLTimestamp, 11, &so[1512]},
    {"id-smime-aa-ets-archiveTimeStamp", "id-smime-aa-ets-archiveTimeStamp", NID_id_smime_aa_ets_archiveTimeStamp, 11, &so[1523]},
    {"id-smime-aa-signatureType", "id-smime-aa-signatureType", NID_id_smime_aa_signatureType, 11, &so[1534]},
    {"id-smime-aa-dvcs-dvc", "id-smime-aa-dvcs-dvc", NID_id_smime_aa_dvcs_dvc, 11, &so[1545]},
    {"id-smime-alg-ESDHwith3DES", "id-smime-alg-ESDHwith3DES", NID_id_smime_alg_ESDHwith3DES, 11, &so[1556]},
    { NULL, NULL, NID_undef },
    {"id-smime-alg-3DESwrap", "id-smime-alg-3DESwrap", NID_id_smime_alg_3DESwrap, 11, &so[1567]},
    { NULL, NULL, NID_undef },
    {"id-smime-alg-ESDH", "id-smime-alg-ESDH", NID_id_smime_alg_ESDH, 11, &so[1578]},
    {"id-smime-alg-CMS3DESwrap", "id-smime-alg-CMS3DESwrap", NID_id_smime_alg_CMS3DESwrap, 11, &so[1589]},
    { NULL, NULL, NID_undef },
    {"id-smime-cd-ldap", "id-smime-cd-ldap", NID_id_smime_cd_ldap, 11, &so[1600]},
    {"id-smime-spq-ets-sqt-uri", "id-smime-spq-ets-sqt-uri", NID_id_smime_spq_ets_sqt_uri, 11, &so[1611]},
    {"id-smime-spq-ets-sqt-unotice", "id-smime-spq-ets-sqt-unotice", NID_id_smime_spq_ets_sqt_unotice, 11, &so[1622]},
    {"id-smime-cti-ets-proofOfOrigin", "id-smime-cti-ets-proofOfOrigin", NID_id_smime_cti_ets_proofOfOrigin, 11, &so[1633]},
    {"id-smime-cti-ets-proofOfReceipt", "id-smime-cti-ets-proofOfReceipt", NID_id_smime_cti_ets_proofOfReceipt, 11, &so[1644]},
    {"id-smime-cti-ets-proofOfDelivery", "id-smime-cti-ets-proofOfDelivery", NID_id_smime_cti_ets_proofOfDelivery, 11, &so[1655]},
    {"id-smime-cti-ets-proofOfSender", "id-smime-cti-ets-proofOfSender", NID_id_smime_cti_ets_proofOfSender, 11, &so[1666]},
    {"id-smime-cti-ets-proofOfApproval", "id-smime-cti-ets-proofOfApproval", NID_id_smime_cti_ets_proofOfApproval, 11, &so[1677]},
    {"id-smime-cti-ets-proofOfCreation", "id-smime-cti-ets-proofOfCreation", NID_id_smime_cti_ets_proofOfCreation, 11, &so[1688]},
    { NULL, NULL, NID_undef },
    {"id-pkix-mod", "id-pkix-mod", NID_id_pkix_mod, 7, &so[1699]},
    {"id-qt", "id-qt", NID_id_qt, 7, &so[1706]},
    {"id-it", "id-it", NID_id_it, 7, &so[1713]},
    {"id-pkip", "id-pkip", NID_id_pkip, 7, &so[1720]},
    {"id-alg", "id-alg", NID_id_alg, 7, &so[1727]},
    {"id-cmc", "id-cmc", NID_id_cmc, 7, &so[1734]},
    {"id-on", "id-on", NID_id_on, 7, &so[1741]},
    {"id-pda", "id-pda", NID_id_pda, 7, &so[1748]},
    {"id-aca", "id-aca", NID_id_aca, 7, &so[1755]},
    {"id-qcs", "id-qcs", NID_id_qcs, 7, &so[1762]},
    {"id-cct", "id-cct", NID_id_cct, 7, &so[1769]},
    {"id-pkix1-explicit-88", "id-pkix1-explicit-88", NID_id_pkix1_explicit_88, 8, &so[1776]},
    {"id-pkix1-implicit-88", "id-pkix1-implicit-88", NID_id_pkix1_implicit_88, 8, &so[1784]},
    {"id-pkix1-explicit-93", "id-pkix1-explicit-93", NID_id_pkix1_explicit_93, 8, &so[1792]},
    {"id-pkix1-implicit-93", "id-pkix1-implicit-93", NID_id_pkix1_implicit_93, 8, &so[1800]},
    {"id-mod-crmf", "id-mod-crmf", NID_id_mod_crmf, 8, &so[1808]},
    {"id-mod-cmc", "id-mod-cmc", NID_id_mod_cmc, 8, &so[1816]},
    {"id-mod-kea-profile-88", "id-mod-kea-profile-88", NID_id_mod_kea_profile_88, 8, &so[1824]},
    {"id-mod-kea-profile-93", "id-mod-kea-profile-93", NID_id_mod_kea_profile_93, 8, &so[1832]},
    {"id-mod-cmp", "id-mod-cmp", NID_id_mod_cmp, 8, &so[1840]},
    {"id-mod-qualified-cert-88", "id-mod-qualified-cert-88", NID_id_mod_qualified_cert_88, 8, &so[1848]},
    {"id-mod-qualified-cert-93", "id-mod-qualified-cert-93", NID_id_mod_qualified_cert_93, 8, &so[1856]},
    {"id-mod-attribute-cert", "id-mod-attribute-cert", NID_id_mod_attribute_cert, 8, &so[1864]},
    {"id-mod-timestamp-protocol", "id-mod-timestamp-protocol", NID_id_mod_timestamp_protocol, 8, &so[1872]},
    {"id-mod-ocsp", "id-mod-ocsp", NID_id_mod_ocsp, 8, &so[1880]},
    {"id-mod-dvcs", "id-mod-dvcs", NID_id_mod_dvcs, 8, &so[1888]},
    {"id-mod-cmp2000", "id-mod-cmp2000", NID_id_mod_cmp2000, 8, &so[1896]},
    {"biometricInfo", "Biometric Info", NID_biometricInfo, 8, &so[1904]},
    {"qcStatements", "qcStatements", NID_qcStatements, 8, &so[1912]},
    {"ac-auditEntity", "ac-auditEntity", NID_ac_auditEntity, 8, &so[1920]},
    {"ac-targeting", "ac-targeting", NID_ac_targeting, 8, &so[1928]},
    {"aaControls", "aaControls", NID_aaControls, 8, &so[1936]},
    {"sbgp-ipAddrBlock", "sbgp-ipAddrBlock", NID_sbgp_ipAddrBlock, 8, &so[1944]},
    {"sbgp-autonomousSysNum", "sbgp-autonomousSysNum", NID_sbgp_autonomousSysNum, 8, &so[1952]},
    {"sbgp-routerIdentifier", "sbgp-routerIdentifier", NID_sbgp_routerIdentifier, 8, &so[1960]},
    {"textNotice", "textNotice", NID_textNotice, 8, &so[1968]},
    {"ipsecEndSystem", "IPSec End System", NID_ipsecEndSystem, 8, &so[1976]},
    {"ipsecTunnel", "IPSec Tunnel", NID_ipsecTunnel, 8, &so[1984]},
    {"ipsecUser", "IPSec User", NID_ipsecUser, 8, &so[1992]},
    {"DVCS", "dvcs", NID_dvcs, 8, &so[2000]},
    {"id-it-caProtEncCert", "id-it-caProtEncCert", NID_id_it_caProtEncCert, 8, &so[2008]},
    {"id-it-signKeyPairTypes", "id-it-signKeyPairTypes", NID_id_it_signKeyPairTypes, 8, &so[2016]},
    {"id-it-encKeyPairTypes", "id-it-encKeyPairTypes", NID_id_it_encKeyPairTypes, 8, &so[2024]},
    {"id-it-preferredSymmAlg", "id-it-preferredSymmAlg", NID_id_it_preferredSymmAlg, 8, &so[2032]},
    {"id-it-caKeyUpdateInfo", "id-it-caKeyUpdateInfo", NID_id_it_caKeyUpdateInfo, 8, &so[2040]},
    {"id-it-currentCRL", "id-it-currentCRL", NID_id_it_currentCRL, 8, &so[2048]},
    {"id-it-unsupportedOIDs", "id-it-unsupportedOIDs", NID_id_it_unsupportedOIDs, 8, &so[2056]},
    {"id-it-subscriptionRequest", "id-it-subscriptionRequest", NID_id_it_subscriptionRequest, 8, &so[2064]},
    {"id-it-subscriptionResponse", "id-it-subscriptionResponse", NID_id_it_subscriptionResponse, 8, &so[2072]},
    {"id-it-keyPairParamReq", "id-it-keyPairParamReq", NID_id_it_keyPairParamReq, 8, &so[2080]},
    {"id-it-keyPairParamRep", "id-it-keyPairParamRep", NID_id_it_keyPairParamRep, 8, &so[2088]},
    {"id-it-revPassphrase", "id-it-revPassphrase", NID_id_it_revPassphrase, 8, &so[2096]},
    {"id-it-implicitConfirm", "id-it-implicitConfirm", NID_id_it_implicitConfirm, 8, &so[2104]},
    {"id-it-confirmWaitTime", "id-it-confirmWaitTime", NID_id_it_confirmWaitTime, 8, &so[2112]},
    {"id-it-origPKIMessage", "id-it-origPKIMessage", NID_id_it_origPKIMessage, 8, &so[2120]},
    {"id-regCtrl", "id-regCtrl", NID_id_regCtrl, 8, &so[2128]},
    {"id-regInfo", "id-regInfo", NID_id_regInfo, 8, &so[2136]},
    {"id-regCtrl-regToken", "id-regCtrl-regToken", NID_id_regCtrl_regToken, 9, &so[2144]},
    {"id-regCtrl-authenticator", "id-regCtrl-authenticator", NID_id_regCtrl_authenticator, 9, &so[2153]},
    {"id-regCtrl-pkiPublicationInfo", "id-regCtrl-pkiPublicationInfo", NID_id_regCtrl_pkiPublicationInfo, 9, &so[2162]},
    {"id-regCtrl-pkiArchiveOptions", "id-regCtrl-pkiArchiveOptions", NID_id_regCtrl_pkiArchiveOptions, 9, &so[2171]},
    {"id-regCtrl-oldCertID", "id-regCtrl-oldCertID", NID_id_regCtrl_oldCertID, 9, &so[2180]},
    {"id-regCtrl-protocolEncrKey", "id-regCtrl-protocolEncrKey", NID_id_regCtrl_protocolEncrKey, 9, &so[2189]},
    {"id-regInfo-utf8Pairs", "id-regInfo-utf8Pairs", NID_id_regInfo_utf8Pairs, 9, &so[2198]},
    {"id-regInfo-certReq", "id-regInfo-certReq", NID_id_regInfo_certReq, 9, &so[2207]},
    {"id-alg-des40", "id-alg-des40", NID_id_alg_des40, 8, &so[2216]},
    {"id-alg-noSignature", "id-alg-noSignature", NID_id_alg_noSignature, 8, &so[2224]},
    {"id-alg-dh-sig-hmac-sha1", "id-alg-dh-sig-hmac-sha1", NID_id_alg_dh_sig_hmac_sha1, 8, &so[2232]},
    {"id-alg-dh-pop", "id-alg-dh-pop", NID_id_alg_dh_pop, 8, &so[2240]},
    {"id-cmc-statusInfo", "id-cmc-statusInfo", NID_id_cmc_statusInfo, 8, &so[2248]},
    {"id-cmc-identification", "id-cmc-identification", NID_id_cmc_identification, 8, &so[2256]},
    {"id-cmc-identityProof", "id-cmc-identityProof", NID_id_cmc_identityProof, 8, &so[2264]},
    {"id-cmc-dataReturn", "id-cmc-dataReturn", NID_id_cmc_dataReturn, 8, &so[2272]},
    {"id-cmc-transactionId", "id-cmc-transactionId", NID_id_cmc_transactionId, 8, &so[2280]},
    {"id-cmc-senderNonce", "id-cmc-senderNonce", NID_id_cmc_senderNonce, 8, &so[2288]},
    {"id-cmc-recipientNonce", "id-cmc-recipientNonce", NID_id_cmc_recipientNonce, 8, &so[2296]},
    {"id-cmc-addExtensions", "id-cmc-addExtensions", NID_id_cmc_addExtensions, 8, &so[2304]},
    {"id-cmc-encryptedPOP", "id-cmc-encryptedPOP", NID_id_cmc_encryptedPOP, 8, &so[2312]},
    {"id-cmc-decryptedPOP", "id-cmc-decryptedPOP", NID_id_cmc_decryptedPOP, 8, &so[2320]},
    {"id-cmc-lraPOPWitness", "id-cmc-lraPOPWitness", NID_id_cmc_lraPOPWitness, 8, &so[2328]},
    {"id-cmc-getCert", "id-cmc-getCert", NID_id_cmc_getCert, 8, &so[2336]},
    {"id-cmc-getCRL", "id-cmc-getCRL", NID_id_cmc_getCRL, 8, &so[2344]},
    {"id-cmc-revokeRequest", "id-cmc-revokeRequest", NID_id_cmc_revokeRequest, 8, &so[2352]},
    {"id-cmc-regInfo", "id-cmc-regInfo", NID_id_cmc_regInfo, 8, &so[2360]},
    {"id-cmc-responseInfo", "id-cmc-responseInfo", NID_id_cmc_responseInfo, 8, &so[2368]},
    {"id-cmc-queryPending", "id-cmc-queryPending", NID_id_cmc_queryPending, 8, &so[2376]},
    {"id-cmc-popLinkRandom", "id-cmc-popLinkRandom", NID_id_cmc_popLinkRandom, 8, &so[2384]},
    {"id-cmc-popLinkWitness", "id-cmc-popLinkWitness", NID_id_cmc_popLinkWitness, 8, &so[2392]},
    {"id-cmc-confirmCertAcceptance", "id-cmc-confirmCertAcceptance", NID_id_cmc_confirmCertAcceptance, 8, &so[2400]},
    {"id-on-personalData", "id-on-personalData", NID_id_on_personalData, 8, &so[2408]},
    {"id-pda-dateOfBirth", "id-pda-dateOfBirth", NID_id_pda_dateOfBirth, 8, &so[2416]},
    {"id-pda-placeOfBirth", "id-pda-placeOfBirth", NID_id_pda_placeOfBirth, 8, &so[2424]},
    { NULL, NULL, NID_undef },
    {"id-pda-gender", "id-pda-gender", NID_id_pda_gender, 8, &so[2432]},
    {"id-pda-countryOfCitizenship", "id-pda-countryOfCitizenship", NID_id_pda_countryOfCitizenship, 8, &so[2440]},
    {"id-pda-countryOfResidence", "id-pda-countryOfResidence", NID_id_pda_countryOfResidence, 8, &so[2448]},
    {"id-aca-authenticationInfo", "id-aca-authenticationInfo", NID_id_aca_authenticationInfo, 8, &so[2456]},
    {"id-aca-accessIdentity", "id-aca-accessIdentity", NID_id_aca_accessIdentity, 8, &so[2464]},
    {"id-aca-chargingIdentity", "id-aca-chargingIdentity", NID_id_aca_chargingIdentity, 8, &so[2472]},
    {"id-aca-group", "id-aca-group", NID_id_aca_group, 8, &so[2480]},
    {"id-aca-role", "id-aca-role", NID_id_aca_role, 8, &so[2488]},
    {"id-qcs-pkixQCSyntax-v1", "id-qcs-pkixQCSyntax-v1", NID_id_qcs_pkixQCSyntax_v1, 8, &so[2496]},
    {"id-cct-crs", "id-cct-crs", NID_id_cct_crs, 8, &so[2504]},
    {"id-cct-PKIData", "id-cct-PKIData", NID_id_cct_PKIData, 8, &so[2512]},
    {"id-cct-PKIResponse", "id-cct-PKIResponse", NID_id_cct_PKIResponse, 8, &so[2520]},
    {"ad_timestamping", "AD Time Stamping", NID_ad_timeStamping, 8, &so[2528]},
    {"AD_DVCS", "ad dvcs", NID_ad_dvcs, 8, &so[2536]},
    {"basicOCSPResponse", "Basic OCSP Response", NID_id_pkix_OCSP_basic, 9, &so[2544]},
    {"Nonce", "OCSP Nonce", NID_id_pkix_OCSP_Nonce, 9, &so[2553]},
    {"CrlID", "OCSP CRL ID", NID_id_pkix_OCSP_CrlID, 9, &so[2562]},
    {"acceptableResponses", "Acceptable OCSP Responses", NID_id_pkix_OCSP_acceptableResponses, 9, &so[2571]},
    {"noCheck", "OCSP No Check", NID_id_pkix_OCSP_noCheck, 9, &so[2580]},
    {"archiveCutoff", "OCSP Archive Cutoff", NID_id_pkix_OCSP_archiveCutoff, 9, &so[2589]},
    {"serviceLocator", "OCSP Service Locator", NID_id_pkix_OCSP_serviceLocator, 9, &so[2598]},
    {"extendedStatus", "Extended OCSP Status", NID_id_pkix_OCSP_extendedStatus, 9, &so[2607]},
    {"valid", "valid", NID_id_pkix_OCSP_valid, 9, &so[2616]},
    {"path", "path", NID_id_pkix_OCSP_path, 9, &so[2625]},
    {"trustRoot", "Trust Root", NID_id_pkix_OCSP_trustRoot, 9, &so[2634]},
    {"algorithm", "algorithm", NID_algorithm, 4, &so[2643]},
    {"rsaSignature", "rsaSignature", NID_rsaSignature, 5, &so[2647]},
    {"X500algorithms", "directory services - algorithms", NID_X500algorithms, 2, &so[2652]},
    {"ORG", "org", NID_org, 1, &so[2654]},
    {"DOD", "dod", NID_dod, 2, &so[2655]},
    {"IANA", "iana", NID_iana, 3, &so[2657]},
    {"directory", "Directory", NID_Directory, 4, &so[2660]},
    {"mgmt", "Management", NID_Management, 4, &so[2664]},
    {"experimental", "Experimental", NID_Experimental, 4, &so[2668]},
    {"private", "Private", NID_Private, 4, &so[2672]},
    {"security", "Security", NID_Security, 4, &so[2676]},
    {"snmpv2", "SNMPv2", NID_SNMPv2, 4, &so[2680]},
    {"Mail", "Mail", NID_Mail, 4, &so[2684]},
    {"enterprises", "Enterprises", NID_Enterprises, 5, &so[2688]},
    {"dcobject", "dcObject", NID_dcObject, 9, &so[2693]},
    {"DC", "domainComponent", NID_domainComponent, 10, &so[2702]},
    {"domain", "Domain", NID_Domain, 10, &so[2712]},
    {"NULL", "NULL", NID_joint_iso_ccitt},
    {"selected-attribute-types", "Selected Attribute Types", NID_selected_attribute_types, 3, &so[2722]},
    {"clearance", "clearance", NID_clearance, 4, &so[2725]},
    { NULL, NULL, NID_undef },
    {"ac-proxying", "ac-proxying", NID_ac_proxying, 8, &so[2729]},
    {"subjectInfoAccess", "Subject Information Access", NID_sinfo_access, 8, &so[2737]},
    {"id-aca-encAttrs", "id-aca-encAttrs", NID_id_aca_encAttrs, 8, &so[2745]},
    {"role", "role", NID_role, 3, &so[2753]},
    {"policyConstraints", "X509v3 Policy Constraints", NID_policy_constraints, 3, &so[2756]},
    {"targetInformation", "X509v3 AC Targeting", NID_target_information, 3, &so[2759]},
    {"noRevAvail", "X509v3 No Revocation Available", NID_no_rev_avail, 3, &so[2762]},
    {"NULL", "NULL", NID_ccitt},
    {"ansi-X9-62", "ANSI X9.62", NID_ansi_X9_62, 5, &so[2765]},
    {"prime-field", "prime-field", NID_X9_62_prime_field, 7, &so[2770]},
    {"characteristic-two-field", "characteristic-two-field", NID_X9_62_characteristic_two_field, 7, &so[2777]},
    {"id-ecPublicKey", "id-ecPublicKey", NID_X9_62_id_ecPublicKey, 7, &so[2784]},
    {"prime192v1", "prime192v1", NID_X9_62_prime192v1, 8, &so[2791]},
    {"prime192v2", "prime192v2", NID_X9_62_prime192v2, 8, &so[2799]},
    {"prime192v3", "prime192v3", NID_X9_62_prime192v3, 8, &so[2807]},
    {"prime239v1", "prime239v1", NID_X9_62_prime239v1, 8, &so[2815]},
    {"prime239v2", "prime239v2", NID_X9_62_prime239v2, 8, &so[2823]},
    {"prime239v3", "prime239v3", NID_X9_62_prime239v3, 8, &so[2831]},
    {"prime256v1", "prime256v1", NID_X9_62_prime256v1, 8, &so[2839]},
    {"ecdsa-with-SHA1", "ecdsa-with-SHA1", NID_ecdsa_with_SHA1, 7, &so[2847]},
    {"CSPName", "Microsoft CSP Name", NID_ms_csp_name, 9, &so[2854]},
    {"AES-128-ECB", "aes-128-ecb", NID_aes_128_ecb, 9, &so[2863]},
    {"AES-128-CBC", "aes-128-cbc", NID_aes_128_cbc, 9, &so[2872]},
    {"AES-128-OFB", "aes-128-ofb", NID_aes_128_ofb128, 9, &so[2881]},
    {"AES-128-CFB", "aes-128-cfb", NID_aes_128_cfb128, 9, &so[2890]},
    {"AES-192-ECB", "aes-192-ecb", NID_aes_192_ecb, 9, &so[2899]},
    {"AES-192-CBC", "aes-192-cbc", NID_aes_192_cbc, 9, &so[2908]},
    {"AES-192-OFB", "aes-192-ofb", NID_aes_192_ofb128, 9, &so[2917]},
    {"AES-192-CFB", "aes-192-cfb", NID_aes_192_cfb128, 9, &so[2926]},
    {"AES-256-ECB", "aes-256-ecb", NID_aes_256_ecb, 9, &so[2935]},
    {"AES-256-CBC", "aes-256-cbc", NID_aes_256_cbc, 9, &so[2944]},
    {"AES-256-OFB", "aes-256-ofb", NID_aes_256_ofb128, 9, &so[2953]},
    {"AES-256-CFB", "aes-256-cfb", NID_aes_256_cfb128, 9, &so[2962]},
    {"holdInstructionCode", "Hold Instruction Code", NID_hold_instruction_code, 3, &so[2971]},
    {"holdInstructionNone", "Hold Instruction None", NID_hold_instruction_none, 7, &so[2974]},
    {"holdInstructionCallIssuer", "Hold Instruction Call Issuer", NID_hold_instruction_call_issuer, 7, &so[2981]},
    {"holdInstructionReject", "Hold Instruction Reject", NID_hold_instruction_reject, 7, &so[2988]},
    {"data", "data", NID_data, 1, &so[2995]},
    {"pss", "pss", NID_pss, 3, &so[2996]},
    {"ucl", "ucl", NID_ucl, 7, &so[2999]},
    {"pilot", "pilot", NID_pilot, 8, &so[3006]},
    {"pilotAttributeType", "pilotAttributeType", NID_pilotAttributeType, 9, &so[3014]},
    {"pilotAttributeSyntax", "pilotAttributeSyntax", NID_pilotAttributeSyntax, 9, &so[3023]},
    {"pilotObjectClass", "pilotObjectClass", NID_pilotObjectClass, 9, &so[3032]},
    {"pilotGroups", "pilotGroups", NID_pilotGroups, 9, &so[3041]},
    {"iA5StringSyntax", "iA5StringSyntax", NID_iA5StringSyntax, 10, &so[3050]},
    {"caseIgnoreIA5StringSyntax", "caseIgnoreIA5StringSyntax", NID_caseIgnoreIA5StringSyntax, 10, &so[3060]},
    {"pilotObject", "pilotObject", NID_pilotObject, 10, &so[3070]},
    {"pilotPerson", "pilotPerson", NID_pilotPerson, 10, &so[3080]},
    {"account", "account", NID_account, 10, &so[3090]},
    {"document", "document", NID_document, 10, &so[3100]},
    {"room", "room", NID_room, 10, &so[3110]},
    {"documentSeries", "documentSeries", NID_documentSeries, 10, &so[3120]},
    {"rFC822localPart", "rFC822localPart", NID_rFC822localPart, 10, &so[3130]},
    {"dNSDomain", "dNSDomain", NID_dNSDomain, 10, &so[3140]},
    {"domainRelatedObject", "domainRelatedObject", NID_domainRelatedObject, 10, &so[3150]},
    {"friendlyCountry", "friendlyCountry", NID_friendlyCountry, 10, &so[3160]},
    {"simpleSecurityObject", "simpleSecurityObject", NID_simpleSecurityObject, 10, &so[3170]},
    {"pilotOrganization", "pilotOrganization", NID_pilotOrganization, 10, &so[3180]},
    {"pilotDSA", "pilotDSA", NID_pilotDSA, 10, &so[3190]},
    {"qualityLabelledData", "qualityLabelledData", NID_qualityLabelledData, 10, &so[3200]},
    {"UID", "userId", NID_userId, 10, &so[3210]},
    {"textEncodedORAddress", "textEncodedORAddress", NID_textEncodedORAddress, 10, &so[3220]},
    {"mail", "rfc822Mailbox", NID_rfc822Mailbox, 10, &so[3230]},
    {"info", "info", NID_info, 10, &so[3240]},
    {"favouriteDrink", "favouriteDrink", NID_favouriteDrink, 10, &so[3250]},
    {"roomNumber", "roomNumber", NID_roomNumber, 10, &so[3260]},
    {"photo", "photo", NID_photo, 10, &so[3270]},
    {"userClass", "userClass", NID_userClass, 10, &so[3280]},
    {"host", "host", NID_host, 10, &so[3290]},
    {"manager", "manager", NID_manager, 10, &so[3300]},
    {"documentIdentifier", "documentIdentifier", NID_documentIdentifier, 10, &so[3310]},
    {"documentTitle", "documentTitle", NID_documentTitle, 10, &so[3320]},
    {"documentVersion", "documentVersion", NID_documentVersion, 10, &so[3330]},
    {"documentAuthor", "documentAuthor", NID_documentAuthor, 10, &so[3340]},
    {"documentLocation", "documentLocation", NID_documentLocation, 10, &so[3350]},
    {"homeTelephoneNumber", "homeTelephoneNumber", NID_homeTelephoneNumber, 10, &so[3360]},
    {"secretary", "secretary", NID_secretary, 10, &so[3370]},
    {"otherMailbox", "otherMailbox", NID_otherMailbox, 10, &so[3380]},
    {"lastModifiedTime", "lastModifiedTime", NID_lastModifiedTime, 10, &so[3390]},
    {"lastModifiedBy", "lastModifiedBy", NID_lastModifiedBy, 10, &so[3400]},
    {"aRecord", "aRecord", NID_aRecord, 10, &so[3410]},
    {"pilotAttributeType27", "pilotAttributeType27", NID_pilotAttributeType27, 10, &so[3420]},
    {"mXRecord", "mXRecord", NID_mXRecord, 10, &so[3430]},
    {"nSRecord", "nSRecord", NID_nSRecord, 10, &so[3440]},
    {"sOARecord", "sOARecord", NID_sOARecord, 10, &so[3450]},
    {"cNAMERecord", "cNAMERecord", NID_cNAMERecord, 10, &so[3460]},
    {"associatedDomain", "associatedDomain", NID_associatedDomain, 10, &so[3470]},
    {"associatedName", "associatedName", NID_associatedName, 10, &so[3480]},
    {"homePostalAddress", "homePostalAddress", NID_homePostalAddress, 10, &so[3490]},
    {"personalTitle", "personalTitle", NID_personalTitle, 10, &so[3500]},
    {"mobileTelephoneNumber", "mobileTelephoneNumber", NID_mobileTelephoneNumber, 10, &so[3510]},
    {"pagerTelephoneNumber", "pagerTelephoneNumber", NID_pagerTelephoneNumber, 10, &so[3520]},
    {"friendlyCountryName", "friendlyCountryName", NID_friendlyCountryName, 10, &so[3530]},
    {"organizationalStatus", "organizationalStatus", NID_organizationalStatus, 10, &so[3540]},
    {"janetMailbox", "janetMailbox", NID_janetMailbox, 10, &so[3550]},
    {"mailPreferenceOption", "mailPreferenceOption", NID_mailPreferenceOption, 10, &so[3560]},
    {"buildingName", "buildingName", NID_buildingName, 10, &so[3570]},
    {"dSAQuality", "dSAQuality", NID_dSAQuality, 10, &so[3580]},
    {"singleLevelQuality", "singleLevelQuality", NID_singleLevelQuality, 10, &so[3590]},
    {"subtreeMinimumQuality", "subtreeMinimumQuality", NID_subtreeMinimumQuality, 10, &so[3600]},
    {"subtreeMaximumQuality", "subtreeMaximumQuality", NID_subtreeMaximumQuality, 10, &so[3610]},
    {"personalSignature", "personalSignature", NID_personalSignature, 10, &so[3620]},
    {"dITRedirect", "dITRedirect", NID_dITRedirect, 10, &so[3630]},
    {"audio", "audio", NID_audio, 10, &so[3640]},
    {"documentPublisher", "documentPublisher", NID_documentPublisher, 10, &so[3650]},
    {"x500UniqueIdentifier", "x500UniqueIdentifier", NID_x500UniqueIdentifier, 3, &so[3660]},
    {"mime-mhs", "MIME MHS", NID_mime_mhs, 5, &so[3663]},
    {"mime-mhs-headings", "mime-mhs-headings", NID_mime_mhs_headings, 6, &so[3668]},
    {"mime-mhs-bodies", "mime-mhs-bodies", NID_mime_mhs_bodies, 6, &so[3674]},
    {"id-hex-partial-message", "id-hex-partial-message", NID_id_hex_partial_message, 7, &so[3680]},
    {"id-hex-multipart-message", "id-hex-multipart-message", NID_id_hex_multipart_message, 7, &so[3687]},
    {"generationQualifier", "generationQualifier", NID_generationQualifier, 3, &so[3694]},
    {"pseudonym", "pseudonym", NID_pseudonym, 3, &so[3697]},
    { NULL, NULL, NID_undef },
    {"id-set", "Secure Electronic Transactions", NID_id_set, 2, &so[3700]},
    {"set-ctype", "content types", NID_set_ctype, 3, &so[3702]},
    {"set-msgExt", "message extensions", NID_set_msgExt, 3, &so[3705]},
    {"set-attr", "set-attr", NID_set_attr, 3, &so[3708]},
    {"set-policy", "set-policy", NID_set_policy, 3, &so[3711]},
    {"set-certExt", "certificate extensions", NID_set_certExt, 3, &so[3714]},
    {"set-brand", "set-brand", NID_set_brand, 3, &so[3717]},
    {"setct-PANData", "setct-PANData", NID_setct_PANData, 4, &so[3720]},
    {"setct-PANToken", "setct-PANToken", NID_setct_PANToken, 4, &so[3724]},
    {"setct-PANOnly", "setct-PANOnly", NID_setct_PANOnly, 4, &so[3728]},
    {"setct-OIData", "setct-OIData", NID_setct_OIData, 4, &so[3732]},
    {"setct-PI", "setct-PI", NID_setct_PI, 4, &so[3736]},
    {"setct-PIData", "setct-PIData", NID_setct_PIData, 4, &so[3740]},
    {"setct-PIDataUnsigned", "setct-PIDataUnsigned", NID_setct_PIDataUnsigned, 4, &so[3744]},
    {"setct-HODInput", "setct-HODInput", NID_setct_HODInput, 4, &so[3748]},
    {"setct-AuthResBaggage", "setct-AuthResBaggage", NID_setct_AuthResBaggage, 4, &so[3752]},
    {"setct-AuthRevReqBaggage", "setct-AuthRevReqBaggage", NID_setct_AuthRevReqBaggage, 4, &so[3756]},
    {"setct-AuthRevResBaggage", "setct-AuthRevResBaggage", NID_setct_AuthRevResBaggage, 4, &so[3760]},
    {"setct-CapTokenSeq", "setct-CapTokenSeq", NID_setct_CapTokenSeq, 4, &so[3764]},
    {"setct-PInitResData", "setct-PInitResData", NID_setct_PInitResData, 4, &so[3768]},
    {"setct-PI-TBS", "setct-PI-TBS", NID_setct_PI_TBS, 4, &so[3772]},
    {"setct-PResData", "setct-PResData", NID_setct_PResData, 4, &so[3776]},
    {"setct-AuthReqTBS", "setct-AuthReqTBS", NID_setct_AuthReqTBS, 4, &so[3780]},
    {"setct-AuthResTBS", "setct-AuthResTBS", NID_setct_AuthResTBS, 4, &so[3784]},
    {"setct-AuthResTBSX", "setct-AuthResTBSX", NID_setct_AuthResTBSX, 4, &so[3788]},
    {"setct-AuthTokenTBS", "setct-AuthTokenTBS", NID_setct_AuthTokenTBS, 4, &so[3792]},
    {"setct-CapTokenData", "setct-CapTokenData", NID_setct_CapTokenData, 4, &so[3796]},
    {"setct-CapTokenTBS", "setct-CapTokenTBS", NID_setct_CapTokenTBS, 4, &so[3800]},
    {"setct-AcqCardCodeMsg", "setct-AcqCardCodeMsg", NID_setct_AcqCardCodeMsg, 4, &so[3804]},
    {"setct-AuthRevReqTBS", "setct-AuthRevReqTBS", NID_setct_AuthRevReqTBS, 4, &so[3808]},
    {"setct-AuthRevResData", "setct-AuthRevResData", NID_setct_AuthRevResData, 4, &so[3812]},
    {"setct-AuthRevResTBS", "setct-AuthRevResTBS", NID_setct_AuthRevResTBS, 4, &so[3816]},
    {"setct-CapReqTBS", "setct-CapReqTBS", NID_setct_CapReqTBS, 4, &so[3820]},
    {"setct-CapReqTBSX", "setct-CapReqTBSX", NID_setct_CapReqTBSX, 4, &so[3824]},
    {"setct-CapResData", "setct-CapResData", NID_setct_CapResData, 4, &so[3828]},
    {"setct-CapRevReqTBS", "setct-CapRevReqTBS", NID_setct_CapRevReqTBS, 4, &so[3832]},
    {"setct-CapRevReqTBSX", "setct-CapRevReqTBSX", NID_setct_CapRevReqTBSX, 4, &so[3836]},
    {"setct-CapRevResData", "setct-CapRevResData", NID_setct_CapRevResData, 4, &so[3840]},
    {"setct-CredReqTBS", "setct-CredReqTBS", NID_setct_CredReqTBS, 4, &so[3844]},
    {"setct-CredReqTBSX", "setct-CredReqTBSX", NID_setct_CredReqTBSX, 4, &so[3848]},
    {"setct-CredResData", "setct-CredResData", NID_setct_CredResData, 4, &so[3852]},
    {"setct-CredRevReqTBS", "setct-CredRevReqTBS", NID_setct_CredRevReqTBS, 4, &so[3856]},
    {"setct-CredRevReqTBSX", "setct-CredRevReqTBSX", NID_setct_CredRevReqTBSX, 4, &so[3860]},
    {"setct-CredRevResData", "setct-CredRevResData", NID_setct_CredRevResData, 4, &so[3864]},
    {"setct-PCertReqData", "setct-PCertReqData", NID_setct_PCertReqData, 4, &so[3868]},
    {"setct-PCertResTBS", "setct-PCertResTBS", NID_setct_PCertResTBS, 4, &so[3872]},
    {"setct-BatchAdminReqData", "setct-BatchAdminReqData", NID_setct_BatchAdminReqData, 4, &so[3876]},
    {"setct-BatchAdminResData", "setct-BatchAdminResData", NID_setct_BatchAdminResData, 4, &so[3880]},
    {"setct-CardCInitResTBS", "setct-CardCInitResTBS", NID_setct_CardCInitResTBS, 4, &so[3884]},
    {"setct-MeAqCInitResTBS", "setct-MeAqCInitResTBS", NID_setct_MeAqCInitResTBS, 4, &so[3888]},
    {"setct-RegFormResTBS", "setct-RegFormResTBS", NID_setct_RegFormResTBS, 4, &so[3892]},
    {"setct-CertReqData", "setct-CertReqData", NID_setct_CertReqData, 4, &so[3896]},
    {"setct-CertReqTBS", "setct-CertReqTBS", NID_setct_CertReqTBS, 4, &so[3900]},
    {"setct-CertResData", "setct-CertResData", NID_setct_CertResData, 4, &so[3904]},
    {"setct-CertInqReqTBS", "setct-CertInqReqTBS", NID_setct_CertInqReqTBS, 4, &so[3908]},
    {"setct-ErrorTBS", "setct-ErrorTBS", NID_setct_ErrorTBS, 4, &so[3912]},
    {"setct-PIDualSignedTBE", "setct-PIDualSignedTBE", NID_setct_PIDualSignedTBE, 4, &so[3916]},
    {"setct-PIUnsignedTBE", "setct-PIUnsignedTBE", NID_setct_PIUnsignedTBE, 4, &so[3920]},
    {"setct-AuthReqTBE", "setct-AuthReqTBE", NID_setct_AuthReqTBE, 4, &so[3924]},
    {"setct-AuthResTBE", "setct-AuthResTBE", NID_setct_AuthResTBE, 4, &so[3928]},
    {"setct-AuthResTBEX", "setct-AuthResTBEX", NID_setct_AuthResTBEX, 4, &so[3932]},
    {"setct-AuthTokenTBE", "setct-AuthTokenTBE", NID_setct_AuthTokenTBE, 4, &so[3936]},
    {"setct-CapTokenTBE", "setct-CapTokenTBE", NID_setct_CapTokenTBE, 4, &so[3940]},
    {"setct-CapTokenTBEX", "setct-CapTokenTBEX", NID_setct_CapTokenTBEX, 4, &so[3944]},
    {"setct-AcqCardCodeMsgTBE", "setct-AcqCardCodeMsgTBE", NID_setct_AcqCardCodeMsgTBE, 4, &so[3948]},
    {"setct-AuthRevReqTBE", "setct-AuthRevReqTBE", NID_setct_AuthRevReqTBE, 4, &so[3952]},
    {"setct-AuthRevResTBE", "setct-AuthRevResTBE", NID_setct_AuthRevResTBE, 4, &so[3956]},
    {"setct-AuthRevResTBEB", "setct-AuthRevResTBEB", NID_setct_AuthRevResTBEB, 4, &so[3960]},
    {"setct-CapReqTBE", "setct-CapReqTBE", NID_setct_CapReqTBE, 4, &so[3964]},
    {"setct-CapReqTBEX", "setct-CapReqTBEX", NID_setct_CapReqTBEX, 4, &so[3968]},
    {"setct-CapResTBE", "setct-CapResTBE", NID_setct_CapResTBE, 4, &so[3972]},
    {"setct-CapRevReqTBE", "setct-CapRevReqTBE", NID_setct_CapRevReqTBE, 4, &so[3976]},
    {"setct-CapRevReqTBEX", "setct-CapRevReqTBEX", NID_setct_CapRevReqTBEX, 4, &so[3980]},
    {"setct-CapRevResTBE", "setct-CapRevResTBE", NID_setct_CapRevResTBE, 4, &so[3984]},
    {"setct-CredReqTBE", "setct-CredReqTBE", NID_setct_CredReqTBE, 4, &so[3988]},
    {"setct-CredReqTBEX", "setct-CredReqTBEX", NID_setct_CredReqTBEX, 4, &so[3992]},
    {"setct-CredResTBE", "setct-CredResTBE", NID_setct_CredResTBE, 4, &so[3996]},
    {"setct-CredRevReqTBE", "setct-CredRevReqTBE", NID_setct_CredRevReqTBE, 4, &so[4000]},
    {"setct-CredRevReqTBEX", "setct-CredRevReqTBEX", NID_setct_CredRevReqTBEX, 4, &so[4004]},
    {"setct-CredRevResTBE", "setct-CredRevResTBE", NID_setct_CredRevResTBE, 4, &so[4008]},
    {"setct-BatchAdminReqTBE", "setct-BatchAdminReqTBE", NID_setct_BatchAdminReqTBE, 4, &so[4012]},
    {"setct-BatchAdminResTBE", "setct-BatchAdminResTBE", NID_setct_BatchAdminResTBE, 4, &so[4016]},
    {"setct-RegFormReqTBE", "setct-RegFormReqTBE", NID_setct_RegFormReqTBE, 4, &so[4020]},
    {"setct-CertReqTBE", "setct-CertReqTBE", NID_setct_CertReqTBE, 4, &so[4024]},
    {"setct-CertReqTBEX", "setct-CertReqTBEX", NID_setct_CertReqTBEX, 4, &so[4028]},
    {"setct-CertResTBE", "setct-CertResTBE", NID_setct_CertResTBE, 4, &so[4032]},
    {"setct-CRLNotificationTBS", "setct-CRLNotificationTBS", NID_setct_CRLNotificationTBS, 4, &so[4036]},
    {"setct-CRLNotificationResTBS", "setct-CRLNotificationResTBS", NID_setct_CRLNotificationResTBS, 4, &so[4040]},
    {"setct-BCIDistributionTBS", "setct-BCIDistributionTBS", NID_setct_BCIDistributionTBS, 4, &so[4044]},
    {"setext-genCrypt", "generic cryptogram", NID_setext_genCrypt, 4, &so[4048]},
    {"setext-miAuth", "merchant initiated auth", NID_setext_miAuth, 4, &so[4052]},
    {"setext-pinSecure", "setext-pinSecure", NID_setext_pinSecure, 4, &so[4056]},
    {"setext-pinAny", "setext-pinAny", NID_setext_pinAny, 4, &so[4060]},
    {"setext-track2", "setext-track2", NID_setext_track2, 4, &so[4064]},
    {"setext-cv", "additional verification", NID_setext_cv, 4, &so[4068]},
    {"set-policy-root", "set-policy-root", NID_set_policy_root, 4, &so[4072]},
    {"setCext-hashedRoot", "setCext-hashedRoot", NID_setCext_hashedRoot, 4, &so[4076]},
    {"setCext-certType", "setCext-certType", NID_setCext_certType, 4, &so[4080]},
    {"setCext-merchData", "setCext-merchData", NID_setCext_merchData, 4, &so[4084]},
    {"setCext-cCertRequired", "setCext-cCertRequired", NID_setCext_cCertRequired, 4, &so[4088]},
    {"setCext-tunneling", "setCext-tunneling", NID_setCext_tunneling, 4, &so[4092]},
    {"setCext-setExt", "setCext-setExt", NID_setCext_setExt, 4, &so[4096]},
    {"setCext-setQualf", "setCext-setQualf", NID_setCext_setQualf, 4, &so[4100]},
    {"setCext-PGWYcapabilities", "setCext-PGWYcapabilities", NID_setCext_PGWYcapabilities, 4, &so[4104]},
    {"setCext-TokenIdentifier", "setCext-TokenIdentifier", NID_setCext_TokenIdentifier, 4, &so[4108]},
    {"setCext-Track2Data", "setCext-Track2Data", NID_setCext_Track2Data, 4, &so[4112]},
    {"setCext-TokenType", "setCext-TokenType", NID_setCext_TokenType, 4, &so[4116]},
    {"setCext-IssuerCapabilities", "setCext-IssuerCapabilities", NID_setCext_IssuerCapabilities, 4, &so[4120]},
    {"setAttr-Cert", "setAttr-Cert", NID_setAttr_Cert, 4, &so[4124]},
    {"setAttr-PGWYcap", "payment gateway capabilities", NID_setAttr_PGWYcap, 4, &so[4128]},
    {"setAttr-TokenType", "setAttr-TokenType", NID_setAttr_TokenType, 4, &so[4132]},
    {"setAttr-IssCap", "issuer capabilities", NID_setAttr_IssCap, 4, &so[4136]},
    {"set-rootKeyThumb", "set-rootKeyThumb", NID_set_rootKeyThumb, 5, &so[4140]},
    {"set-addPolicy", "set-addPolicy", NID_set_addPolicy, 5, &so[4145]},
    {"setAttr-Token-EMV", "setAttr-Token-EMV", NID_setAttr_Token_EMV, 5, &so[4150]},
    {"setAttr-Token-B0Prime", "setAttr-Token-B0Prime", NID_setAttr_Token_B0Prime, 5, &so[4155]},
    {"setAttr-IssCap-CVM", "setAttr-IssCap-CVM", NID_setAttr_IssCap_CVM, 5, &so[4160]},
    {"setAttr-IssCap-T2", "setAttr-IssCap-T2", NID_setAttr_IssCap_T2, 5, &so[4165]},
    {"setAttr-IssCap-Sig", "setAttr-IssCap-Sig", NID_setAttr_IssCap_Sig, 5, &so[4170]},
    {"setAttr-GenCryptgrm", "generate cryptogram", NID_setAttr_GenCryptgrm, 6, &so[4175]},
    {"setAttr-T2Enc", "encrypted track 2", NID_setAttr_T2Enc, 6, &so[4181]},
    {"setAttr-T2cleartxt", "cleartext track 2", NID_setAttr_T2cleartxt, 6, &so[4187]},
    {"setAttr-TokICCsig", "ICC or token signature", NID_setAttr_TokICCsig, 6, &so[4193]},
    {"setAttr-SecDevSig", "secure device signature", NID_setAttr_SecDevSig, 6, &so[4199]},
    {"set-brand-IATA-ATA", "set-brand-IATA-ATA", NID_set_brand_IATA_ATA, 4, &so[4205]},
    {"set-brand-Diners", "set-brand-Diners", NID_set_brand_Diners, 4, &so[4209]},
    {"set-brand-AmericanExpress", "set-brand-AmericanExpress", NID_set_brand_AmericanExpress, 4, &so[4213]},
    {"set-brand-JCB", "set-brand-JCB", NID_set_brand_JCB, 4, &so[4217]},
    {"set-brand-Visa", "set-brand-Visa", NID_set_brand_Visa, 4, &so[4221]},
    {"set-brand-MasterCard", "set-brand-MasterCard", NID_set_brand_MasterCard, 4, &so[4225]},
    {"set-brand-Novus", "set-brand-Novus", NID_set_brand_Novus, 5, &so[4229]},
    {"DES-CDMF", "des-cdmf", NID_des_cdmf, 8, &so[4234]},
    {"rsaOAEPEncryptionSET", "rsaOAEPEncryptionSET", NID_rsaOAEPEncryptionSET, 9, &so[4242]},
    {"ITU-T", "itu-t", NID_itu_t},
    {"JOINT-ISO-ITU-T", "joint-iso-itu-t", NID_joint_iso_itu_t},
    {"international-organizations", "International Organizations", NID_international_organizations, 1, &so[4251]},
    {"msSmartcardLogin", "Microsoft Smartcard Login", NID_ms_smartcard_login, 10, &so[4252]},
    {"msUPN", "Microsoft User Principal Name", NID_ms_upn, 10, &so[4262]},
    {"AES-128-CFB1", "aes-128-cfb1", NID_aes_128_cfb1},
    {"AES-192-CFB1", "aes-192-cfb1", NID_aes_192_cfb1},
    {"AES-256-CFB1", "aes-256-cfb1", NID_aes_256_cfb1},
    {"AES-128-CFB8", "aes-128-cfb8", NID_aes_128_cfb8},
    {"AES-192-CFB8", "aes-192-cfb8", NID_aes_192_cfb8},
    {"AES-256-CFB8", "aes-256-cfb8", NID_aes_256_cfb8},
    {"DES-CFB1", "des-cfb1", NID_des_cfb1},
    {"DES-CFB8", "des-cfb8", NID_des_cfb8},
    {"DES-EDE3-CFB1", "des-ede3-cfb1", NID_des_ede3_cfb1},
    {"DES-EDE3-CFB8", "des-ede3-cfb8", NID_des_ede3_cfb8},
    {"street", "streetAddress", NID_streetAddress, 3, &so[4272]},
    {"postalCode", "postalCode", NID_postalCode, 3, &so[4275]},
    {"id-ppl", "id-ppl", NID_id_ppl, 7, &so[4278]},
    {"proxyCertInfo", "Proxy Certificate Information", NID_proxyCertInfo, 8, &so[4285]},
    {"id-ppl-anyLanguage", "Any language", NID_id_ppl_anyLanguage, 8, &so[4293]},
    {"id-ppl-inheritAll", "Inherit all", NID_id_ppl_inheritAll, 8, &so[4301]},
    {"nameConstraints", "X509v3 Name Constraints", NID_name_constraints, 3, &so[4309]},
    {"id-ppl-independent", "Independent", NID_Independent, 8, &so[4312]},
    {"RSA-SHA256", "sha256WithRSAEncryption", NID_sha256WithRSAEncryption, 9, &so[4320]},
    {"RSA-SHA384", "sha384WithRSAEncryption", NID_sha384WithRSAEncryption, 9, &so[4329]},
    {"RSA-SHA512", "sha512WithRSAEncryption", NID_sha512WithRSAEncryption, 9, &so[4338]},
    {"RSA-SHA224", "sha224WithRSAEncryption", NID_sha224WithRSAEncryption, 9, &so[4347]},
    {"SHA256", "sha256", NID_sha256, 9, &so[4356]},
    {"SHA384", "sha384", NID_sha384, 9, &so[4365]},
    {"SHA512", "sha512", NID_sha512, 9, &so[4374]},
    {"SHA224", "sha224", NID_sha224, 9, &so[4383]},
    {"identified-organization", "identified-organization", NID_identified_organization, 1, &so[4392]},
    {"certicom-arc", "certicom-arc", NID_certicom_arc, 3, &so[4393]},
    {"wap", "wap", NID_wap, 2, &so[4396]},
    {"wap-wsg", "wap-wsg", NID_wap_wsg, 3, &so[4398]},
    {"id-characteristic-two-basis", "id-characteristic-two-basis", NID_X9_62_id_characteristic_two_basis, 8, &so[4401]},
    {"onBasis", "onBasis", NID_X9_62_onBasis, 9, &so[4409]},
    {"tpBasis", "tpBasis", NID_X9_62_tpBasis, 9, &so[4418]},
    {"ppBasis", "ppBasis", NID_X9_62_ppBasis, 9, &so[4427]},
    {"c2pnb163v1", "c2pnb163v1", NID_X9_62_c2pnb163v1, 8, &so[4436]},
    {"c2pnb163v2", "c2pnb163v2", NID_X9_62_c2pnb163v2, 8, &so[4444]},
    {"c2pnb163v3", "c2pnb163v3", NID_X9_62_c2pnb163v3, 8, &so[4452]},
    {"c2pnb176v1", "c2pnb176v1", NID_X9_62_c2pnb176v1, 8, &so[4460]},
    {"c2tnb191v1", "c2tnb191v1", NID_X9_62_c2tnb191v1, 8, &so[4468]},
    {"c2tnb191v2", "c2tnb191v2", NID_X9_62_c2tnb191v2, 8, &so[4476]},
    {"c2tnb191v3", "c2tnb191v3", NID_X9_62_c2tnb191v3, 8, &so[4484]},
    {"c2onb191v4", "c2onb191v4", NID_X9_62_c2onb191v4, 8, &so[4492]},
    {"c2onb191v5", "c2onb191v5", NID_X9_62_c2onb191v5, 8, &so[4500]},
    {"c2pnb208w1", "c2pnb208w1", NID_X9_62_c2pnb208w1, 8, &so[4508]},
    {"c2tnb239v1", "c2tnb239v1", NID_X9_62_c2tnb239v1, 8, &so[4516]},
    {"c2tnb239v2", "c2tnb239v2", NID_X9_62_c2tnb239v2, 8, &so[4524]},
    {"c2tnb239v3", "c2tnb239v3", NID_X9_62_c2tnb239v3, 8, &so[4532]},
    {"c2onb239v4", "c2onb239v4", NID_X9_62_c2onb239v4, 8, &so[4540]},
    {"c2onb239v5", "c2onb239v5", NID_X9_62_c2onb239v5, 8, &so[4548]},
    {"c2pnb272w1", "c2pnb272w1", NID_X9_62_c2pnb272w1, 8, &so[4556]},
    {"c2pnb304w1", "c2pnb304w1", NID_X9_62_c2pnb304w1, 8, &so[4564]},
    {"c2tnb359v1", "c2tnb359v1", NID_X9_62_c2tnb359v1, 8, &so[4572]},
    {"c2pnb368w1", "c2pnb368w1", NID_X9_62_c2pnb368w1, 8, &so[4580]},
    {"c2tnb431r1", "c2tnb431r1", NID_X9_62_c2tnb431r1, 8, &so[4588]},
    {"secp112r1", "secp112r1", NID_secp112r1, 5, &so[4596]},
    {"secp112r2", "secp112r2", NID_secp112r2, 5, &so[4601]},
    {"secp128r1", "secp128r1", NID_secp128r1, 5, &so[4606]},
    {"secp128r2", "secp128r2", NID_secp128r2, 5, &so[4611]},
    {"secp160k1", "secp160k1", NID_secp160k1, 5, &so[4616]},
    {"secp160r1", "secp160r1", NID_secp160r1, 5, &so[4621]},
    {"secp160r2", "secp160r2", NID_secp160r2, 5, &so[4626]},
    {"secp192k1", "secp192k1", NID_secp192k1, 5, &so[4631]},
    {"secp224k1", "secp224k1", NID_secp224k1, 5, &so[4636]},
    {"secp224r1", "secp224r1", NID_secp224r1, 5, &so[4641]},
    {"secp256k1", "secp256k1", NID_secp256k1, 5, &so[4646]},
    {"secp384r1", "secp384r1", NID_secp384r1, 5, &so[4651]},
    {"secp521r1", "secp521r1", NID_secp521r1, 5, &so[4656]},
    {"sect113r1", "sect113r1", NID_sect113r1, 5, &so[4661]},
    {"sect113r2", "sect113r2", NID_sect113r2, 5, &so[4666]},
    {"sect131r1", "sect131r1", NID_sect131r1, 5, &so[4671]},
    {"sect131r2", "sect131r2", NID_sect131r2, 5, &so[4676]},
    {"sect163k1", "sect163k1", NID_sect163k1, 5, &so[4681]},
    {"sect163r1", "sect163r1", NID_sect163r1, 5, &so[4686]},
    {"sect163r2", "sect163r2", NID_sect163r2, 5, &so[4691]},
    {"sect193r1", "sect193r1", NID_sect193r1, 5, &so[4696]},
    {"sect193r2", "sect193r2", NID_sect193r2, 5, &so[4701]},
    {"sect233k1", "sect233k1", NID_sect233k1, 5, &so[4706]},
    {"sect233r1", "sect233r1", NID_sect233r1, 5, &so[4711]},
    {"sect239k1", "sect239k1", NID_sect239k1, 5, &so[4716]},
    {"sect283k1", "sect283k1", NID_sect283k1, 5, &so[4721]},
    {"sect283r1", "sect283r1", NID_sect283r1, 5, &so[4726]},
    {"sect409k1", "sect409k1", NID_sect409k1, 5, &so[4731]},
    {"sect409r1", "sect409r1", NID_sect409r1, 5, &so[4736]},
    {"sect571k1", "sect571k1", NID_sect571k1, 5, &so[4741]},
    {"sect571r1", "sect571r1", NID_sect571r1, 5, &so[4746]},
    {"wap-wsg-idm-ecid-wtls1", "wap-wsg-idm-ecid-wtls1", NID_wap_wsg_idm_ecid_wtls1, 5, &so[4751]},
    {"wap-wsg-idm-ecid-wtls3", "wap-wsg-idm-ecid-wtls3", NID_wap_wsg_idm_ecid_wtls3, 5, &so[4756]},
    {"wap-wsg-idm-ecid-wtls4", "wap-wsg-idm-ecid-wtls4", NID_wap_wsg_idm_ecid_wtls4, 5, &so[4761]},
    {"wap-wsg-idm-ecid-wtls5", "wap-wsg-idm-ecid-wtls5", NID_wap_wsg_idm_ecid_wtls5, 5, &so[4766]},
    {"wap-wsg-idm-ecid-wtls6", "wap-wsg-idm-ecid-wtls6", NID_wap_wsg_idm_ecid_wtls6, 5, &so[4771]},
    {"wap-wsg-idm-ecid-wtls7", "wap-wsg-idm-ecid-wtls7", NID_wap_wsg_idm_ecid_wtls7, 5, &so[4776]},
    {"wap-wsg-idm-ecid-wtls8", "wap-wsg-idm-ecid-wtls8", NID_wap_wsg_idm_ecid_wtls8, 5, &so[4781]},
    {"wap-wsg-idm-ecid-wtls9", "wap-wsg-idm-ecid-wtls9", NID_wap_wsg_idm_ecid_wtls9, 5, &so[4786]},
    {"wap-wsg-idm-ecid-wtls10", "wap-wsg-idm-ecid-wtls10", NID_wap_wsg_idm_ecid_wtls10, 5, &so[4791]},
    {"wap-wsg-idm-ecid-wtls11", "wap-wsg-idm-ecid-wtls11", NID_wap_wsg_idm_ecid_wtls11, 5, &so[4796]},
    {"wap-wsg-idm-ecid-wtls12", "wap-wsg-idm-ecid-wtls12", NID_wap_wsg_idm_ecid_wtls12, 5, &so[4801]},
    {"anyPolicy", "X509v3 Any Policy", NID_any_policy, 4, &so[4806]},
    {"policyMappings", "X509v3 Policy Mappings", NID_policy_mappings, 3, &so[4810]},
    {"inhibitAnyPolicy", "X509v3 Inhibit Any Policy", NID_inhibit_any_policy, 3, &so[4813]},
    {"Oakley-EC2N-3", "ipsec3", NID_ipsec3},
    {"Oakley-EC2N-4", "ipsec4", NID_ipsec4},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"subjectDirectoryAttributes", "X509v3 Subject Directory Attributes", NID_subject_directory_attributes, 3, &so[4816]},
    {"issuingDistributionPoint", "X509v3 Issuing Distribution Point", NID_issuing_distribution_point, 3, &so[4819]},
    {"certificateIssuer", "X509v3 Certificate Issuer", NID_certificate_issuer, 3, &so[4822]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"HMAC-MD5", "hmac-md5", NID_hmac_md5, 8, &so[4825]},
    {"HMAC-SHA1", "hmac-sha1", NID_hmac_sha1, 8, &so[4833]},
    {"id-PasswordBasedMAC", "password based MAC", NID_id_PasswordBasedMAC, 9, &so[4841]},
    {"id-DHBasedMac", "Diffie-Hellman based MAC", NID_id_DHBasedMac, 9, &so[4850]},
    {"id-it-suppLangTags", "id-it-suppLangTags", NID_id_it_suppLangTags, 8, &so[4859]},
    {"caRepository", "CA Repository", NID_caRepository, 8, &so[4867]},
    {"id-smime-ct-compressedData", "id-smime-ct-compressedData", NID_id_smime_ct_compressedData, 11, &so[4875]},
    {"id-ct-asciiTextWithCRLF", "id-ct-asciiTextWithCRLF", NID_id_ct_asciiTextWithCRLF, 11, &so[4886]},
    {"id-aes128-wrap", "id-aes128-wrap", NID_id_aes128_wrap, 9, &so[4897]},
    {"id-aes192-wrap", "id-aes192-wrap", NID_id_aes192_wrap, 9, &so[4906]},
    {"id-aes256-wrap", "id-aes256-wrap", NID_id_aes256_wrap, 9, &so[4915]},
    {"ecdsa-with-Recommended", "ecdsa-with-Recommended", NID_ecdsa_with_Recommended, 7, &so[4924]},
    {"ecdsa-with-Specified", "ecdsa-with-Specified", NID_ecdsa_with_Specified, 7, &so[4931]},
    {"ecdsa-with-SHA224", "ecdsa-with-SHA224", NID_ecdsa_with_SHA224, 8, &so[4938]},
    {"ecdsa-with-SHA256", "ecdsa-with-SHA256", NID_ecdsa_with_SHA256, 8, &so[4946]},
    {"ecdsa-with-SHA384", "ecdsa-with-SHA384", NID_ecdsa_with_SHA384, 8, &so[4954]},
    {"ecdsa-with-SHA512", "ecdsa-with-SHA512", NID_ecdsa_with_SHA512, 8, &so[4962]},
    {"hmacWithMD5", "hmacWithMD5", NID_hmacWithMD5, 8, &so[4970]},
    {"hmacWithSHA224", "hmacWithSHA224", NID_hmacWithSHA224, 8, &so[4978]},
    {"hmacWithSHA256", "hmacWithSHA256", NID_hmacWithSHA256, 8, &so[4986]},
    {"hmacWithSHA384", "hmacWithSHA384", NID_hmacWithSHA384, 8, &so[4994]},
    {"hmacWithSHA512", "hmacWithSHA512", NID_hmacWithSHA512, 8, &so[5002]},
    {"dsa_with_SHA224", "dsa_with_SHA224", NID_dsa_with_SHA224, 9, &so[5010]},
    {"dsa_with_SHA256", "dsa_with_SHA256", NID_dsa_with_SHA256, 9, &so[5019]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"HMAC", "hmac", NID_hmac},
    {"LocalKeySet", "Microsoft Local Key set", NID_LocalKeySet, 9, &so[5028]},
    {"freshestCRL", "X509v3 Freshest CRL", NID_freshest_crl, 3, &so[5037]},
    {"id-on-permanentIdentifier", "Permanent Identifier", NID_id_on_permanentIdentifier, 8, &so[5040]},
    {"searchGuide", "searchGuide", NID_searchGuide, 3, &so[5048]},
    {"businessCategory", "businessCategory", NID_businessCategory, 3, &so[5051]},
    {"postalAddress", "postalAddress", NID_postalAddress, 3, &so[5054]},
    {"postOfficeBox", "postOfficeBox", NID_postOfficeBox, 3, &so[5057]},
    {"physicalDeliveryOfficeName", "physicalDeliveryOfficeName", NID_physicalDeliveryOfficeName, 3, &so[5060]},
    {"telephoneNumber", "telephoneNumber", NID_telephoneNumber, 3, &so[5063]},
    {"telexNumber", "telexNumber", NID_telexNumber, 3, &so[5066]},
    {"teletexTerminalIdentifier", "teletexTerminalIdentifier", NID_teletexTerminalIdentifier, 3, &so[5069]},
    {"facsimileTelephoneNumber", "facsimileTelephoneNumber", NID_facsimileTelephoneNumber, 3, &so[5072]},
    {"x121Address", "x121Address", NID_x121Address, 3, &so[5075]},
    {"internationaliSDNNumber", "internationaliSDNNumber", NID_internationaliSDNNumber, 3, &so[5078]},
    {"registeredAddress", "registeredAddress", NID_registeredAddress, 3, &so[5081]},
    {"destinationIndicator", "destinationIndicator", NID_destinationIndicator, 3, &so[5084]},
    {"preferredDeliveryMethod", "preferredDeliveryMethod", NID_preferredDeliveryMethod, 3, &so[5087]},
    {"presentationAddress", "presentationAddress", NID_presentationAddress, 3, &so[5090]},
    {"supportedApplicationContext", "supportedApplicationContext", NID_supportedApplicationContext, 3, &so[5093]},
    {"member", "member", NID_member, 3, &so[5096]},
    {"owner", "owner", NID_owner, 3, &so[5099]},
    {"roleOccupant", "roleOccupant", NID_roleOccupant, 3, &so[5102]},
    {"seeAlso", "seeAlso", NID_seeAlso, 3, &so[5105]},
    {"userPassword", "userPassword", NID_userPassword, 3, &so[5108]},
    {"userCertificate", "userCertificate", NID_userCertificate, 3, &so[5111]},
    {"cACertificate", "cACertificate", NID_cACertificate, 3, &so[5114]},
    {"authorityRevocationList", "authorityRevocationList", NID_authorityRevocationList, 3, &so[5117]},
    {"certificateRevocationList", "certificateRevocationList", NID_certificateRevocationList, 3, &so[5120]},
    {"crossCertificatePair", "crossCertificatePair", NID_crossCertificatePair, 3, &so[5123]},
    {"enhancedSearchGuide", "enhancedSearchGuide", NID_enhancedSearchGuide, 3, &so[5126]},
    {"protocolInformation", "protocolInformation", NID_protocolInformation, 3, &so[5129]},
    {"distinguishedName", "distinguishedName", NID_distinguishedName, 3, &so[5132]},
    {"uniqueMember", "uniqueMember", NID_uniqueMember, 3, &so[5135]},
    {"houseIdentifier", "houseIdentifier", NID_houseIdentifier, 3, &so[5138]},
    {"supportedAlgorithms", "supportedAlgorithms", NID_supportedAlgorithms, 3, &so[5141]},
    {"deltaRevocationList", "deltaRevocationList", NID_deltaRevocationList, 3, &so[5144]},
    {"dmdName", "dmdName", NID_dmdName, 3, &so[5147]},
    {"id-alg-PWRI-KEK", "id-alg-PWRI-KEK", NID_id_alg_PWRI_KEK, 11, &so[5150]},
    {"CMAC", "cmac", NID_cmac},
    {"id-aes128-GCM", "aes-128-gcm", NID_aes_128_gcm, 9, &so[5161]},
    {"id-aes128-CCM", "aes-128-ccm", NID_aes_128_ccm, 9, &so[5170]},
    {"id-aes128-wrap-pad", "id-aes128-wrap-pad", NID_id_aes128_wrap_pad, 9, &so[5179]},
    {"id-aes192-GCM", "aes-192-gcm", NID_aes_192_gcm, 9, &so[5188]},
    {"id-aes192-CCM", "aes-192-ccm", NID_aes_192_ccm, 9, &so[5197]},
    {"id-aes192-wrap-pad", "id-aes192-wrap-pad", NID_id_aes192_wrap_pad, 9, &so[5206]},
    {"id-aes256-GCM", "aes-256-gcm", NID_aes_256_gcm, 9, &so[5215]},
    {"id-aes256-CCM", "aes-256-ccm", NID_aes_256_ccm, 9, &so[5224]},
    {"id-aes256-wrap-pad", "id-aes256-wrap-pad", NID_id_aes256_wrap_pad, 9, &so[5233]},
    {"AES-128-CTR", "aes-128-ctr", NID_aes_128_ctr},
    {"AES-192-CTR", "aes-192-ctr", NID_aes_192_ctr},
    {"AES-256-CTR", "aes-256-ctr", NID_aes_256_ctr},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"anyExtendedKeyUsage", "Any Extended Key Usage", NID_anyExtendedKeyUsage, 4, &so[5242]},
    {"MGF1", "mgf1", NID_mgf1, 9, &so[5246]},
    {"RSASSA-PSS", "rsassaPss", NID_rsassaPss, 9, &so[5255]},
    {"AES-128-XTS", "aes-128-xts", NID_aes_128_xts, 8, &so[5264]},
    {"AES-256-XTS", "aes-256-xts", NID_aes_256_xts, 8, &so[5272]},
    {"RC4-HMAC-MD5", "rc4-hmac-md5", NID_rc4_hmac_md5},
    {"AES-128-CBC-HMAC-SHA1", "aes-128-cbc-hmac-sha1", NID_aes_128_cbc_hmac_sha1},
    {"AES-192-CBC-HMAC-SHA1", "aes-192-cbc-hmac-sha1", NID_aes_192_cbc_hmac_sha1},
    {"AES-256-CBC-HMAC-SHA1", "aes-256-cbc-hmac-sha1", NID_aes_256_cbc_hmac_sha1},
    {"RSAES-OAEP", "rsaesOaep", NID_rsaesOaep, 9, &so[5280]},
    {"dhpublicnumber", "X9.42 DH", NID_dhpublicnumber, 7, &so[5289]},
    {"brainpoolP160r1", "brainpoolP160r1", NID_brainpoolP160r1, 9, &so[5296]},
    {"brainpoolP160t1", "brainpoolP160t1", NID_brainpoolP160t1, 9, &so[5305]},
    {"brainpoolP192r1", "brainpoolP192r1", NID_brainpoolP192r1, 9, &so[5314]},
    {"brainpoolP192t1", "brainpoolP192t1", NID_brainpoolP192t1, 9, &so[5323]},
    {"brainpoolP224r1", "brainpoolP224r1", NID_brainpoolP224r1, 9, &so[5332]},
    {"brainpoolP224t1", "brainpoolP224t1", NID_brainpoolP224t1, 9, &so[5341]},
    {"brainpoolP256r1", "brainpoolP256r1", NID_brainpoolP256r1, 9, &so[5350]},
    {"brainpoolP256t1", "brainpoolP256t1", NID_brainpoolP256t1, 9, &so[5359]},
    {"brainpoolP320r1", "brainpoolP320r1", NID_brainpoolP320r1, 9, &so[5368]},
    {"brainpoolP320t1", "brainpoolP320t1", NID_brainpoolP320t1, 9, &so[5377]},
    {"brainpoolP384r1", "brainpoolP384r1", NID_brainpoolP384r1, 9, &so[5386]},
    {"brainpoolP384t1", "brainpoolP384t1", NID_brainpoolP384t1, 9, &so[5395]},
    {"brainpoolP512r1", "brainpoolP512r1", NID_brainpoolP512r1, 9, &so[5404]},
    {"brainpoolP512t1", "brainpoolP512t1", NID_brainpoolP512t1, 9, &so[5413]},
    {"PSPECIFIED", "pSpecified", NID_pSpecified, 9, &so[5422]},
    {"dhSinglePass-stdDH-sha1kdf-scheme", "dhSinglePass-stdDH-sha1kdf-scheme", NID_dhSinglePass_stdDH_sha1kdf_scheme, 9, &so[5431]},
    {"dhSinglePass-stdDH-sha224kdf-scheme", "dhSinglePass-stdDH-sha224kdf-scheme", NID_dhSinglePass_stdDH_sha224kdf_scheme, 6, &so[5440]},
    {"dhSinglePass-stdDH-sha256kdf-scheme", "dhSinglePass-stdDH-sha256kdf-scheme", NID_dhSinglePass_stdDH_sha256kdf_scheme, 6, &so[5446]},
    {"dhSinglePass-stdDH-sha384kdf-scheme", "dhSinglePass-stdDH-sha384kdf-scheme", NID_dhSinglePass_stdDH_sha384kdf_scheme, 6, &so[5452]},
    {"dhSinglePass-stdDH-sha512kdf-scheme", "dhSinglePass-stdDH-sha512kdf-scheme", NID_dhSinglePass_stdDH_sha512kdf_scheme, 6, &so[5458]},
    {"dhSinglePass-cofactorDH-sha1kdf-scheme", "dhSinglePass-cofactorDH-sha1kdf-scheme", NID_dhSinglePass_cofactorDH_sha1kdf_scheme, 9, &so[5464]},
    {"dhSinglePass-cofactorDH-sha224kdf-scheme", "dhSinglePass-cofactorDH-sha224kdf-scheme", NID_dhSinglePass_cofactorDH_sha224kdf_scheme, 6, &so[5473]},
    {"dhSinglePass-cofactorDH-sha256kdf-scheme", "dhSinglePass-cofactorDH-sha256kdf-scheme", NID_dhSinglePass_cofactorDH_sha256kdf_scheme, 6, &so[5479]},
    {"dhSinglePass-cofactorDH-sha384kdf-scheme", "dhSinglePass-cofactorDH-sha384kdf-scheme", NID_dhSinglePass_cofactorDH_sha384kdf_scheme, 6, &so[5485]},
    {"dhSinglePass-cofactorDH-sha512kdf-scheme", "dhSinglePass-cofactorDH-sha512kdf-scheme", NID_dhSinglePass_cofactorDH_sha512kdf_scheme, 6, &so[5491]},
    {"dh-std-kdf", "dh-std-kdf", NID_dh_std_kdf},
    {"dh-cofactor-kdf", "dh-cofactor-kdf", NID_dh_cofactor_kdf},
    {"AES-128-CBC-HMAC-SHA256", "aes-128-cbc-hmac-sha256", NID_aes_128_cbc_hmac_sha256},
    {"AES-192-CBC-HMAC-SHA256", "aes-192-cbc-hmac-sha256", NID_aes_192_cbc_hmac_sha256},
    {"AES-256-CBC-HMAC-SHA256", "aes-256-cbc-hmac-sha256", NID_aes_256_cbc_hmac_sha256},
    {"ct_precert_scts", "CT Precertificate SCTs", NID_ct_precert_scts, 10, &so[5497]},
    {"ct_precert_poison", "CT Precertificate Poison", NID_ct_precert_poison, 10, &so[5507]},
    {"ct_precert_signer", "CT Precertificate Signer", NID_ct_precert_signer, 10, &so[5517]},
    {"ct_cert_scts", "CT Certificate SCTs", NID_ct_cert_scts, 10, &so[5527]},
    {"jurisdictionL", "jurisdictionLocalityName", NID_jurisdictionLocalityName, 11, &so[5537]},
    {"jurisdictionST", "jurisdictionStateOrProvinceName", NID_jurisdictionStateOrProvinceName, 11, &so[5548]},
    {"jurisdictionC", "jurisdictionCountryName", NID_jurisdictionCountryName, 11, &so[5559]},
    {"AES-128-OCB", "aes-128-ocb", NID_aes_128_ocb},
    {"AES-192-OCB", "aes-192-ocb", NID_aes_192_ocb},
    {"AES-256-OCB", "aes-256-ocb", NID_aes_256_ocb},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"id-scrypt", "scrypt", NID_id_scrypt, 9, &so[5570]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"ChaCha20-Poly1305", "chacha20-poly1305", NID_chacha20_poly1305},
    {"ChaCha20", "chacha20", NID_chacha20},
    {"tlsfeature", "TLS Feature", NID_tlsfeature, 8, &so[5579]},
    {"TLS1-PRF", "tls1-prf", NID_tls1_prf},
    {"ipsecIKE", "ipsec Internet Key Exchange", NID_ipsec_IKE, 8, &so[5587]},
    {"capwapAC", "Ctrl/provision WAP Access", NID_capwapAC, 8, &so[5595]},
    {"capwapWTP", "Ctrl/Provision WAP Termination", NID_capwapWTP, 8, &so[5603]},
    {"secureShellClient", "SSH Client", NID_sshClient, 8, &so[5611]},
    {"secureShellServer", "SSH Server", NID_sshServer, 8, &so[5619]},
    {"sendRouter", "Send Router", NID_sendRouter, 8, &so[5627]},
    {"sendProxiedRouter", "Send Proxied Router", NID_sendProxiedRouter, 8, &so[5635]},
    {"sendOwner", "Send Owner", NID_sendOwner, 8, &so[5643]},
    {"sendProxiedOwner", "Send Proxied Owner", NID_sendProxiedOwner, 8, &so[5651]},
    {"id-pkinit", "id-pkinit", NID_id_pkinit, 6, &so[5659]},
    {"pkInitClientAuth", "PKINIT Client Auth", NID_pkInitClientAuth, 7, &so[5665]},
    {"pkInitKDC", "Signing KDC Response", NID_pkInitKDC, 7, &so[5672]},
    {"X25519", "X25519", NID_X25519, 3, &so[5679]},
    {"X448", "X448", NID_X448, 3, &so[5682]},
    {"HKDF", "hkdf", NID_hkdf},
    {"KxRSA", "kx-rsa", NID_kx_rsa},
    {"KxECDHE", "kx-ecdhe", NID_kx_ecdhe},
    {"KxDHE", "kx-dhe", NID_kx_dhe},
    {"KxECDHE-PSK", "kx-ecdhe-psk", NID_kx_ecdhe_psk},
    {"KxDHE-PSK", "kx-dhe-psk", NID_kx_dhe_psk},
    {"KxRSA_PSK", "kx-rsa-psk", NID_kx_rsa_psk},
    {"KxPSK", "kx-psk", NID_kx_psk},
    {"KxSRP", "kx-srp", NID_kx_srp},
    { NULL, NULL, NID_undef },
    {"AuthRSA", "auth-rsa", NID_auth_rsa},
    {"AuthECDSA", "auth-ecdsa", NID_auth_ecdsa},
    {"AuthPSK", "auth-psk", NID_auth_psk},
    {"AuthDSS", "auth-dss", NID_auth_dss},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"AuthSRP", "auth-srp", NID_auth_srp},
    {"AuthNULL", "auth-null", NID_auth_null},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"id-smime-ct-contentCollection", "id-smime-ct-contentCollection", NID_id_smime_ct_contentCollection, 11, &so[5685]},
    {"id-smime-ct-authEnvelopedData", "id-smime-ct-authEnvelopedData", NID_id_smime_ct_authEnvelopedData, 11, &so[5696]},
    {"id-ct-xml", "id-ct-xml", NID_id_ct_xml, 11, &so[5707]},
    {"Poly1305", "poly1305", NID_poly1305},
    {"SipHash", "siphash", NID_siphash},
    {"KxANY", "kx-any", NID_kx_any},
    {"AuthANY", "auth-any", NID_auth_any},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"id-smime-aa-signingCertificateV2", "id-smime-aa-signingCertificateV2", NID_id_smime_aa_signingCertificateV2, 11, &so[5718]},
    {"ED25519", "ED25519", NID_ED25519, 3, &so[5729]},
    {"ED448", "ED448", NID_ED448, 3, &so[5732]},
    {"organizationIdentifier", "organizationIdentifier", NID_organizationIdentifier, 3, &so[5735]},
    {"c3", "countryCode3c", NID_countryCode3c, 3, &so[5738]},
    {"n3", "countryCode3n", NID_countryCode3n, 3, &so[5741]},
    {"dnsName", "dnsName", NID_dnsName, 3, &so[5744]},
    {"x509ExtAdmission", "Professional Information or basis for Admission", NID_x509ExtAdmission, 5, &so[5747]},
    {"SHA512-224", "sha512-224", NID_sha512_224, 9, &so[5752]},
    {"SHA512-256", "sha512-256", NID_sha512_256, 9, &so[5761]},
    {"SHA3-224", "sha3-224", NID_sha3_224, 9, &so[5770]},
    {"SHA3-256", "sha3-256", NID_sha3_256, 9, &so[5779]},
    {"SHA3-384", "sha3-384", NID_sha3_384, 9, &so[5788]},
    {"SHA3-512", "sha3-512", NID_sha3_512, 9, &so[5797]},
    {"SHAKE128", "shake128", NID_shake128, 9, &so[5806]},
    {"SHAKE256", "shake256", NID_shake256, 9, &so[5815]},
    {"id-hmacWithSHA3-224", "hmac-sha3-224", NID_hmac_sha3_224, 9, &so[5824]},
    {"id-hmacWithSHA3-256", "hmac-sha3-256", NID_hmac_sha3_256, 9, &so[5833]},
    {"id-hmacWithSHA3-384", "hmac-sha3-384", NID_hmac_sha3_384, 9, &so[5842]},
    {"id-hmacWithSHA3-512", "hmac-sha3-512", NID_hmac_sha3_512, 9, &so[5851]},
    {"id-dsa-with-sha384", "dsa_with_SHA384", NID_dsa_with_SHA384, 9, &so[5860]},
    {"id-dsa-with-sha512", "dsa_with_SHA512", NID_dsa_with_SHA512, 9, &so[5869]},
    {"id-dsa-with-sha3-224", "dsa_with_SHA3-224", NID_dsa_with_SHA3_224, 9, &so[5878]},
    {"id-dsa-with-sha3-256", "dsa_with_SHA3-256", NID_dsa_with_SHA3_256, 9, &so[5887]},
    {"id-dsa-with-sha3-384", "dsa_with_SHA3-384", NID_dsa_with_SHA3_384, 9, &so[5896]},
    {"id-dsa-with-sha3-512", "dsa_with_SHA3-512", NID_dsa_with_SHA3_512, 9, &so[5905]},
    {"id-ecdsa-with-sha3-224", "ecdsa_with_SHA3-224", NID_ecdsa_with_SHA3_224, 9, &so[5914]},
    {"id-ecdsa-with-sha3-256", "ecdsa_with_SHA3-256", NID_ecdsa_with_SHA3_256, 9, &so[5923]},
    {"id-ecdsa-with-sha3-384", "ecdsa_with_SHA3-384", NID_ecdsa_with_SHA3_384, 9, &so[5932]},
    {"id-ecdsa-with-sha3-512", "ecdsa_with_SHA3-512", NID_ecdsa_with_SHA3_512, 9, &so[5941]},
    {"id-rsassa-pkcs1-v1_5-with-sha3-224", "RSA-SHA3-224", NID_RSA_SHA3_224, 9, &so[5950]},
    {"id-rsassa-pkcs1-v1_5-with-sha3-256", "RSA-SHA3-256", NID_RSA_SHA3_256, 9, &so[5959]},
    {"id-rsassa-pkcs1-v1_5-with-sha3-384", "RSA-SHA3-384", NID_RSA_SHA3_384, 9, &so[5968]},
    {"id-rsassa-pkcs1-v1_5-with-sha3-512", "RSA-SHA3-512", NID_RSA_SHA3_512, 9, &so[5977]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"ffdhe2048", "ffdhe2048", NID_ffdhe2048},
    {"ffdhe3072", "ffdhe3072", NID_ffdhe3072},
    {"ffdhe4096", "ffdhe4096", NID_ffdhe4096},
    {"ffdhe6144", "ffdhe6144", NID_ffdhe6144},
    {"ffdhe8192", "ffdhe8192", NID_ffdhe8192},
    {"cmcCA", "CMC Certificate Authority", NID_cmcCA, 8, &so[5986]},
    {"cmcRA", "CMC Registration Authority", NID_cmcRA, 8, &so[5994]},
    {"SM4-ECB", "sm4-ecb", NID_sm4_ecb, 8, &so[6002]},
    {"SM4-CBC", "sm4-cbc", NID_sm4_cbc, 8, &so[6010]},
    {"SM4-OFB", "sm4-ofb", NID_sm4_ofb128, 8, &so[6018]},
    {"SM4-CFB1", "sm4-cfb1", NID_sm4_cfb1, 8, &so[6026]},
    {"SM4-CFB", "sm4-cfb", NID_sm4_cfb128, 8, &so[6034]},
    {"SM4-CFB8", "sm4-cfb8", NID_sm4_cfb8, 8, &so[6042]},
    {"SM4-CTR", "sm4-ctr", NID_sm4_ctr, 8, &so[6050]},
    {"ISO-CN", "ISO CN Member Body", NID_ISO_CN, 3, &so[6058]},
    {"oscca", "oscca", NID_oscca, 5, &so[6061]},
    {"sm-scheme", "sm-scheme", NID_sm_scheme, 6, &so[6066]},
    {"SM3", "sm3", NID_sm3, 8, &so[6072]},
    {"RSA-SM3", "sm3WithRSAEncryption", NID_sm3WithRSAEncryption, 8, &so[6080]},
    {"RSA-SHA512/224", "sha512-224WithRSAEncryption", NID_sha512_224WithRSAEncryption, 9, &so[6088]},
    {"RSA-SHA512/256", "sha512-256WithRSAEncryption", NID_sha512_256WithRSAEncryption, 9, &so[6097]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"ISO-UA", "ISO-UA", NID_ISO_UA, 3, &so[6106]},
    {"ua-pki", "ua-pki", NID_ua_pki, 7, &so[6109]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"dstu4145le", "DSTU 4145-2002 little endian", NID_dstu4145le, 11, &so[6116]},
    {"dstu4145be", "DSTU 4145-2002 big endian", NID_dstu4145be, 13, &so[6127]},
    {"uacurve0", "DSTU curve 0", NID_uacurve0, 13, &so[6140]},
    {"uacurve1", "DSTU curve 1", NID_uacurve1, 13, &so[6153]},
    {"uacurve2", "DSTU curve 2", NID_uacurve2, 13, &so[6166]},
    {"uacurve3", "DSTU curve 3", NID_uacurve3, 13, &so[6179]},
    {"uacurve4", "DSTU curve 4", NID_uacurve4, 13, &so[6192]},
    {"uacurve5", "DSTU curve 5", NID_uacurve5, 13, &so[6205]},
    {"uacurve6", "DSTU curve 6", NID_uacurve6, 13, &so[6218]},
    {"uacurve7", "DSTU curve 7", NID_uacurve7, 13, &so[6231]},
    {"uacurve8", "DSTU curve 8", NID_uacurve8, 13, &so[6244]},
    {"uacurve9", "DSTU curve 9", NID_uacurve9, 13, &so[6257]},
    {"ieee", "ieee", NID_ieee, 2, &so[6270]},
    {"ieee-siswg", "IEEE Security in Storage Working Group", NID_ieee_siswg, 5, &so[6272]},
    {"SM2", "sm2", NID_sm2, 8, &so[6277]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"hmacWithSHA512-224", "hmacWithSHA512-224", NID_hmacWithSHA512_224, 8, &so[6285]},
    {"hmacWithSHA512-256", "hmacWithSHA512-256", NID_hmacWithSHA512_256, 8, &so[6293]},
    {"GMAC", "gmac", NID_gmac, 5, &so[6301]},
    {"KMAC128", "kmac128", NID_kmac128, 9, &so[6306]},
    {"KMAC256", "kmac256", NID_kmac256, 9, &so[6315]},
    {"AES-128-SIV", "aes-128-siv", NID_aes_128_siv},
    {"AES-192-SIV", "aes-192-siv", NID_aes_192_siv},
    {"AES-256-SIV", "aes-256-siv", NID_aes_256_siv},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"SSHKDF", "sshkdf", NID_sshkdf},
    {"SM2-SM3", "SM2-with-SM3", NID_SM2_with_SM3, 8, &so[6324]},
    {"SSKDF", "sskdf", NID_sskdf},
    {"X963KDF", "x963kdf", NID_x963kdf},
    {"X942KDF", "x942kdf", NID_x942kdf},
    {"id-on-SmtpUTF8Mailbox", "Smtp UTF8 Mailbox", NID_id_on_SmtpUTF8Mailbox, 8, &so[6332]},
    {"id-on-xmppAddr", "XmppAddr", NID_XmppAddr, 8, &so[6340]},
    {"id-on-dnsSRV", "SRVName", NID_SRVName, 8, &so[6348]},
    {"id-on-NAIRealm", "NAIRealm", NID_NAIRealm, 8, &so[6356]},
    {"modp_1536", "modp_1536", NID_modp_1536},
    {"modp_2048", "modp_2048", NID_modp_2048},
    {"modp_3072", "modp_3072", NID_modp_3072},
    {"modp_4096", "modp_4096", NID_modp_4096},
    {"modp_6144", "modp_6144", NID_modp_6144},
    {"modp_8192", "modp_8192", NID_modp_8192},
    { NULL, NULL, NID_undef },
    {"cmcArchive", "CMC Archive Server", NID_cmcArchive, 8, &so[6364]},
    {"id-kp-bgpsec-router", "BGPsec Router", NID_id_kp_bgpsec_router, 8, &so[6372]},
    {"id-kp-BrandIndicatorforMessageIdentification", "Brand Indicator for Message Identification", NID_id_kp_BrandIndicatorforMessageIdentification, 8, &so[6380]},
    {"cmKGA", "Certificate Management Key Generation Authority", NID_cmKGA, 8, &so[6388]},
    {"id-it-caCerts", "id-it-caCerts", NID_id_it_caCerts, 8, &so[6396]},
    {"id-it-rootCaKeyUpdate", "id-it-rootCaKeyUpdate", NID_id_it_rootCaKeyUpdate, 8, &so[6404]},
    {"id-it-certReqTemplate", "id-it-certReqTemplate", NID_id_it_certReqTemplate, 8, &so[6412]},
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    { NULL, NULL, NID_undef },
    {"id-ct-routeOriginAuthz", "id-ct-routeOriginAuthz", NID_id_ct_routeOriginAuthz, 11, &so[6420]},
    {"id-ct-rpkiManifest", "id-ct-rpkiManifest", NID_id_ct_rpkiManifest, 11, &so[6431]},
    {"id-ct-rpkiGhostbusters", "id-ct-rpkiGhostbusters", NID_id_ct_rpkiGhostbusters, 11, &so[6442]},
    {"id-ct-resourceTaggedAttest", "id-ct-resourceTaggedAttest", NID_id_ct_resourceTaggedAttest, 11, &so[6453]},
    {"id-cp", "id-cp", NID_id_cp, 7, &so[6464]},
    {"sbgp-ipAddrBlockv2", "sbgp-ipAddrBlockv2", NID_sbgp_ipAddrBlockv2, 8, &so[6471]},
    {"sbgp-autonomousSysNumv2", "sbgp-autonomousSysNumv2", NID_sbgp_autonomousSysNumv2, 8, &so[6479]},
    {"ipAddr-asNumber", "ipAddr-asNumber", NID_ipAddr_asNumber, 8, &so[6487]},
    {"ipAddr-asNumberv2", "ipAddr-asNumberv2", NID_ipAddr_asNumberv2, 8, &so[6495]},
    {"rpkiManifest", "RPKI Manifest", NID_rpkiManifest, 8, &so[6503]},
    {"signedObject", "Signed Object", NID_signedObject, 8, &so[6511]},
    {"rpkiNotify", "RPKI Notify", NID_rpkiNotify, 8, &so[6519]},
    {"id-ct-geofeedCSVwithCRLF", "id-ct-geofeedCSVwithCRLF", NID_id_ct_geofeedCSVwithCRLF, 11, &so[6527]},
    {"id-ct-signedChecklist", "id-ct-signedChecklist", NID_id_ct_signedChecklist, 11, &so[6538]},
    {"ZUC", "zuc", NID_zuc, 8, &so[6549]},
    {"ZUC-128-EEA3", "zuc-128-eea3", NID_zuc_128_eea3, 8, &so[6557]},
    {"SM4-GCM", "sm4-gcm", NID_sm4_gcm, 8, &so[6565]},
    {"SM4-CCM", "sm4-ccm", NID_sm4_ccm, 8, &so[6573]},
    {"KxSM2", "kx-sm2", NID_kx_sm2},
    {"KxSM2DHE", "kx-sm2dhe", NID_kx_sm2dhe},
    {"AuthSM2", "auth-sm2", NID_auth_sm2},
    {"ZUC-128-EIA3", "zuc-128-eia3", NID_zuc_128_eia3, 8, &so[6581]},
    {"delegationUsage", "X509v3 Delegation Usage", NID_delegation_usage, 9, &so[6589]},
    {"hmacWithSM3", "hmacWithSM3", NID_hmacWithSM3, 10, &so[6598]},
    {"oracle-organization", "Oracle organization", NID_oracle, 7, &so[6608]},
    {"oracle-jdk-trustedkeyusage", "Trusted key usage (Oracle)", NID_oracle_jdk_trustedkeyusage, 12, &so[6615]},
    {"WBSM4-XIAOLAI-ECB", "wbsm4-xiaolai-ecb", NID_wbsm4_xiaolai_ecb},
    {"WBSM4-XIAOLAI-CBC", "wbsm4-xiaolai-cbc", NID_wbsm4_xiaolai_cbc},
    {"WBSM4-XIAOLAI-OFB", "wbsm4-xiaolai-ofb", NID_wbsm4_xiaolai_ofb128},
    {"WBSM4-XIAOLAI-CFB", "wbsm4-xiaolai-cfb", NID_wbsm4_xiaolai_cfb128},
    {"WBSM4-XIAOLAI-CTR", "wbsm4-xiaolai-ctr", NID_wbsm4_xiaolai_ctr},
    {"WBSM4-XIAOLAI-GCM", "wbsm4-xiaolai-gcm", NID_wbsm4_xiaolai_gcm},
    {"WBSM4-XIAOLAI-CCM", "wbsm4-xiaolai-ccm", NID_wbsm4_xiaolai_ccm},
    {"WBSM4-BAIWU-ECB", "wbsm4-baiwu-ecb", NID_wbsm4_baiwu_ecb},
    {"WBSM4-BAIWU-CBC", "wbsm4-baiwu-cbc", NID_wbsm4_baiwu_cbc},
    {"WBSM4-BAIWU-CTR", "wbsm4-baiwu-ctr", NID_wbsm4_baiwu_ctr},
    {"WBSM4-BAIWU-GCM", "wbsm4-baiwu-gcm", NID_wbsm4_baiwu_gcm},
    {"WBSM4-BAIWU-CCM", "wbsm4-baiwu-ccm", NID_wbsm4_baiwu_ccm},
    {"WBSM4-WSISE-ECB", "wbsm4-wsise-ecb", NID_wbsm4_wsise_ecb},
    {"WBSM4-WSISE-CBC", "wbsm4-wsise-cbc", NID_wbsm4_wsise_cbc},
    {"WBSM4-WSISE-CTR", "wbsm4-wsise-ctr", NID_wbsm4_wsise_ctr},
    {"WBSM4-WSISE-GCM", "wbsm4-wsise-gcm", NID_wbsm4_wsise_gcm},
    {"WBSM4-WSISE-CCM", "wbsm4-wsise-ccm", NID_wbsm4_wsise_ccm},
    {"WBSM4-BAIWU-OFB", "wbsm4-baiwu-ofb", NID_wbsm4_baiwu_ofb128},
    {"WBSM4-BAIWU-CFB", "wbsm4-baiwu-cfb", NID_wbsm4_baiwu_cfb128},
    {"WBSM4-WSISE-OFB", "wbsm4-wsise-ofb", NID_wbsm4_wsise_ofb128},
    {"WBSM4-WSISE-CFB", "wbsm4-wsise-cfb", NID_wbsm4_wsise_cfb128},
};

#define NUM_SN 1031
static const unsigned int sn_objs[NUM_SN] = {
     364,    /* "AD_DVCS" */
     419,    /* "AES-128-CBC" */
     916,    /* "AES-128-CBC-HMAC-SHA1" */
     948,    /* "AES-128-CBC-HMAC-SHA256" */
     421,    /* "AES-128-CFB" */
     650,    /* "AES-128-CFB1" */
     653,    /* "AES-128-CFB8" */
     904,    /* "AES-128-CTR" */
     418,    /* "AES-128-ECB" */
     958,    /* "AES-128-OCB" */
     420,    /* "AES-128-OFB" */
    1198,    /* "AES-128-SIV" */
     913,    /* "AES-128-XTS" */
     423,    /* "AES-192-CBC" */
     917,    /* "AES-192-CBC-HMAC-SHA1" */
     949,    /* "AES-192-CBC-HMAC-SHA256" */
     425,    /* "AES-192-CFB" */
     651,    /* "AES-192-CFB1" */
     654,    /* "AES-192-CFB8" */
     905,    /* "AES-192-CTR" */
     422,    /* "AES-192-ECB" */
     959,    /* "AES-192-OCB" */
     424,    /* "AES-192-OFB" */
    1199,    /* "AES-192-SIV" */
     427,    /* "AES-256-CBC" */
     918,    /* "AES-256-CBC-HMAC-SHA1" */
     950,    /* "AES-256-CBC-HMAC-SHA256" */
     429,    /* "AES-256-CFB" */
     652,    /* "AES-256-CFB1" */
     655,    /* "AES-256-CFB8" */
     906,    /* "AES-256-CTR" */
     426,    /* "AES-256-ECB" */
     960,    /* "AES-256-OCB" */
     428,    /* "AES-256-OFB" */
    1200,    /* "AES-256-SIV" */
     914,    /* "AES-256-XTS" */
    1064,    /* "AuthANY" */
    1049,    /* "AuthDSS" */
    1047,    /* "AuthECDSA" */
    1053,    /* "AuthNULL" */
    1048,    /* "AuthPSK" */
    1046,    /* "AuthRSA" */
    1254,    /* "AuthSM2" */
    1052,    /* "AuthSRP" */
      14,    /* "C" */
     894,    /* "CMAC" */
      13,    /* "CN" */
     141,    /* "CRLReason" */
     417,    /* "CSPName" */
    1019,    /* "ChaCha20" */
    1018,    /* "ChaCha20-Poly1305" */
     367,    /* "CrlID" */
     391,    /* "DC" */
      31,    /* "DES-CBC" */
     643,    /* "DES-CDMF" */
      30,    /* "DES-CFB" */
     656,    /* "DES-CFB1" */
     657,    /* "DES-CFB8" */
      29,    /* "DES-ECB" */
      32,    /* "DES-EDE" */
      43,    /* "DES-EDE-CBC" */
      60,    /* "DES-EDE-CFB" */
      62,    /* "DES-EDE-OFB" */
      33,    /* "DES-EDE3" */
      44,    /* "DES-EDE3-CBC" */
      61,    /* "DES-EDE3-CFB" */
     658,    /* "DES-EDE3-CFB1" */
     659,    /* "DES-EDE3-CFB8" */
      63,    /* "DES-EDE3-OFB" */
      45,    /* "DES-OFB" */
      80,    /* "DESX-CBC" */
     380,    /* "DOD" */
     116,    /* "DSA" */
      66,    /* "DSA-SHA" */
     113,    /* "DSA-SHA1" */
      70,    /* "DSA-SHA1-old" */
      67,    /* "DSA-old" */
     297,    /* "DVCS" */
    1087,    /* "ED25519" */
    1088,    /* "ED448" */
    1195,    /* "GMAC" */
      99,    /* "GN" */
    1036,    /* "HKDF" */
     855,    /* "HMAC" */
     780,    /* "HMAC-MD5" */
     781,    /* "HMAC-SHA1" */
     381,    /* "IANA" */
     181,    /* "ISO" */
    1140,    /* "ISO-CN" */
    1150,    /* "ISO-UA" */
     183,    /* "ISO-US" */
     645,    /* "ITU-T" */
     646,    /* "JOINT-ISO-ITU-T" */
    1196,    /* "KMAC128" */
    1197,    /* "KMAC256" */
    1063,    /* "KxANY" */
    1039,    /* "KxDHE" */
    1041,    /* "KxDHE-PSK" */
    1038,    /* "KxECDHE" */
    1040,    /* "KxECDHE-PSK" */
    1043,    /* "KxPSK" */
    1037,    /* "KxRSA" */
    1042,    /* "KxRSA_PSK" */
    1252,    /* "KxSM2" */
    1253,    /* "KxSM2DHE" */
    1044,    /* "KxSRP" */
      15,    /* "L" */
     856,    /* "LocalKeySet" */
       4,    /* "MD5" */
     114,    /* "MD5-SHA1" */
     911,    /* "MGF1" */
     388,    /* "Mail" */
     393,    /* "NULL" */
     404,    /* "NULL" */
      57,    /* "Netscape" */
     366,    /* "Nonce" */
      17,    /* "O" */
     178,    /* "OCSP" */
     180,    /* "OCSPSigning" */
     379,    /* "ORG" */
      18,    /* "OU" */
     749,    /* "Oakley-EC2N-3" */
     750,    /* "Oakley-EC2N-4" */
      10,    /* "PBE-MD5-DES" */
     147,    /* "PBE-SHA1-2DES" */
     146,    /* "PBE-SHA1-3DES" */
     170,    /* "PBE-SHA1-DES" */
     144,    /* "PBE-SHA1-RC4-128" */
     145,    /* "PBE-SHA1-RC4-40" */
     161,    /* "PBES2" */
      69,    /* "PBKDF2" */
     162,    /* "PBMAC1" */
     127,    /* "PKIX" */
     935,    /* "PSPECIFIED" */
    1061,    /* "Poly1305" */
       5,    /* "RC4" */
      97,    /* "RC4-40" */
     915,    /* "RC4-HMAC-MD5" */
     120,    /* "RC5-CBC" */
     122,    /* "RC5-CFB" */
     121,    /* "RC5-ECB" */
     123,    /* "RC5-OFB" */
      19,    /* "RSA" */
       8,    /* "RSA-MD5" */
     104,    /* "RSA-NP-MD5" */
      42,    /* "RSA-SHA" */
      65,    /* "RSA-SHA1" */
     115,    /* "RSA-SHA1-2" */
     671,    /* "RSA-SHA224" */
     668,    /* "RSA-SHA256" */
     669,    /* "RSA-SHA384" */
     670,    /* "RSA-SHA512" */
    1145,    /* "RSA-SHA512/224" */
    1146,    /* "RSA-SHA512/256" */
    1144,    /* "RSA-SM3" */
     919,    /* "RSAES-OAEP" */
     912,    /* "RSASSA-PSS" */
      41,    /* "SHA" */
      64,    /* "SHA1" */
     675,    /* "SHA224" */
     672,    /* "SHA256" */
    1096,    /* "SHA3-224" */
    1097,    /* "SHA3-256" */
    1098,    /* "SHA3-384" */
    1099,    /* "SHA3-512" */
     673,    /* "SHA384" */
     674,    /* "SHA512" */
    1094,    /* "SHA512-224" */
    1095,    /* "SHA512-256" */
    1100,    /* "SHAKE128" */
    1101,    /* "SHAKE256" */
    1172,    /* "SM2" */
    1204,    /* "SM2-SM3" */
    1143,    /* "SM3" */
    1134,    /* "SM4-CBC" */
    1251,    /* "SM4-CCM" */
    1137,    /* "SM4-CFB" */
    1136,    /* "SM4-CFB1" */
    1138,    /* "SM4-CFB8" */
    1139,    /* "SM4-CTR" */
    1133,    /* "SM4-ECB" */
    1250,    /* "SM4-GCM" */
    1135,    /* "SM4-OFB" */
     188,    /* "SMIME" */
     167,    /* "SMIME-CAPS" */
     100,    /* "SN" */
    1203,    /* "SSHKDF" */
    1205,    /* "SSKDF" */
      16,    /* "ST" */
     143,    /* "SXNetID" */
    1062,    /* "SipHash" */
    1021,    /* "TLS1-PRF" */
     458,    /* "UID" */
       0,    /* "UNDEF" */
    1268,    /* "WBSM4-BAIWU-CBC" */
    1271,    /* "WBSM4-BAIWU-CCM" */
    1278,    /* "WBSM4-BAIWU-CFB" */
    1269,    /* "WBSM4-BAIWU-CTR" */
    1267,    /* "WBSM4-BAIWU-ECB" */
    1270,    /* "WBSM4-BAIWU-GCM" */
    1277,    /* "WBSM4-BAIWU-OFB" */
    1273,    /* "WBSM4-WSISE-CBC" */
    1276,    /* "WBSM4-WSISE-CCM" */
    1280,    /* "WBSM4-WSISE-CFB" */
    1274,    /* "WBSM4-WSISE-CTR" */
    1272,    /* "WBSM4-WSISE-ECB" */
    1275,    /* "WBSM4-WSISE-GCM" */
    1279,    /* "WBSM4-WSISE-OFB" */
    1261,    /* "WBSM4-XIAOLAI-CBC" */
    1266,    /* "WBSM4-XIAOLAI-CCM" */
    1263,    /* "WBSM4-XIAOLAI-CFB" */
    1264,    /* "WBSM4-XIAOLAI-CTR" */
    1260,    /* "WBSM4-XIAOLAI-ECB" */
    1265,    /* "WBSM4-XIAOLAI-GCM" */
    1262,    /* "WBSM4-XIAOLAI-OFB" */
    1034,    /* "X25519" */
    1035,    /* "X448" */
      11,    /* "X500" */
     378,    /* "X500algorithms" */
      12,    /* "X509" */
     184,    /* "X9-57" */
    1207,    /* "X942KDF" */
    1206,    /* "X963KDF" */
     185,    /* "X9cm" */
     125,    /* "ZLIB" */
    1248,    /* "ZUC" */
    1249,    /* "ZUC-128-EEA3" */
    1255,    /* "ZUC-128-EIA3" */
     478,    /* "aRecord" */
     289,    /* "aaControls" */
     287,    /* "ac-auditEntity" */
     397,    /* "ac-proxying" */
     288,    /* "ac-targeting" */
     368,    /* "acceptableResponses" */
     446,    /* "account" */
     363,    /* "ad_timestamping" */
     376,    /* "algorithm" */
     405,    /* "ansi-X9-62" */
     910,    /* "anyExtendedKeyUsage" */
     746,    /* "anyPolicy" */
     370,    /* "archiveCutoff" */
     484,    /* "associatedDomain" */
     485,    /* "associatedName" */
     501,    /* "audio" */
     177,    /* "authorityInfoAccess" */
      90,    /* "authorityKeyIdentifier" */
     882,    /* "authorityRevocationList" */
      87,    /* "basicConstraints" */
     365,    /* "basicOCSPResponse" */
     285,    /* "biometricInfo" */
     921,    /* "brainpoolP160r1" */
     922,    /* "brainpoolP160t1" */
     923,    /* "brainpoolP192r1" */
     924,    /* "brainpoolP192t1" */
     925,    /* "brainpoolP224r1" */
     926,    /* "brainpoolP224t1" */
     927,    /* "brainpoolP256r1" */
     928,    /* "brainpoolP256t1" */
     929,    /* "brainpoolP320r1" */
     930,    /* "brainpoolP320t1" */
     931,    /* "brainpoolP384r1" */
     932,    /* "brainpoolP384t1" */
     933,    /* "brainpoolP512r1" */
     934,    /* "brainpoolP512t1" */
     494,    /* "buildingName" */
     860,    /* "businessCategory" */
     691,    /* "c2onb191v4" */
     692,    /* "c2onb191v5" */
     697,    /* "c2onb239v4" */
     698,    /* "c2onb239v5" */
     684,    /* "c2pnb163v1" */
     685,    /* "c2pnb163v2" */
     686,    /* "c2pnb163v3" */
     687,    /* "c2pnb176v1" */
     693,    /* "c2pnb208w1" */
     699,    /* "c2pnb272w1" */
     700,    /* "c2pnb304w1" */
     702,    /* "c2pnb368w1" */
     688,    /* "c2tnb191v1" */
     689,    /* "c2tnb191v2" */
     690,    /* "c2tnb191v3" */
     694,    /* "c2tnb239v1" */
     695,    /* "c2tnb239v2" */
     696,    /* "c2tnb239v3" */
     701,    /* "c2tnb359v1" */
     703,    /* "c2tnb431r1" */
    1090,    /* "c3" */
     881,    /* "cACertificate" */
     483,    /* "cNAMERecord" */
     179,    /* "caIssuers" */
     785,    /* "caRepository" */
    1023,    /* "capwapAC" */
    1024,    /* "capwapWTP" */
     443,    /* "caseIgnoreIA5StringSyntax" */
     152,    /* "certBag" */
     677,    /* "certicom-arc" */
     771,    /* "certificateIssuer" */
      89,    /* "certificatePolicies" */
     883,    /* "certificateRevocationList" */
      54,    /* "challengePassword" */
     407,    /* "characteristic-two-field" */
     395,    /* "clearance" */
     130,    /* "clientAuth" */
    1222,    /* "cmKGA" */
    1219,    /* "cmcArchive" */
    1131,    /* "cmcCA" */
    1132,    /* "cmcRA" */
     131,    /* "codeSigning" */
      50,    /* "contentType" */
      53,    /* "countersignature" */
     153,    /* "crlBag" */
     103,    /* "crlDistributionPoints" */
      88,    /* "crlNumber" */
     884,    /* "crossCertificatePair" */
     954,    /* "ct_cert_scts" */
     952,    /* "ct_precert_poison" */
     951,    /* "ct_precert_scts" */
     953,    /* "ct_precert_signer" */
     500,    /* "dITRedirect" */
     451,    /* "dNSDomain" */
     495,    /* "dSAQuality" */
     434,    /* "data" */
     390,    /* "dcobject" */
    1256,    /* "delegationUsage" */
     140,    /* "deltaCRL" */
     891,    /* "deltaRevocationList" */
     107,    /* "description" */
     871,    /* "destinationIndicator" */
     947,    /* "dh-cofactor-kdf" */
     946,    /* "dh-std-kdf" */
      28,    /* "dhKeyAgreement" */
     941,    /* "dhSinglePass-cofactorDH-sha1kdf-scheme" */
     942,    /* "dhSinglePass-cofactorDH-sha224kdf-scheme" */
     943,    /* "dhSinglePass-cofactorDH-sha256kdf-scheme" */
     944,    /* "dhSinglePass-cofactorDH-sha384kdf-scheme" */
     945,    /* "dhSinglePass-cofactorDH-sha512kdf-scheme" */
     936,    /* "dhSinglePass-stdDH-sha1kdf-scheme" */
     937,    /* "dhSinglePass-stdDH-sha224kdf-scheme" */
     938,    /* "dhSinglePass-stdDH-sha256kdf-scheme" */
     939,    /* "dhSinglePass-stdDH-sha384kdf-scheme" */
     940,    /* "dhSinglePass-stdDH-sha512kdf-scheme" */
     920,    /* "dhpublicnumber" */
     382,    /* "directory" */
     887,    /* "distinguishedName" */
     892,    /* "dmdName" */
     174,    /* "dnQualifier" */
    1092,    /* "dnsName" */
     447,    /* "document" */
     471,    /* "documentAuthor" */
     468,    /* "documentIdentifier" */
     472,    /* "documentLocation" */
     502,    /* "documentPublisher" */
     449,    /* "documentSeries" */
     469,    /* "documentTitle" */
     470,    /* "documentVersion" */
     392,    /* "domain" */
     452,    /* "domainRelatedObject" */
     802,    /* "dsa_with_SHA224" */
     803,    /* "dsa_with_SHA256" */
    1159,    /* "dstu4145be" */
    1158,    /* "dstu4145le" */
     791,    /* "ecdsa-with-Recommended" */
     416,    /* "ecdsa-with-SHA1" */
     793,    /* "ecdsa-with-SHA224" */
     794,    /* "ecdsa-with-SHA256" */
     795,    /* "ecdsa-with-SHA384" */
     796,    /* "ecdsa-with-SHA512" */
     792,    /* "ecdsa-with-Specified" */
      48,    /* "emailAddress" */
     132,    /* "emailProtection" */
     885,    /* "enhancedSearchGuide" */
     389,    /* "enterprises" */
     384,    /* "experimental" */
     172,    /* "extReq" */
      56,    /* "extendedCertificateAttributes" */
     126,    /* "extendedKeyUsage" */
     372,    /* "extendedStatus" */
     867,    /* "facsimileTelephoneNumber" */
     462,    /* "favouriteDrink" */
    1126,    /* "ffdhe2048" */
    1127,    /* "ffdhe3072" */
    1128,    /* "ffdhe4096" */
    1129,    /* "ffdhe6144" */
    1130,    /* "ffdhe8192" */
     857,    /* "freshestCRL" */
     453,    /* "friendlyCountry" */
     490,    /* "friendlyCountryName" */
     156,    /* "friendlyName" */
     509,    /* "generationQualifier" */
     797,    /* "hmacWithMD5" */
     163,    /* "hmacWithSHA1" */
     798,    /* "hmacWithSHA224" */
     799,    /* "hmacWithSHA256" */
     800,    /* "hmacWithSHA384" */
     801,    /* "hmacWithSHA512" */
    1193,    /* "hmacWithSHA512-224" */
    1194,    /* "hmacWithSHA512-256" */
    1257,    /* "hmacWithSM3" */
     432,    /* "holdInstructionCallIssuer" */
     430,    /* "holdInstructionCode" */
     431,    /* "holdInstructionNone" */
     433,    /* "holdInstructionReject" */
     486,    /* "homePostalAddress" */
     473,    /* "homeTelephoneNumber" */
     466,    /* "host" */
     889,    /* "houseIdentifier" */
     442,    /* "iA5StringSyntax" */
     783,    /* "id-DHBasedMac" */
     782,    /* "id-PasswordBasedMAC" */
     266,    /* "id-aca" */
     355,    /* "id-aca-accessIdentity" */
     354,    /* "id-aca-authenticationInfo" */
     356,    /* "id-aca-chargingIdentity" */
     399,    /* "id-aca-encAttrs" */
     357,    /* "id-aca-group" */
     358,    /* "id-aca-role" */
     176,    /* "id-ad" */
     896,    /* "id-aes128-CCM" */
     895,    /* "id-aes128-GCM" */
     788,    /* "id-aes128-wrap" */
     897,    /* "id-aes128-wrap-pad" */
     899,    /* "id-aes192-CCM" */
     898,    /* "id-aes192-GCM" */
     789,    /* "id-aes192-wrap" */
     900,    /* "id-aes192-wrap-pad" */
     902,    /* "id-aes256-CCM" */
     901,    /* "id-aes256-GCM" */
     790,    /* "id-aes256-wrap" */
     903,    /* "id-aes256-wrap-pad" */
     262,    /* "id-alg" */
     893,    /* "id-alg-PWRI-KEK" */
     323,    /* "id-alg-des40" */
     326,    /* "id-alg-dh-pop" */
     325,    /* "id-alg-dh-sig-hmac-sha1" */
     324,    /* "id-alg-noSignature" */
     268,    /* "id-cct" */
     361,    /* "id-cct-PKIData" */
     362,    /* "id-cct-PKIResponse" */
     360,    /* "id-cct-crs" */
      81,    /* "id-ce" */
     680,    /* "id-characteristic-two-basis" */
     263,    /* "id-cmc" */
     334,    /* "id-cmc-addExtensions" */
     346,    /* "id-cmc-confirmCertAcceptance" */
     330,    /* "id-cmc-dataReturn" */
     336,    /* "id-cmc-decryptedPOP" */
     335,    /* "id-cmc-encryptedPOP" */
     339,    /* "id-cmc-getCRL" */
     338,    /* "id-cmc-getCert" */
     328,    /* "id-cmc-identification" */
     329,    /* "id-cmc-identityProof" */
     337,    /* "id-cmc-lraPOPWitness" */
     344,    /* "id-cmc-popLinkRandom" */
     345,    /* "id-cmc-popLinkWitness" */
     343,    /* "id-cmc-queryPending" */
     333,    /* "id-cmc-recipientNonce" */
     341,    /* "id-cmc-regInfo" */
     342,    /* "id-cmc-responseInfo" */
     340,    /* "id-cmc-revokeRequest" */
     332,    /* "id-cmc-senderNonce" */
     327,    /* "id-cmc-statusInfo" */
     331,    /* "id-cmc-transactionId" */
    1238,    /* "id-cp" */
     787,    /* "id-ct-asciiTextWithCRLF" */
    1246,    /* "id-ct-geofeedCSVwithCRLF" */
    1237,    /* "id-ct-resourceTaggedAttest" */
    1234,    /* "id-ct-routeOriginAuthz" */
    1236,    /* "id-ct-rpkiGhostbusters" */
    1235,    /* "id-ct-rpkiManifest" */
    1247,    /* "id-ct-signedChecklist" */
    1060,    /* "id-ct-xml" */
    1108,    /* "id-dsa-with-sha3-224" */
    1109,    /* "id-dsa-with-sha3-256" */
    1110,    /* "id-dsa-with-sha3-384" */
    1111,    /* "id-dsa-with-sha3-512" */
    1106,    /* "id-dsa-with-sha384" */
    1107,    /* "id-dsa-with-sha512" */
     408,    /* "id-ecPublicKey" */
    1112,    /* "id-ecdsa-with-sha3-224" */
    1113,    /* "id-ecdsa-with-sha3-256" */
    1114,    /* "id-ecdsa-with-sha3-384" */
    1115,    /* "id-ecdsa-with-sha3-512" */
     508,    /* "id-hex-multipart-message" */
     507,    /* "id-hex-partial-message" */
    1102,    /* "id-hmacWithSHA3-224" */
    1103,    /* "id-hmacWithSHA3-256" */
    1104,    /* "id-hmacWithSHA3-384" */
    1105,    /* "id-hmacWithSHA3-512" */
     260,    /* "id-it" */
    1223,    /* "id-it-caCerts" */
     302,    /* "id-it-caKeyUpdateInfo" */
     298,    /* "id-it-caProtEncCert" */
    1225,    /* "id-it-certReqTemplate" */
     311,    /* "id-it-confirmWaitTime" */
     303,    /* "id-it-currentCRL" */
     300,    /* "id-it-encKeyPairTypes" */
     310,    /* "id-it-implicitConfirm" */
     308,    /* "id-it-keyPairParamRep" */
     307,    /* "id-it-keyPairParamReq" */
     312,    /* "id-it-origPKIMessage" */
     301,    /* "id-it-preferredSymmAlg" */
     309,    /* "id-it-revPassphrase" */
    1224,    /* "id-it-rootCaKeyUpdate" */
     299,    /* "id-it-signKeyPairTypes" */
     305,    /* "id-it-subscriptionRequest" */
     306,    /* "id-it-subscriptionResponse" */
     784,    /* "id-it-suppLangTags" */
     304,    /* "id-it-unsupportedOIDs" */
     128,    /* "id-kp" */
    1221,    /* "id-kp-BrandIndicatorforMessageIdentification" */
    1220,    /* "id-kp-bgpsec-router" */
     280,    /* "id-mod-attribute-cert" */
     274,    /* "id-mod-cmc" */
     277,    /* "id-mod-cmp" */
     284,    /* "id-mod-cmp2000" */
     273,    /* "id-mod-crmf" */
     283,    /* "id-mod-dvcs" */
     275,    /* "id-mod-kea-profile-88" */
     276,    /* "id-mod-kea-profile-93" */
     282,    /* "id-mod-ocsp" */
     278,    /* "id-mod-qualified-cert-88" */
     279,    /* "id-mod-qualified-cert-93" */
     281,    /* "id-mod-timestamp-protocol" */
     264,    /* "id-on" */
    1211,    /* "id-on-NAIRealm" */
    1208,    /* "id-on-SmtpUTF8Mailbox" */
    1210,    /* "id-on-dnsSRV" */
     858,    /* "id-on-permanentIdentifier" */
     347,    /* "id-on-personalData" */
    1209,    /* "id-on-xmppAddr" */
     265,    /* "id-pda" */
     352,    /* "id-pda-countryOfCitizenship" */
     353,    /* "id-pda-countryOfResidence" */
     348,    /* "id-pda-dateOfBirth" */
     351,    /* "id-pda-gender" */
     349,    /* "id-pda-placeOfBirth" */
     175,    /* "id-pe" */
    1031,    /* "id-pkinit" */
     261,    /* "id-pkip" */
     258,    /* "id-pkix-mod" */
     269,    /* "id-pkix1-explicit-88" */
     271,    /* "id-pkix1-explicit-93" */
     270,    /* "id-pkix1-implicit-88" */
     272,    /* "id-pkix1-implicit-93" */
     662,    /* "id-ppl" */
     664,    /* "id-ppl-anyLanguage" */
     667,    /* "id-ppl-independent" */
     665,    /* "id-ppl-inheritAll" */
     267,    /* "id-qcs" */
     359,    /* "id-qcs-pkixQCSyntax-v1" */
     259,    /* "id-qt" */
     164,    /* "id-qt-cps" */
     165,    /* "id-qt-unotice" */
     313,    /* "id-regCtrl" */
     316,    /* "id-regCtrl-authenticator" */
     319,    /* "id-regCtrl-oldCertID" */
     318,    /* "id-regCtrl-pkiArchiveOptions" */
     317,    /* "id-regCtrl-pkiPublicationInfo" */
     320,    /* "id-regCtrl-protocolEncrKey" */
     315,    /* "id-regCtrl-regToken" */
     314,    /* "id-regInfo" */
     322,    /* "id-regInfo-certReq" */
     321,    /* "id-regInfo-utf8Pairs" */
    1116,    /* "id-rsassa-pkcs1-v1_5-with-sha3-224" */
    1117,    /* "id-rsassa-pkcs1-v1_5-with-sha3-256" */
    1118,    /* "id-rsassa-pkcs1-v1_5-with-sha3-384" */
    1119,    /* "id-rsassa-pkcs1-v1_5-with-sha3-512" */
     973,    /* "id-scrypt" */
     512,    /* "id-set" */
     191,    /* "id-smime-aa" */
     215,    /* "id-smime-aa-contentHint" */
     218,    /* "id-smime-aa-contentIdentifier" */
     221,    /* "id-smime-aa-contentReference" */
     240,    /* "id-smime-aa-dvcs-dvc" */
     217,    /* "id-smime-aa-encapContentType" */
     222,    /* "id-smime-aa-encrypKeyPref" */
     220,    /* "id-smime-aa-equivalentLabels" */
     232,    /* "id-smime-aa-ets-CertificateRefs" */
     233,    /* "id-smime-aa-ets-RevocationRefs" */
     238,    /* "id-smime-aa-ets-archiveTimeStamp" */
     237,    /* "id-smime-aa-ets-certCRLTimestamp" */
     234,    /* "id-smime-aa-ets-certValues" */
     227,    /* "id-smime-aa-ets-commitmentType" */
     231,    /* "id-smime-aa-ets-contentTimestamp" */
     236,    /* "id-smime-aa-ets-escTimeStamp" */
     230,    /* "id-smime-aa-ets-otherSigCert" */
     235,    /* "id-smime-aa-ets-revocationValues" */
     226,    /* "id-smime-aa-ets-sigPolicyId" */
     229,    /* "id-smime-aa-ets-signerAttr" */
     228,    /* "id-smime-aa-ets-signerLocation" */
     219,    /* "id-smime-aa-macValue" */
     214,    /* "id-smime-aa-mlExpandHistory" */
     216,    /* "id-smime-aa-msgSigDigest" */
     212,    /* "id-smime-aa-receiptRequest" */
     213,    /* "id-smime-aa-securityLabel" */
     239,    /* "id-smime-aa-signatureType" */
     223,    /* "id-smime-aa-signingCertificate" */
    1086,    /* "id-smime-aa-signingCertificateV2" */
     224,    /* "id-smime-aa-smimeEncryptCerts" */
     225,    /* "id-smime-aa-timeStampToken" */
     192,    /* "id-smime-alg" */
     243,    /* "id-smime-alg-3DESwrap" */
     246,    /* "id-smime-alg-CMS3DESwrap" */
     245,    /* "id-smime-alg-ESDH" */
     241,    /* "id-smime-alg-ESDHwith3DES" */
     193,    /* "id-smime-cd" */
     248,    /* "id-smime-cd-ldap" */
     190,    /* "id-smime-ct" */
     210,    /* "id-smime-ct-DVCSRequestData" */
     211,    /* "id-smime-ct-DVCSResponseData" */
     208,    /* "id-smime-ct-TDTInfo" */
     207,    /* "id-smime-ct-TSTInfo" */
     205,    /* "id-smime-ct-authData" */
    1059,    /* "id-smime-ct-authEnvelopedData" */
     786,    /* "id-smime-ct-compressedData" */
    1058,    /* "id-smime-ct-contentCollection" */
     209,    /* "id-smime-ct-contentInfo" */
     206,    /* "id-smime-ct-publishCert" */
     204,    /* "id-smime-ct-receipt" */
     195,    /* "id-smime-cti" */
     255,    /* "id-smime-cti-ets-proofOfApproval" */
     256,    /* "id-smime-cti-ets-proofOfCreation" */
     253,    /* "id-smime-cti-ets-proofOfDelivery" */
     251,    /* "id-smime-cti-ets-proofOfOrigin" */
     252,    /* "id-smime-cti-ets-proofOfReceipt" */
     254,    /* "id-smime-cti-ets-proofOfSender" */
     189,    /* "id-smime-mod" */
     196,    /* "id-smime-mod-cms" */
     197,    /* "id-smime-mod-ess" */
     202,    /* "id-smime-mod-ets-eSigPolicy-88" */
     203,    /* "id-smime-mod-ets-eSigPolicy-97" */
     200,    /* "id-smime-mod-ets-eSignature-88" */
     201,    /* "id-smime-mod-ets-eSignature-97" */
     199,    /* "id-smime-mod-msg-v3" */
     198,    /* "id-smime-mod-oid" */
     194,    /* "id-smime-spq" */
     250,    /* "id-smime-spq-ets-sqt-unotice" */
     249,    /* "id-smime-spq-ets-sqt-uri" */
     676,    /* "identified-organization" */
    1170,    /* "ieee" */
    1171,    /* "ieee-siswg" */
     461,    /* "info" */
     748,    /* "inhibitAnyPolicy" */
     101,    /* "initials" */
     647,    /* "international-organizations" */
     869,    /* "internationaliSDNNumber" */
     142,    /* "invalidityDate" */
    1241,    /* "ipAddr-asNumber" */
    1242,    /* "ipAddr-asNumberv2" */
     294,    /* "ipsecEndSystem" */
    1022,    /* "ipsecIKE" */
     295,    /* "ipsecTunnel" */
     296,    /* "ipsecUser" */
      86,    /* "issuerAltName" */
     770,    /* "issuingDistributionPoint" */
     492,    /* "janetMailbox" */
     957,    /* "jurisdictionC" */
     955,    /* "jurisdictionL" */
     956,    /* "jurisdictionST" */
     150,    /* "keyBag" */
      83,    /* "keyUsage" */
     477,    /* "lastModifiedBy" */
     476,    /* "lastModifiedTime" */
     157,    /* "localKeyID" */
     480,    /* "mXRecord" */
     460,    /* "mail" */
     493,    /* "mailPreferenceOption" */
     467,    /* "manager" */
     875,    /* "member" */
     182,    /* "member-body" */
      51,    /* "messageDigest" */
     383,    /* "mgmt" */
     504,    /* "mime-mhs" */
     506,    /* "mime-mhs-bodies" */
     505,    /* "mime-mhs-headings" */
     488,    /* "mobileTelephoneNumber" */
    1212,    /* "modp_1536" */
    1213,    /* "modp_2048" */
    1214,    /* "modp_3072" */
    1215,    /* "modp_4096" */
    1216,    /* "modp_6144" */
    1217,    /* "modp_8192" */
     136,    /* "msCTLSign" */
     135,    /* "msCodeCom" */
     134,    /* "msCodeInd" */
     138,    /* "msEFS" */
     171,    /* "msExtReq" */
     137,    /* "msSGC" */
     648,    /* "msSmartcardLogin" */
     649,    /* "msUPN" */
    1091,    /* "n3" */
     481,    /* "nSRecord" */
     173,    /* "name" */
     666,    /* "nameConstraints" */
     369,    /* "noCheck" */
     403,    /* "noRevAvail" */
      72,    /* "nsBaseUrl" */
      76,    /* "nsCaPolicyUrl" */
      74,    /* "nsCaRevocationUrl" */
      58,    /* "nsCertExt" */
      79,    /* "nsCertSequence" */
      71,    /* "nsCertType" */
      78,    /* "nsComment" */
      59,    /* "nsDataType" */
      75,    /* "nsRenewalUrl" */
      73,    /* "nsRevocationUrl" */
     139,    /* "nsSGC" */
      77,    /* "nsSslServerName" */
     681,    /* "onBasis" */
    1259,    /* "oracle-jdk-trustedkeyusage" */
    1258,    /* "oracle-organization" */
    1089,    /* "organizationIdentifier" */
     491,    /* "organizationalStatus" */
    1141,    /* "oscca" */
     475,    /* "otherMailbox" */
     876,    /* "owner" */
     489,    /* "pagerTelephoneNumber" */
     374,    /* "path" */
     499,    /* "personalSignature" */
     487,    /* "personalTitle" */
     464,    /* "photo" */
     863,    /* "physicalDeliveryOfficeName" */
     437,    /* "pilot" */
     439,    /* "pilotAttributeSyntax" */
     438,    /* "pilotAttributeType" */
     479,    /* "pilotAttributeType27" */
     456,    /* "pilotDSA" */
     441,    /* "pilotGroups" */
     444,    /* "pilotObject" */
     440,    /* "pilotObjectClass" */
     455,    /* "pilotOrganization" */
     445,    /* "pilotPerson" */
    1032,    /* "pkInitClientAuth" */
    1033,    /* "pkInitKDC" */
       2,    /* "pkcs" */
     186,    /* "pkcs1" */
      27,    /* "pkcs3" */
     187,    /* "pkcs5" */
      20,    /* "pkcs7" */
      21,    /* "pkcs7-data" */
      25,    /* "pkcs7-digestData" */
      26,    /* "pkcs7-encryptedData" */
      23,    /* "pkcs7-envelopedData" */
      24,    /* "pkcs7-signedAndEnvelopedData" */
      22,    /* "pkcs7-signedData" */
     151,    /* "pkcs8ShroudedKeyBag" */
      47,    /* "pkcs9" */
     401,    /* "policyConstraints" */
     747,    /* "policyMappings" */
     862,    /* "postOfficeBox" */
     861,    /* "postalAddress" */
     661,    /* "postalCode" */
     683,    /* "ppBasis" */
     872,    /* "preferredDeliveryMethod" */
     873,    /* "presentationAddress" */
     406,    /* "prime-field" */
     409,    /* "prime192v1" */
     410,    /* "prime192v2" */
     411,    /* "prime192v3" */
     412,    /* "prime239v1" */
     413,    /* "prime239v2" */
     414,    /* "prime239v3" */
     415,    /* "prime256v1" */
     385,    /* "private" */
      84,    /* "privateKeyUsagePeriod" */
     886,    /* "protocolInformation" */
     663,    /* "proxyCertInfo" */
     510,    /* "pseudonym" */
     435,    /* "pss" */
     286,    /* "qcStatements" */
     457,    /* "qualityLabelledData" */
     450,    /* "rFC822localPart" */
     870,    /* "registeredAddress" */
     400,    /* "role" */
     877,    /* "roleOccupant" */
     448,    /* "room" */
     463,    /* "roomNumber" */
    1243,    /* "rpkiManifest" */
    1245,    /* "rpkiNotify" */
       6,    /* "rsaEncryption" */
     644,    /* "rsaOAEPEncryptionSET" */
     377,    /* "rsaSignature" */
       1,    /* "rsadsi" */
     482,    /* "sOARecord" */
     155,    /* "safeContentsBag" */
     291,    /* "sbgp-autonomousSysNum" */
    1240,    /* "sbgp-autonomousSysNumv2" */
     290,    /* "sbgp-ipAddrBlock" */
    1239,    /* "sbgp-ipAddrBlockv2" */
     292,    /* "sbgp-routerIdentifier" */
     159,    /* "sdsiCertificate" */
     859,    /* "searchGuide" */
     704,    /* "secp112r1" */
     705,    /* "secp112r2" */
     706,    /* "secp128r1" */
     707,    /* "secp128r2" */
     708,    /* "secp160k1" */
     709,    /* "secp160r1" */
     710,    /* "secp160r2" */
     711,    /* "secp192k1" */
     712,    /* "secp224k1" */
     713,    /* "secp224r1" */
     714,    /* "secp256k1" */
     715,    /* "secp384r1" */
     716,    /* "secp521r1" */
     154,    /* "secretBag" */
     474,    /* "secretary" */
     717,    /* "sect113r1" */
     718,    /* "sect113r2" */
     719,    /* "sect131r1" */
     720,    /* "sect131r2" */
     721,    /* "sect163k1" */
     722,    /* "sect163r1" */
     723,    /* "sect163r2" */
     724,    /* "sect193r1" */
     725,    /* "sect193r2" */
     726,    /* "sect233k1" */
     727,    /* "sect233r1" */
     728,    /* "sect239k1" */
     729,    /* "sect283k1" */
     730,    /* "sect283r1" */
     731,    /* "sect409k1" */
     732,    /* "sect409r1" */
     733,    /* "sect571k1" */
     734,    /* "sect571r1" */
    1025,    /* "secureShellClient" */
    1026,    /* "secureShellServer" */
     386,    /* "security" */
     878,    /* "seeAlso" */
     394,    /* "selected-attribute-types" */
    1029,    /* "sendOwner" */
    1030,    /* "sendProxiedOwner" */
    1028,    /* "sendProxiedRouter" */
    1027,    /* "sendRouter" */
     105,    /* "serialNumber" */
     129,    /* "serverAuth" */
     371,    /* "serviceLocator" */
     625,    /* "set-addPolicy" */
     515,    /* "set-attr" */
     518,    /* "set-brand" */
     638,    /* "set-brand-AmericanExpress" */
     637,    /* "set-brand-Diners" */
     636,    /* "set-brand-IATA-ATA" */
     639,    /* "set-brand-JCB" */
     641,    /* "set-brand-MasterCard" */
     642,    /* "set-brand-Novus" */
     640,    /* "set-brand-Visa" */
     517,    /* "set-certExt" */
     513,    /* "set-ctype" */
     514,    /* "set-msgExt" */
     516,    /* "set-policy" */
     607,    /* "set-policy-root" */
     624,    /* "set-rootKeyThumb" */
     620,    /* "setAttr-Cert" */
     631,    /* "setAttr-GenCryptgrm" */
     623,    /* "setAttr-IssCap" */
     628,    /* "setAttr-IssCap-CVM" */
     630,    /* "setAttr-IssCap-Sig" */
     629,    /* "setAttr-IssCap-T2" */
     621,    /* "setAttr-PGWYcap" */
     635,    /* "setAttr-SecDevSig" */
     632,    /* "setAttr-T2Enc" */
     633,    /* "setAttr-T2cleartxt" */
     634,    /* "setAttr-TokICCsig" */
     627,    /* "setAttr-Token-B0Prime" */
     626,    /* "setAttr-Token-EMV" */
     622,    /* "setAttr-TokenType" */
     619,    /* "setCext-IssuerCapabilities" */
     615,    /* "setCext-PGWYcapabilities" */
     616,    /* "setCext-TokenIdentifier" */
     618,    /* "setCext-TokenType" */
     617,    /* "setCext-Track2Data" */
     611,    /* "setCext-cCertRequired" */
     609,    /* "setCext-certType" */
     608,    /* "setCext-hashedRoot" */
     610,    /* "setCext-merchData" */
     613,    /* "setCext-setExt" */
     614,    /* "setCext-setQualf" */
     612,    /* "setCext-tunneling" */
     540,    /* "setct-AcqCardCodeMsg" */
     576,    /* "setct-AcqCardCodeMsgTBE" */
     570,    /* "setct-AuthReqTBE" */
     534,    /* "setct-AuthReqTBS" */
     527,    /* "setct-AuthResBaggage" */
     571,    /* "setct-AuthResTBE" */
     572,    /* "setct-AuthResTBEX" */
     535,    /* "setct-AuthResTBS" */
     536,    /* "setct-AuthResTBSX" */
     528,    /* "setct-AuthRevReqBaggage" */
     577,    /* "setct-AuthRevReqTBE" */
     541,    /* "setct-AuthRevReqTBS" */
     529,    /* "setct-AuthRevResBaggage" */
     542,    /* "setct-AuthRevResData" */
     578,    /* "setct-AuthRevResTBE" */
     579,    /* "setct-AuthRevResTBEB" */
     543,    /* "setct-AuthRevResTBS" */
     573,    /* "setct-AuthTokenTBE" */
     537,    /* "setct-AuthTokenTBS" */
     600,    /* "setct-BCIDistributionTBS" */
     558,    /* "setct-BatchAdminReqData" */
     592,    /* "setct-BatchAdminReqTBE" */
     559,    /* "setct-BatchAdminResData" */
     593,    /* "setct-BatchAdminResTBE" */
     599,    /* "setct-CRLNotificationResTBS" */
     598,    /* "setct-CRLNotificationTBS" */
     580,    /* "setct-CapReqTBE" */
     581,    /* "setct-CapReqTBEX" */
     544,    /* "setct-CapReqTBS" */
     545,    /* "setct-CapReqTBSX" */
     546,    /* "setct-CapResData" */
     582,    /* "setct-CapResTBE" */
     583,    /* "setct-CapRevReqTBE" */
     584,    /* "setct-CapRevReqTBEX" */
     547,    /* "setct-CapRevReqTBS" */
     548,    /* "setct-CapRevReqTBSX" */
     549,    /* "setct-CapRevResData" */
     585,    /* "setct-CapRevResTBE" */
     538,    /* "setct-CapTokenData" */
     530,    /* "setct-CapTokenSeq" */
     574,    /* "setct-CapTokenTBE" */
     575,    /* "setct-CapTokenTBEX" */
     539,    /* "setct-CapTokenTBS" */
     560,    /* "setct-CardCInitResTBS" */
     566,    /* "setct-CertInqReqTBS" */
     563,    /* "setct-CertReqData" */
     595,    /* "setct-CertReqTBE" */
     596,    /* "setct-CertReqTBEX" */
     564,    /* "setct-CertReqTBS" */
     565,    /* "setct-CertResData" */
     597,    /* "setct-CertResTBE" */
     586,    /* "setct-CredReqTBE" */
     587,    /* "setct-CredReqTBEX" */
     550,    /* "setct-CredReqTBS" */
     551,    /* "setct-CredReqTBSX" */
     552,    /* "setct-CredResData" */
     588,    /* "setct-CredResTBE" */
     589,    /* "setct-CredRevReqTBE" */
     590,    /* "setct-CredRevReqTBEX" */
     553,    /* "setct-CredRevReqTBS" */
     554,    /* "setct-CredRevReqTBSX" */
     555,    /* "setct-CredRevResData" */
     591,    /* "setct-CredRevResTBE" */
     567,    /* "setct-ErrorTBS" */
     526,    /* "setct-HODInput" */
     561,    /* "setct-MeAqCInitResTBS" */
     522,    /* "setct-OIData" */
     519,    /* "setct-PANData" */
     521,    /* "setct-PANOnly" */
     520,    /* "setct-PANToken" */
     556,    /* "setct-PCertReqData" */
     557,    /* "setct-PCertResTBS" */
     523,    /* "setct-PI" */
     532,    /* "setct-PI-TBS" */
     524,    /* "setct-PIData" */
     525,    /* "setct-PIDataUnsigned" */
     568,    /* "setct-PIDualSignedTBE" */
     569,    /* "setct-PIUnsignedTBE" */
     531,    /* "setct-PInitResData" */
     533,    /* "setct-PResData" */
     594,    /* "setct-RegFormReqTBE" */
     562,    /* "setct-RegFormResTBS" */
     606,    /* "setext-cv" */
     601,    /* "setext-genCrypt" */
     602,    /* "setext-miAuth" */
     604,    /* "setext-pinAny" */
     603,    /* "setext-pinSecure" */
     605,    /* "setext-track2" */
    1244,    /* "signedObject" */
      52,    /* "signingTime" */
     454,    /* "simpleSecurityObject" */
     496,    /* "singleLevelQuality" */
    1142,    /* "sm-scheme" */
     387,    /* "snmpv2" */
     660,    /* "street" */
      85,    /* "subjectAltName" */
     769,    /* "subjectDirectoryAttributes" */
     398,    /* "subjectInfoAccess" */
      82,    /* "subjectKeyIdentifier" */
     498,    /* "subtreeMaximumQuality" */
     497,    /* "subtreeMinimumQuality" */
     890,    /* "supportedAlgorithms" */
     874,    /* "supportedApplicationContext" */
     402,    /* "targetInformation" */
     864,    /* "telephoneNumber" */
     866,    /* "teletexTerminalIdentifier" */
     865,    /* "telexNumber" */
     459,    /* "textEncodedORAddress" */
     293,    /* "textNotice" */
     133,    /* "timeStamping" */
     106,    /* "title" */
    1020,    /* "tlsfeature" */
     682,    /* "tpBasis" */
     375,    /* "trustRoot" */
    1151,    /* "ua-pki" */
    1160,    /* "uacurve0" */
    1161,    /* "uacurve1" */
    1162,    /* "uacurve2" */
    1163,    /* "uacurve3" */
    1164,    /* "uacurve4" */
    1165,    /* "uacurve5" */
    1166,    /* "uacurve6" */
    1167,    /* "uacurve7" */
    1168,    /* "uacurve8" */
    1169,    /* "uacurve9" */
     436,    /* "ucl" */
     102,    /* "uid" */
     888,    /* "uniqueMember" */
      55,    /* "unstructuredAddress" */
      49,    /* "unstructuredName" */
     880,    /* "userCertificate" */
     465,    /* "userClass" */
     879,    /* "userPassword" */
     373,    /* "valid" */
     678,    /* "wap" */
     679,    /* "wap-wsg" */
     735,    /* "wap-wsg-idm-ecid-wtls1" */
     743,    /* "wap-wsg-idm-ecid-wtls10" */
     744,    /* "wap-wsg-idm-ecid-wtls11" */
     745,    /* "wap-wsg-idm-ecid-wtls12" */
     736,    /* "wap-wsg-idm-ecid-wtls3" */
     737,    /* "wap-wsg-idm-ecid-wtls4" */
     738,    /* "wap-wsg-idm-ecid-wtls5" */
     739,    /* "wap-wsg-idm-ecid-wtls6" */
     740,    /* "wap-wsg-idm-ecid-wtls7" */
     741,    /* "wap-wsg-idm-ecid-wtls8" */
     742,    /* "wap-wsg-idm-ecid-wtls9" */
     868,    /* "x121Address" */
     503,    /* "x500UniqueIdentifier" */
     158,    /* "x509Certificate" */
     160,    /* "x509Crl" */
    1093,    /* "x509ExtAdmission" */
};

#define NUM_LN 1031
static const unsigned int ln_objs[NUM_LN] = {
     363,    /* "AD Time Stamping" */
     405,    /* "ANSI X9.62" */
     368,    /* "Acceptable OCSP Responses" */
     910,    /* "Any Extended Key Usage" */
     664,    /* "Any language" */
     177,    /* "Authority Information Access" */
    1220,    /* "BGPsec Router" */
     365,    /* "Basic OCSP Response" */
     285,    /* "Biometric Info" */
    1221,    /* "Brand Indicator for Message Identification" */
     179,    /* "CA Issuers" */
     785,    /* "CA Repository" */
    1219,    /* "CMC Archive Server" */
    1131,    /* "CMC Certificate Authority" */
    1132,    /* "CMC Registration Authority" */
     954,    /* "CT Certificate SCTs" */
     952,    /* "CT Precertificate Poison" */
     951,    /* "CT Precertificate SCTs" */
     953,    /* "CT Precertificate Signer" */
    1222,    /* "Certificate Management Key Generation Authority" */
     131,    /* "Code Signing" */
    1024,    /* "Ctrl/Provision WAP Termination" */
    1023,    /* "Ctrl/provision WAP Access" */
    1159,    /* "DSTU 4145-2002 big endian" */
    1158,    /* "DSTU 4145-2002 little endian" */
    1160,    /* "DSTU curve 0" */
    1161,    /* "DSTU curve 1" */
    1162,    /* "DSTU curve 2" */
    1163,    /* "DSTU curve 3" */
    1164,    /* "DSTU curve 4" */
    1165,    /* "DSTU curve 5" */
    1166,    /* "DSTU curve 6" */
    1167,    /* "DSTU curve 7" */
    1168,    /* "DSTU curve 8" */
    1169,    /* "DSTU curve 9" */
     783,    /* "Diffie-Hellman based MAC" */
     382,    /* "Directory" */
     392,    /* "Domain" */
     132,    /* "E-mail Protection" */
    1087,    /* "ED25519" */
    1088,    /* "ED448" */
     389,    /* "Enterprises" */
     384,    /* "Experimental" */
     372,    /* "Extended OCSP Status" */
     172,    /* "Extension Request" */
     432,    /* "Hold Instruction Call Issuer" */
     430,    /* "Hold Instruction Code" */
     431,    /* "Hold Instruction None" */
     433,    /* "Hold Instruction Reject" */
     634,    /* "ICC or token signature" */
    1171,    /* "IEEE Security in Storage Working Group" */
     294,    /* "IPSec End System" */
     295,    /* "IPSec Tunnel" */
     296,    /* "IPSec User" */
    1140,    /* "ISO CN Member Body" */
     182,    /* "ISO Member Body" */
     183,    /* "ISO US Member Body" */
    1150,    /* "ISO-UA" */
     667,    /* "Independent" */
     665,    /* "Inherit all" */
     647,    /* "International Organizations" */
     142,    /* "Invalidity Date" */
     504,    /* "MIME MHS" */
     388,    /* "Mail" */
     383,    /* "Management" */
     417,    /* "Microsoft CSP Name" */
     135,    /* "Microsoft Commercial Code Signing" */
     138,    /* "Microsoft Encrypted File System" */
     171,    /* "Microsoft Extension Request" */
     134,    /* "Microsoft Individual Code Signing" */
     856,    /* "Microsoft Local Key set" */
     137,    /* "Microsoft Server Gated Crypto" */
     648,    /* "Microsoft Smartcard Login" */
     136,    /* "Microsoft Trust List Signing" */
     649,    /* "Microsoft User Principal Name" */
    1211,    /* "NAIRealm" */
     393,    /* "NULL" */
     404,    /* "NULL" */
      72,    /* "Netscape Base Url" */
      76,    /* "Netscape CA Policy Url" */
      74,    /* "Netscape CA Revocation Url" */
      71,    /* "Netscape Cert Type" */
      58,    /* "Netscape Certificate Extension" */
      79,    /* "Netscape Certificate Sequence" */
      78,    /* "Netscape Comment" */
      57,    /* "Netscape Communications Corp." */
      59,    /* "Netscape Data Type" */
      75,    /* "Netscape Renewal Url" */
      73,    /* "Netscape Revocation Url" */
      77,    /* "Netscape SSL Server Name" */
     139,    /* "Netscape Server Gated Crypto" */
     178,    /* "OCSP" */
     370,    /* "OCSP Archive Cutoff" */
     367,    /* "OCSP CRL ID" */
     369,    /* "OCSP No Check" */
     366,    /* "OCSP Nonce" */
     371,    /* "OCSP Service Locator" */
     180,    /* "OCSP Signing" */
    1258,    /* "Oracle organization" */
     161,    /* "PBES2" */
      69,    /* "PBKDF2" */
     162,    /* "PBMAC1" */
    1032,    /* "PKINIT Client Auth" */
     127,    /* "PKIX" */
     858,    /* "Permanent Identifier" */
     164,    /* "Policy Qualifier CPS" */
     165,    /* "Policy Qualifier User Notice" */
     385,    /* "Private" */
    1093,    /* "Professional Information or basis for Admission" */
     663,    /* "Proxy Certificate Information" */
    1243,    /* "RPKI Manifest" */
    1245,    /* "RPKI Notify" */
       1,    /* "RSA Data Security, Inc." */
       2,    /* "RSA Data Security, Inc. PKCS" */
    1116,    /* "RSA-SHA3-224" */
    1117,    /* "RSA-SHA3-256" */
    1118,    /* "RSA-SHA3-384" */
    1119,    /* "RSA-SHA3-512" */
     188,    /* "S/MIME" */
     167,    /* "S/MIME Capabilities" */
    1204,    /* "SM2-with-SM3" */
     387,    /* "SNMPv2" */
    1210,    /* "SRVName" */
    1025,    /* "SSH Client" */
    1026,    /* "SSH Server" */
     512,    /* "Secure Electronic Transactions" */
     386,    /* "Security" */
     394,    /* "Selected Attribute Types" */
    1029,    /* "Send Owner" */
    1030,    /* "Send Proxied Owner" */
    1028,    /* "Send Proxied Router" */
    1027,    /* "Send Router" */
    1244,    /* "Signed Object" */
    1033,    /* "Signing KDC Response" */
    1208,    /* "Smtp UTF8 Mailbox" */
     143,    /* "Strong Extranet ID" */
     398,    /* "Subject Information Access" */
    1020,    /* "TLS Feature" */
     130,    /* "TLS Web Client Authentication" */
     129,    /* "TLS Web Server Authentication" */
     133,    /* "Time Stamping" */
     375,    /* "Trust Root" */
    1259,    /* "Trusted key usage (Oracle)" */
    1034,    /* "X25519" */
    1035,    /* "X448" */
      12,    /* "X509" */
     402,    /* "X509v3 AC Targeting" */
     746,    /* "X509v3 Any Policy" */
      90,    /* "X509v3 Authority Key Identifier" */
      87,    /* "X509v3 Basic Constraints" */
     103,    /* "X509v3 CRL Distribution Points" */
      88,    /* "X509v3 CRL Number" */
     141,    /* "X509v3 CRL Reason Code" */
     771,    /* "X509v3 Certificate Issuer" */
      89,    /* "X509v3 Certificate Policies" */
    1256,    /* "X509v3 Delegation Usage" */
     140,    /* "X509v3 Delta CRL Indicator" */
     126,    /* "X509v3 Extended Key Usage" */
     857,    /* "X509v3 Freshest CRL" */
     748,    /* "X509v3 Inhibit Any Policy" */
      86,    /* "X509v3 Issuer Alternative Name" */
     770,    /* "X509v3 Issuing Distribution Point" */
      83,    /* "X509v3 Key Usage" */
     666,    /* "X509v3 Name Constraints" */
     403,    /* "X509v3 No Revocation Available" */
     401,    /* "X509v3 Policy Constraints" */
     747,    /* "X509v3 Policy Mappings" */
      84,    /* "X509v3 Private Key Usage Period" */
      85,    /* "X509v3 Subject Alternative Name" */
     769,    /* "X509v3 Subject Directory Attributes" */
      82,    /* "X509v3 Subject Key Identifier" */
     920,    /* "X9.42 DH" */
     184,    /* "X9.57" */
     185,    /* "X9.57 CM ?" */
    1209,    /* "XmppAddr" */
     478,    /* "aRecord" */
     289,    /* "aaControls" */
     287,    /* "ac-auditEntity" */
     397,    /* "ac-proxying" */
     288,    /* "ac-targeting" */
     446,    /* "account" */
     364,    /* "ad dvcs" */
     606,    /* "additional verification" */
     419,    /* "aes-128-cbc" */
     916,    /* "aes-128-cbc-hmac-sha1" */
     948,    /* "aes-128-cbc-hmac-sha256" */
     896,    /* "aes-128-ccm" */
     421,    /* "aes-128-cfb" */
     650,    /* "aes-128-cfb1" */
     653,    /* "aes-128-cfb8" */
     904,    /* "aes-128-ctr" */
     418,    /* "aes-128-ecb" */
     895,    /* "aes-128-gcm" */
     958,    /* "aes-128-ocb" */
     420,    /* "aes-128-ofb" */
    1198,    /* "aes-128-siv" */
     913,    /* "aes-128-xts" */
     423,    /* "aes-192-cbc" */
     917,    /* "aes-192-cbc-hmac-sha1" */
     949,    /* "aes-192-cbc-hmac-sha256" */
     899,    /* "aes-192-ccm" */
     425,    /* "aes-192-cfb" */
     651,    /* "aes-192-cfb1" */
     654,    /* "aes-192-cfb8" */
     905,    /* "aes-192-ctr" */
     422,    /* "aes-192-ecb" */
     898,    /* "aes-192-gcm" */
     959,    /* "aes-192-ocb" */
     424,    /* "aes-192-ofb" */
    1199,    /* "aes-192-siv" */
     427,    /* "aes-256-cbc" */
     918,    /* "aes-256-cbc-hmac-sha1" */
     950,    /* "aes-256-cbc-hmac-sha256" */
     902,    /* "aes-256-ccm" */
     429,    /* "aes-256-cfb" */
     652,    /* "aes-256-cfb1" */
     655,    /* "aes-256-cfb8" */
     906,    /* "aes-256-ctr" */
     426,    /* "aes-256-ecb" */
     901,    /* "aes-256-gcm" */
     960,    /* "aes-256-ocb" */
     428,    /* "aes-256-ofb" */
    1200,    /* "aes-256-siv" */
     914,    /* "aes-256-xts" */
     376,    /* "algorithm" */
     484,    /* "associatedDomain" */
     485,    /* "associatedName" */
     501,    /* "audio" */
    1064,    /* "auth-any" */
    1049,    /* "auth-dss" */
    1047,    /* "auth-ecdsa" */
    1053,    /* "auth-null" */
    1048,    /* "auth-psk" */
    1046,    /* "auth-rsa" */
    1254,    /* "auth-sm2" */
    1052,    /* "auth-srp" */
     882,    /* "authorityRevocationList" */
     921,    /* "brainpoolP160r1" */
     922,    /* "brainpoolP160t1" */
     923,    /* "brainpoolP192r1" */
     924,    /* "brainpoolP192t1" */
     925,    /* "brainpoolP224r1" */
     926,    /* "brainpoolP224t1" */
     927,    /* "brainpoolP256r1" */
     928,    /* "brainpoolP256t1" */
     929,    /* "brainpoolP320r1" */
     930,    /* "brainpoolP320t1" */
     931,    /* "brainpoolP384r1" */
     932,    /* "brainpoolP384t1" */
     933,    /* "brainpoolP512r1" */
     934,    /* "brainpoolP512t1" */
     494,    /* "buildingName" */
     860,    /* "businessCategory" */
     691,    /* "c2onb191v4" */
     692,    /* "c2onb191v5" */
     697,    /* "c2onb239v4" */
     698,    /* "c2onb239v5" */
     684,    /* "c2pnb163v1" */
     685,    /* "c2pnb163v2" */
     686,    /* "c2pnb163v3" */
     687,    /* "c2pnb176v1" */
     693,    /* "c2pnb208w1" */
     699,    /* "c2pnb272w1" */
     700,    /* "c2pnb304w1" */
     702,    /* "c2pnb368w1" */
     688,    /* "c2tnb191v1" */
     689,    /* "c2tnb191v2" */
     690,    /* "c2tnb191v3" */
     694,    /* "c2tnb239v1" */
     695,    /* "c2tnb239v2" */
     696,    /* "c2tnb239v3" */
     701,    /* "c2tnb359v1" */
     703,    /* "c2tnb431r1" */
     881,    /* "cACertificate" */
     483,    /* "cNAMERecord" */
     443,    /* "caseIgnoreIA5StringSyntax" */
     152,    /* "certBag" */
     677,    /* "certicom-arc" */
     517,    /* "certificate extensions" */
     883,    /* "certificateRevocationList" */
    1019,    /* "chacha20" */
    1018,    /* "chacha20-poly1305" */
      54,    /* "challengePassword" */
     407,    /* "characteristic-two-field" */
     395,    /* "clearance" */
     633,    /* "cleartext track 2" */
     894,    /* "cmac" */
      13,    /* "commonName" */
     513,    /* "content types" */
      50,    /* "contentType" */
      53,    /* "countersignature" */
    1090,    /* "countryCode3c" */
    1091,    /* "countryCode3n" */
      14,    /* "countryName" */
     153,    /* "crlBag" */
     884,    /* "crossCertificatePair" */
     500,    /* "dITRedirect" */
     451,    /* "dNSDomain" */
     495,    /* "dSAQuality" */
     434,    /* "data" */
     390,    /* "dcObject" */
     891,    /* "deltaRevocationList" */
      31,    /* "des-cbc" */
     643,    /* "des-cdmf" */
      30,    /* "des-cfb" */
     656,    /* "des-cfb1" */
     657,    /* "des-cfb8" */
      29,    /* "des-ecb" */
      32,    /* "des-ede" */
      43,    /* "des-ede-cbc" */
      60,    /* "des-ede-cfb" */
      62,    /* "des-ede-ofb" */
      33,    /* "des-ede3" */
      44,    /* "des-ede3-cbc" */
      61,    /* "des-ede3-cfb" */
     658,    /* "des-ede3-cfb1" */
     659,    /* "des-ede3-cfb8" */
      63,    /* "des-ede3-ofb" */
      45,    /* "des-ofb" */
     107,    /* "description" */
     871,    /* "destinationIndicator" */
      80,    /* "desx-cbc" */
     947,    /* "dh-cofactor-kdf" */
     946,    /* "dh-std-kdf" */
      28,    /* "dhKeyAgreement" */
     941,    /* "dhSinglePass-cofactorDH-sha1kdf-scheme" */
     942,    /* "dhSinglePass-cofactorDH-sha224kdf-scheme" */
     943,    /* "dhSinglePass-cofactorDH-sha256kdf-scheme" */
     944,    /* "dhSinglePass-cofactorDH-sha384kdf-scheme" */
     945,    /* "dhSinglePass-cofactorDH-sha512kdf-scheme" */
     936,    /* "dhSinglePass-stdDH-sha1kdf-scheme" */
     937,    /* "dhSinglePass-stdDH-sha224kdf-scheme" */
     938,    /* "dhSinglePass-stdDH-sha256kdf-scheme" */
     939,    /* "dhSinglePass-stdDH-sha384kdf-scheme" */
     940,    /* "dhSinglePass-stdDH-sha512kdf-scheme" */
      11,    /* "directory services (X.500)" */
     378,    /* "directory services - algorithms" */
     887,    /* "distinguishedName" */
     892,    /* "dmdName" */
     174,    /* "dnQualifier" */
    1092,    /* "dnsName" */
     447,    /* "document" */
     471,    /* "documentAuthor" */
     468,    /* "documentIdentifier" */
     472,    /* "documentLocation" */
     502,    /* "documentPublisher" */
     449,    /* "documentSeries" */
     469,    /* "documentTitle" */
     470,    /* "documentVersion" */
     380,    /* "dod" */
     391,    /* "domainComponent" */
     452,    /* "domainRelatedObject" */
     116,    /* "dsaEncryption" */
      67,    /* "dsaEncryption-old" */
      66,    /* "dsaWithSHA" */
     113,    /* "dsaWithSHA1" */
      70,    /* "dsaWithSHA1-old" */
     802,    /* "dsa_with_SHA224" */
     803,    /* "dsa_with_SHA256" */
    1108,    /* "dsa_with_SHA3-224" */
    1109,    /* "dsa_with_SHA3-256" */
    1110,    /* "dsa_with_SHA3-384" */
    1111,    /* "dsa_with_SHA3-512" */
    1106,    /* "dsa_with_SHA384" */
    1107,    /* "dsa_with_SHA512" */
     297,    /* "dvcs" */
     791,    /* "ecdsa-with-Recommended" */
     416,    /* "ecdsa-with-SHA1" */
     793,    /* "ecdsa-with-SHA224" */
     794,    /* "ecdsa-with-SHA256" */
     795,    /* "ecdsa-with-SHA384" */
     796,    /* "ecdsa-with-SHA512" */
     792,    /* "ecdsa-with-Specified" */
    1112,    /* "ecdsa_with_SHA3-224" */
    1113,    /* "ecdsa_with_SHA3-256" */
    1114,    /* "ecdsa_with_SHA3-384" */
    1115,    /* "ecdsa_with_SHA3-512" */
      48,    /* "emailAddress" */
     632,    /* "encrypted track 2" */
     885,    /* "enhancedSearchGuide" */
      56,    /* "extendedCertificateAttributes" */
     867,    /* "facsimileTelephoneNumber" */
     462,    /* "favouriteDrink" */
    1126,    /* "ffdhe2048" */
    1127,    /* "ffdhe3072" */
    1128,    /* "ffdhe4096" */
    1129,    /* "ffdhe6144" */
    1130,    /* "ffdhe8192" */
     453,    /* "friendlyCountry" */
     490,    /* "friendlyCountryName" */
     156,    /* "friendlyName" */
     631,    /* "generate cryptogram" */
     509,    /* "generationQualifier" */
     601,    /* "generic cryptogram" */
      99,    /* "givenName" */
    1195,    /* "gmac" */
    1036,    /* "hkdf" */
     855,    /* "hmac" */
     780,    /* "hmac-md5" */
     781,    /* "hmac-sha1" */
    1102,    /* "hmac-sha3-224" */
    1103,    /* "hmac-sha3-256" */
    1104,    /* "hmac-sha3-384" */
    1105,    /* "hmac-sha3-512" */
     797,    /* "hmacWithMD5" */
     163,    /* "hmacWithSHA1" */
     798,    /* "hmacWithSHA224" */
     799,    /* "hmacWithSHA256" */
     800,    /* "hmacWithSHA384" */
     801,    /* "hmacWithSHA512" */
    1193,    /* "hmacWithSHA512-224" */
    1194,    /* "hmacWithSHA512-256" */
    1257,    /* "hmacWithSM3" */
     486,    /* "homePostalAddress" */
     473,    /* "homeTelephoneNumber" */
     466,    /* "host" */
     889,    /* "houseIdentifier" */
     442,    /* "iA5StringSyntax" */
     381,    /* "iana" */
     266,    /* "id-aca" */
     355,    /* "id-aca-accessIdentity" */
     354,    /* "id-aca-authenticationInfo" */
     356,    /* "id-aca-chargingIdentity" */
     399,    /* "id-aca-encAttrs" */
     357,    /* "id-aca-group" */
     358,    /* "id-aca-role" */
     176,    /* "id-ad" */
     788,    /* "id-aes128-wrap" */
     897,    /* "id-aes128-wrap-pad" */
     789,    /* "id-aes192-wrap" */
     900,    /* "id-aes192-wrap-pad" */
     790,    /* "id-aes256-wrap" */
     903,    /* "id-aes256-wrap-pad" */
     262,    /* "id-alg" */
     893,    /* "id-alg-PWRI-KEK" */
     323,    /* "id-alg-des40" */
     326,    /* "id-alg-dh-pop" */
     325,    /* "id-alg-dh-sig-hmac-sha1" */
     324,    /* "id-alg-noSignature" */
     268,    /* "id-cct" */
     361,    /* "id-cct-PKIData" */
     362,    /* "id-cct-PKIResponse" */
     360,    /* "id-cct-crs" */
      81,    /* "id-ce" */
     680,    /* "id-characteristic-two-basis" */
     263,    /* "id-cmc" */
     334,    /* "id-cmc-addExtensions" */
     346,    /* "id-cmc-confirmCertAcceptance" */
     330,    /* "id-cmc-dataReturn" */
     336,    /* "id-cmc-decryptedPOP" */
     335,    /* "id-cmc-encryptedPOP" */
     339,    /* "id-cmc-getCRL" */
     338,    /* "id-cmc-getCert" */
     328,    /* "id-cmc-identification" */
     329,    /* "id-cmc-identityProof" */
     337,    /* "id-cmc-lraPOPWitness" */
     344,    /* "id-cmc-popLinkRandom" */
     345,    /* "id-cmc-popLinkWitness" */
     343,    /* "id-cmc-queryPending" */
     333,    /* "id-cmc-recipientNonce" */
     341,    /* "id-cmc-regInfo" */
     342,    /* "id-cmc-responseInfo" */
     340,    /* "id-cmc-revokeRequest" */
     332,    /* "id-cmc-senderNonce" */
     327,    /* "id-cmc-statusInfo" */
     331,    /* "id-cmc-transactionId" */
    1238,    /* "id-cp" */
     787,    /* "id-ct-asciiTextWithCRLF" */
    1246,    /* "id-ct-geofeedCSVwithCRLF" */
    1237,    /* "id-ct-resourceTaggedAttest" */
    1234,    /* "id-ct-routeOriginAuthz" */
    1236,    /* "id-ct-rpkiGhostbusters" */
    1235,    /* "id-ct-rpkiManifest" */
    1247,    /* "id-ct-signedChecklist" */
    1060,    /* "id-ct-xml" */
     408,    /* "id-ecPublicKey" */
     508,    /* "id-hex-multipart-message" */
     507,    /* "id-hex-partial-message" */
     260,    /* "id-it" */
    1223,    /* "id-it-caCerts" */
     302,    /* "id-it-caKeyUpdateInfo" */
     298,    /* "id-it-caProtEncCert" */
    1225,    /* "id-it-certReqTemplate" */
     311,    /* "id-it-confirmWaitTime" */
     303,    /* "id-it-currentCRL" */
     300,    /* "id-it-encKeyPairTypes" */
     310,    /* "id-it-implicitConfirm" */
     308,    /* "id-it-keyPairParamRep" */
     307,    /* "id-it-keyPairParamReq" */
     312,    /* "id-it-origPKIMessage" */
     301,    /* "id-it-preferredSymmAlg" */
     309,    /* "id-it-revPassphrase" */
    1224,    /* "id-it-rootCaKeyUpdate" */
     299,    /* "id-it-signKeyPairTypes" */
     305,    /* "id-it-subscriptionRequest" */
     306,    /* "id-it-subscriptionResponse" */
     784,    /* "id-it-suppLangTags" */
     304,    /* "id-it-unsupportedOIDs" */
     128,    /* "id-kp" */
     280,    /* "id-mod-attribute-cert" */
     274,    /* "id-mod-cmc" */
     277,    /* "id-mod-cmp" */
     284,    /* "id-mod-cmp2000" */
     273,    /* "id-mod-crmf" */
     283,    /* "id-mod-dvcs" */
     275,    /* "id-mod-kea-profile-88" */
     276,    /* "id-mod-kea-profile-93" */
     282,    /* "id-mod-ocsp" */
     278,    /* "id-mod-qualified-cert-88" */
     279,    /* "id-mod-qualified-cert-93" */
     281,    /* "id-mod-timestamp-protocol" */
     264,    /* "id-on" */
     347,    /* "id-on-personalData" */
     265,    /* "id-pda" */
     352,    /* "id-pda-countryOfCitizenship" */
     353,    /* "id-pda-countryOfResidence" */
     348,    /* "id-pda-dateOfBirth" */
     351,    /* "id-pda-gender" */
     349,    /* "id-pda-placeOfBirth" */
     175,    /* "id-pe" */
    1031,    /* "id-pkinit" */
     261,    /* "id-pkip" */
     258,    /* "id-pkix-mod" */
     269,    /* "id-pkix1-explicit-88" */
     271,    /* "id-pkix1-explicit-93" */
     270,    /* "id-pkix1-implicit-88" */
     272,    /* "id-pkix1-implicit-93" */
     662,    /* "id-ppl" */
     267,    /* "id-qcs" */
     359,    /* "id-qcs-pkixQCSyntax-v1" */
     259,    /* "id-qt" */
     313,    /* "id-regCtrl" */
     316,    /* "id-regCtrl-authenticator" */
     319,    /* "id-regCtrl-oldCertID" */
     318,    /* "id-regCtrl-pkiArchiveOptions" */
     317,    /* "id-regCtrl-pkiPublicationInfo" */
     320,    /* "id-regCtrl-protocolEncrKey" */
     315,    /* "id-regCtrl-regToken" */
     314,    /* "id-regInfo" */
     322,    /* "id-regInfo-certReq" */
     321,    /* "id-regInfo-utf8Pairs" */
     191,    /* "id-smime-aa" */
     215,    /* "id-smime-aa-contentHint" */
     218,    /* "id-smime-aa-contentIdentifier" */
     221,    /* "id-smime-aa-contentReference" */
     240,    /* "id-smime-aa-dvcs-dvc" */
     217,    /* "id-smime-aa-encapContentType" */
     222,    /* "id-smime-aa-encrypKeyPref" */
     220,    /* "id-smime-aa-equivalentLabels" */
     232,    /* "id-smime-aa-ets-CertificateRefs" */
     233,    /* "id-smime-aa-ets-RevocationRefs" */
     238,    /* "id-smime-aa-ets-archiveTimeStamp" */
     237,    /* "id-smime-aa-ets-certCRLTimestamp" */
     234,    /* "id-smime-aa-ets-certValues" */
     227,    /* "id-smime-aa-ets-commitmentType" */
     231,    /* "id-smime-aa-ets-contentTimestamp" */
     236,    /* "id-smime-aa-ets-escTimeStamp" */
     230,    /* "id-smime-aa-ets-otherSigCert" */
     235,    /* "id-smime-aa-ets-revocationValues" */
     226,    /* "id-smime-aa-ets-sigPolicyId" */
     229,    /* "id-smime-aa-ets-signerAttr" */
     228,    /* "id-smime-aa-ets-signerLocation" */
     219,    /* "id-smime-aa-macValue" */
     214,    /* "id-smime-aa-mlExpandHistory" */
     216,    /* "id-smime-aa-msgSigDigest" */
     212,    /* "id-smime-aa-receiptRequest" */
     213,    /* "id-smime-aa-securityLabel" */
     239,    /* "id-smime-aa-signatureType" */
     223,    /* "id-smime-aa-signingCertificate" */
    1086,    /* "id-smime-aa-signingCertificateV2" */
     224,    /* "id-smime-aa-smimeEncryptCerts" */
     225,    /* "id-smime-aa-timeStampToken" */
     192,    /* "id-smime-alg" */
     243,    /* "id-smime-alg-3DESwrap" */
     246,    /* "id-smime-alg-CMS3DESwrap" */
     245,    /* "id-smime-alg-ESDH" */
     241,    /* "id-smime-alg-ESDHwith3DES" */
     193,    /* "id-smime-cd" */
     248,    /* "id-smime-cd-ldap" */
     190,    /* "id-smime-ct" */
     210,    /* "id-smime-ct-DVCSRequestData" */
     211,    /* "id-smime-ct-DVCSResponseData" */
     208,    /* "id-smime-ct-TDTInfo" */
     207,    /* "id-smime-ct-TSTInfo" */
     205,    /* "id-smime-ct-authData" */
    1059,    /* "id-smime-ct-authEnvelopedData" */
     786,    /* "id-smime-ct-compressedData" */
    1058,    /* "id-smime-ct-contentCollection" */
     209,    /* "id-smime-ct-contentInfo" */
     206,    /* "id-smime-ct-publishCert" */
     204,    /* "id-smime-ct-receipt" */
     195,    /* "id-smime-cti" */
     255,    /* "id-smime-cti-ets-proofOfApproval" */
     256,    /* "id-smime-cti-ets-proofOfCreation" */
     253,    /* "id-smime-cti-ets-proofOfDelivery" */
     251,    /* "id-smime-cti-ets-proofOfOrigin" */
     252,    /* "id-smime-cti-ets-proofOfReceipt" */
     254,    /* "id-smime-cti-ets-proofOfSender" */
     189,    /* "id-smime-mod" */
     196,    /* "id-smime-mod-cms" */
     197,    /* "id-smime-mod-ess" */
     202,    /* "id-smime-mod-ets-eSigPolicy-88" */
     203,    /* "id-smime-mod-ets-eSigPolicy-97" */
     200,    /* "id-smime-mod-ets-eSignature-88" */
     201,    /* "id-smime-mod-ets-eSignature-97" */
     199,    /* "id-smime-mod-msg-v3" */
     198,    /* "id-smime-mod-oid" */
     194,    /* "id-smime-spq" */
     250,    /* "id-smime-spq-ets-sqt-unotice" */
     249,    /* "id-smime-spq-ets-sqt-uri" */
     676,    /* "identified-organization" */
    1170,    /* "ieee" */
     461,    /* "info" */
     101,    /* "initials" */
     869,    /* "internationaliSDNNumber" */
    1241,    /* "ipAddr-asNumber" */
    1242,    /* "ipAddr-asNumberv2" */
    1022,    /* "ipsec Internet Key Exchange" */
     749,    /* "ipsec3" */
     750,    /* "ipsec4" */
     181,    /* "iso" */
     623,    /* "issuer capabilities" */
     645,    /* "itu-t" */
     492,    /* "janetMailbox" */
     646,    /* "joint-iso-itu-t" */
     957,    /* "jurisdictionCountryName" */
     955,    /* "jurisdictionLocalityName" */
     956,    /* "jurisdictionStateOrProvinceName" */
     150,    /* "keyBag" */
    1196,    /* "kmac128" */
    1197,    /* "kmac256" */
    1063,    /* "kx-any" */
    1039,    /* "kx-dhe" */
    1041,    /* "kx-dhe-psk" */
    1038,    /* "kx-ecdhe" */
    1040,    /* "kx-ecdhe-psk" */
    1043,    /* "kx-psk" */
    1037,    /* "kx-rsa" */
    1042,    /* "kx-rsa-psk" */
    1252,    /* "kx-sm2" */
    1253,    /* "kx-sm2dhe" */
    1044,    /* "kx-srp" */
     477,    /* "lastModifiedBy" */
     476,    /* "lastModifiedTime" */
     157,    /* "localKeyID" */
      15,    /* "localityName" */
     480,    /* "mXRecord" */
     493,    /* "mailPreferenceOption" */
     467,    /* "manager" */
       4,    /* "md5" */
     114,    /* "md5-sha1" */
     104,    /* "md5WithRSA" */
       8,    /* "md5WithRSAEncryption" */
     875,    /* "member" */
     602,    /* "merchant initiated auth" */
     514,    /* "message extensions" */
      51,    /* "messageDigest" */
     911,    /* "mgf1" */
     506,    /* "mime-mhs-bodies" */
     505,    /* "mime-mhs-headings" */
     488,    /* "mobileTelephoneNumber" */
    1212,    /* "modp_1536" */
    1213,    /* "modp_2048" */
    1214,    /* "modp_3072" */
    1215,    /* "modp_4096" */
    1216,    /* "modp_6144" */
    1217,    /* "modp_8192" */
     481,    /* "nSRecord" */
     173,    /* "name" */
     681,    /* "onBasis" */
     379,    /* "org" */
    1089,    /* "organizationIdentifier" */
      17,    /* "organizationName" */
     491,    /* "organizationalStatus" */
      18,    /* "organizationalUnitName" */
    1141,    /* "oscca" */
     475,    /* "otherMailbox" */
     876,    /* "owner" */
     935,    /* "pSpecified" */
     489,    /* "pagerTelephoneNumber" */
     782,    /* "password based MAC" */
     374,    /* "path" */
     621,    /* "payment gateway capabilities" */
      10,    /* "pbeWithMD5AndDES-CBC" */
     144,    /* "pbeWithSHA1And128BitRC4" */
     147,    /* "pbeWithSHA1And2-KeyTripleDES-CBC" */
     146,    /* "pbeWithSHA1And3-KeyTripleDES-CBC" */
     145,    /* "pbeWithSHA1And40BitRC4" */
     170,    /* "pbeWithSHA1AndDES-CBC" */
     499,    /* "personalSignature" */
     487,    /* "personalTitle" */
     464,    /* "photo" */
     863,    /* "physicalDeliveryOfficeName" */
     437,    /* "pilot" */
     439,    /* "pilotAttributeSyntax" */
     438,    /* "pilotAttributeType" */
     479,    /* "pilotAttributeType27" */
     456,    /* "pilotDSA" */
     441,    /* "pilotGroups" */
     444,    /* "pilotObject" */
     440,    /* "pilotObjectClass" */
     455,    /* "pilotOrganization" */
     445,    /* "pilotPerson" */
     186,    /* "pkcs1" */
      27,    /* "pkcs3" */
     187,    /* "pkcs5" */
      20,    /* "pkcs7" */
      21,    /* "pkcs7-data" */
      25,    /* "pkcs7-digestData" */
      26,    /* "pkcs7-encryptedData" */
      23,    /* "pkcs7-envelopedData" */
      24,    /* "pkcs7-signedAndEnvelopedData" */
      22,    /* "pkcs7-signedData" */
     151,    /* "pkcs8ShroudedKeyBag" */
      47,    /* "pkcs9" */
    1061,    /* "poly1305" */
     862,    /* "postOfficeBox" */
     861,    /* "postalAddress" */
     661,    /* "postalCode" */
     683,    /* "ppBasis" */
     872,    /* "preferredDeliveryMethod" */
     873,    /* "presentationAddress" */
     406,    /* "prime-field" */
     409,    /* "prime192v1" */
     410,    /* "prime192v2" */
     411,    /* "prime192v3" */
     412,    /* "prime239v1" */
     413,    /* "prime239v2" */
     414,    /* "prime239v3" */
     415,    /* "prime256v1" */
     886,    /* "protocolInformation" */
     510,    /* "pseudonym" */
     435,    /* "pss" */
     286,    /* "qcStatements" */
     457,    /* "qualityLabelledData" */
     450,    /* "rFC822localPart" */
       5,    /* "rc4" */
      97,    /* "rc4-40" */
     915,    /* "rc4-hmac-md5" */
     120,    /* "rc5-cbc" */
     122,    /* "rc5-cfb" */
     121,    /* "rc5-ecb" */
     123,    /* "rc5-ofb" */
     870,    /* "registeredAddress" */
     460,    /* "rfc822Mailbox" */
     400,    /* "role" */
     877,    /* "roleOccupant" */
     448,    /* "room" */
     463,    /* "roomNumber" */
      19,    /* "rsa" */
       6,    /* "rsaEncryption" */
     644,    /* "rsaOAEPEncryptionSET" */
     377,    /* "rsaSignature" */
     919,    /* "rsaesOaep" */
     912,    /* "rsassaPss" */
     482,    /* "sOARecord" */
     155,    /* "safeContentsBag" */
     291,    /* "sbgp-autonomousSysNum" */
    1240,    /* "sbgp-autonomousSysNumv2" */
     290,    /* "sbgp-ipAddrBlock" */
    1239,    /* "sbgp-ipAddrBlockv2" */
     292,    /* "sbgp-routerIdentifier" */
     973,    /* "scrypt" */
     159,    /* "sdsiCertificate" */
     859,    /* "searchGuide" */
     704,    /* "secp112r1" */
     705,    /* "secp112r2" */
     706,    /* "secp128r1" */
     707,    /* "secp128r2" */
     708,    /* "secp160k1" */
     709,    /* "secp160r1" */
     710,    /* "secp160r2" */
     711,    /* "secp192k1" */
     712,    /* "secp224k1" */
     713,    /* "secp224r1" */
     714,    /* "secp256k1" */
     715,    /* "secp384r1" */
     716,    /* "secp521r1" */
     154,    /* "secretBag" */
     474,    /* "secretary" */
     717,    /* "sect113r1" */
     718,    /* "sect113r2" */
     719,    /* "sect131r1" */
     720,    /* "sect131r2" */
     721,    /* "sect163k1" */
     722,    /* "sect163r1" */
     723,    /* "sect163r2" */
     724,    /* "sect193r1" */
     725,    /* "sect193r2" */
     726,    /* "sect233k1" */
     727,    /* "sect233r1" */
     728,    /* "sect239k1" */
     729,    /* "sect283k1" */
     730,    /* "sect283r1" */
     731,    /* "sect409k1" */
     732,    /* "sect409r1" */
     733,    /* "sect571k1" */
     734,    /* "sect571r1" */
     635,    /* "secure device signature" */
     878,    /* "seeAlso" */
     105,    /* "serialNumber" */
     625,    /* "set-addPolicy" */
     515,    /* "set-attr" */
     518,    /* "set-brand" */
     638,    /* "set-brand-AmericanExpress" */
     637,    /* "set-brand-Diners" */
     636,    /* "set-brand-IATA-ATA" */
     639,    /* "set-brand-JCB" */
     641,    /* "set-brand-MasterCard" */
     642,    /* "set-brand-Novus" */
     640,    /* "set-brand-Visa" */
     516,    /* "set-policy" */
     607,    /* "set-policy-root" */
     624,    /* "set-rootKeyThumb" */
     620,    /* "setAttr-Cert" */
     628,    /* "setAttr-IssCap-CVM" */
     630,    /* "setAttr-IssCap-Sig" */
     629,    /* "setAttr-IssCap-T2" */
     627,    /* "setAttr-Token-B0Prime" */
     626,    /* "setAttr-Token-EMV" */
     622,    /* "setAttr-TokenType" */
     619,    /* "setCext-IssuerCapabilities" */
     615,    /* "setCext-PGWYcapabilities" */
     616,    /* "setCext-TokenIdentifier" */
     618,    /* "setCext-TokenType" */
     617,    /* "setCext-Track2Data" */
     611,    /* "setCext-cCertRequired" */
     609,    /* "setCext-certType" */
     608,    /* "setCext-hashedRoot" */
     610,    /* "setCext-merchData" */
     613,    /* "setCext-setExt" */
     614,    /* "setCext-setQualf" */
     612,    /* "setCext-tunneling" */
     540,    /* "setct-AcqCardCodeMsg" */
     576,    /* "setct-AcqCardCodeMsgTBE" */
     570,    /* "setct-AuthReqTBE" */
     534,    /* "setct-AuthReqTBS" */
     527,    /* "setct-AuthResBaggage" */
     571,    /* "setct-AuthResTBE" */
     572,    /* "setct-AuthResTBEX" */
     535,    /* "setct-AuthResTBS" */
     536,    /* "setct-AuthResTBSX" */
     528,    /* "setct-AuthRevReqBaggage" */
     577,    /* "setct-AuthRevReqTBE" */
     541,    /* "setct-AuthRevReqTBS" */
     529,    /* "setct-AuthRevResBaggage" */
     542,    /* "setct-AuthRevResData" */
     578,    /* "setct-AuthRevResTBE" */
     579,    /* "setct-AuthRevResTBEB" */
     543,    /* "setct-AuthRevResTBS" */
     573,    /* "setct-AuthTokenTBE" */
     537,    /* "setct-AuthTokenTBS" */
     600,    /* "setct-BCIDistributionTBS" */
     558,    /* "setct-BatchAdminReqData" */
     592,    /* "setct-BatchAdminReqTBE" */
     559,    /* "setct-BatchAdminResData" */
     593,    /* "setct-BatchAdminResTBE" */
     599,    /* "setct-CRLNotificationResTBS" */
     598,    /* "setct-CRLNotificationTBS" */
     580,    /* "setct-CapReqTBE" */
     581,    /* "setct-CapReqTBEX" */
     544,    /* "setct-CapReqTBS" */
     545,    /* "setct-CapReqTBSX" */
     546,    /* "setct-CapResData" */
     582,    /* "setct-CapResTBE" */
     583,    /* "setct-CapRevReqTBE" */
     584,    /* "setct-CapRevReqTBEX" */
     547,    /* "setct-CapRevReqTBS" */
     548,    /* "setct-CapRevReqTBSX" */
     549,    /* "setct-CapRevResData" */
     585,    /* "setct-CapRevResTBE" */
     538,    /* "setct-CapTokenData" */
     530,    /* "setct-CapTokenSeq" */
     574,    /* "setct-CapTokenTBE" */
     575,    /* "setct-CapTokenTBEX" */
     539,    /* "setct-CapTokenTBS" */
     560,    /* "setct-CardCInitResTBS" */
     566,    /* "setct-CertInqReqTBS" */
     563,    /* "setct-CertReqData" */
     595,    /* "setct-CertReqTBE" */
     596,    /* "setct-CertReqTBEX" */
     564,    /* "setct-CertReqTBS" */
     565,    /* "setct-CertResData" */
     597,    /* "setct-CertResTBE" */
     586,    /* "setct-CredReqTBE" */
     587,    /* "setct-CredReqTBEX" */
     550,    /* "setct-CredReqTBS" */
     551,    /* "setct-CredReqTBSX" */
     552,    /* "setct-CredResData" */
     588,    /* "setct-CredResTBE" */
     589,    /* "setct-CredRevReqTBE" */
     590,    /* "setct-CredRevReqTBEX" */
     553,    /* "setct-CredRevReqTBS" */
     554,    /* "setct-CredRevReqTBSX" */
     555,    /* "setct-CredRevResData" */
     591,    /* "setct-CredRevResTBE" */
     567,    /* "setct-ErrorTBS" */
     526,    /* "setct-HODInput" */
     561,    /* "setct-MeAqCInitResTBS" */
     522,    /* "setct-OIData" */
     519,    /* "setct-PANData" */
     521,    /* "setct-PANOnly" */
     520,    /* "setct-PANToken" */
     556,    /* "setct-PCertReqData" */
     557,    /* "setct-PCertResTBS" */
     523,    /* "setct-PI" */
     532,    /* "setct-PI-TBS" */
     524,    /* "setct-PIData" */
     525,    /* "setct-PIDataUnsigned" */
     568,    /* "setct-PIDualSignedTBE" */
     569,    /* "setct-PIUnsignedTBE" */
     531,    /* "setct-PInitResData" */
     533,    /* "setct-PResData" */
     594,    /* "setct-RegFormReqTBE" */
     562,    /* "setct-RegFormResTBS" */
     604,    /* "setext-pinAny" */
     603,    /* "setext-pinSecure" */
     605,    /* "setext-track2" */
      41,    /* "sha" */
      64,    /* "sha1" */
     115,    /* "sha1WithRSA" */
      65,    /* "sha1WithRSAEncryption" */
     675,    /* "sha224" */
     671,    /* "sha224WithRSAEncryption" */
     672,    /* "sha256" */
     668,    /* "sha256WithRSAEncryption" */
    1096,    /* "sha3-224" */
    1097,    /* "sha3-256" */
    1098,    /* "sha3-384" */
    1099,    /* "sha3-512" */
     673,    /* "sha384" */
     669,    /* "sha384WithRSAEncryption" */
     674,    /* "sha512" */
    1094,    /* "sha512-224" */
    1145,    /* "sha512-224WithRSAEncryption" */
    1095,    /* "sha512-256" */
    1146,    /* "sha512-256WithRSAEncryption" */
     670,    /* "sha512WithRSAEncryption" */
      42,    /* "shaWithRSAEncryption" */
    1100,    /* "shake128" */
    1101,    /* "shake256" */
      52,    /* "signingTime" */
     454,    /* "simpleSecurityObject" */
     496,    /* "singleLevelQuality" */
    1062,    /* "siphash" */
    1142,    /* "sm-scheme" */
    1172,    /* "sm2" */
    1143,    /* "sm3" */
    1144,    /* "sm3WithRSAEncryption" */
    1134,    /* "sm4-cbc" */
    1251,    /* "sm4-ccm" */
    1137,    /* "sm4-cfb" */
    1136,    /* "sm4-cfb1" */
    1138,    /* "sm4-cfb8" */
    1139,    /* "sm4-ctr" */
    1133,    /* "sm4-ecb" */
    1250,    /* "sm4-gcm" */
    1135,    /* "sm4-ofb" */
    1203,    /* "sshkdf" */
    1205,    /* "sskdf" */
      16,    /* "stateOrProvinceName" */
     660,    /* "streetAddress" */
     498,    /* "subtreeMaximumQuality" */
     497,    /* "subtreeMinimumQuality" */
     890,    /* "supportedAlgorithms" */
     874,    /* "supportedApplicationContext" */
     100,    /* "surname" */
     864,    /* "telephoneNumber" */
     866,    /* "teletexTerminalIdentifier" */
     865,    /* "telexNumber" */
     459,    /* "textEncodedORAddress" */
     293,    /* "textNotice" */
     106,    /* "title" */
    1021,    /* "tls1-prf" */
     682,    /* "tpBasis" */
    1151,    /* "ua-pki" */
     436,    /* "ucl" */
       0,    /* "undefined" */
     102,    /* "uniqueIdentifier" */
     888,    /* "uniqueMember" */
      55,    /* "unstructuredAddress" */
      49,    /* "unstructuredName" */
     880,    /* "userCertificate" */
     465,    /* "userClass" */
     458,    /* "userId" */
     879,    /* "userPassword" */
     373,    /* "valid" */
     678,    /* "wap" */
     679,    /* "wap-wsg" */
     735,    /* "wap-wsg-idm-ecid-wtls1" */
     743,    /* "wap-wsg-idm-ecid-wtls10" */
     744,    /* "wap-wsg-idm-ecid-wtls11" */
     745,    /* "wap-wsg-idm-ecid-wtls12" */
     736,    /* "wap-wsg-idm-ecid-wtls3" */
     737,    /* "wap-wsg-idm-ecid-wtls4" */
     738,    /* "wap-wsg-idm-ecid-wtls5" */
     739,    /* "wap-wsg-idm-ecid-wtls6" */
     740,    /* "wap-wsg-idm-ecid-wtls7" */
     741,    /* "wap-wsg-idm-ecid-wtls8" */
     742,    /* "wap-wsg-idm-ecid-wtls9" */
    1268,    /* "wbsm4-baiwu-cbc" */
    1271,    /* "wbsm4-baiwu-ccm" */
    1278,    /* "wbsm4-baiwu-cfb" */
    1269,    /* "wbsm4-baiwu-ctr" */
    1267,    /* "wbsm4-baiwu-ecb" */
    1270,    /* "wbsm4-baiwu-gcm" */
    1277,    /* "wbsm4-baiwu-ofb" */
    1273,    /* "wbsm4-wsise-cbc" */
    1276,    /* "wbsm4-wsise-ccm" */
    1280,    /* "wbsm4-wsise-cfb" */
    1274,    /* "wbsm4-wsise-ctr" */
    1272,    /* "wbsm4-wsise-ecb" */
    1275,    /* "wbsm4-wsise-gcm" */
    1279,    /* "wbsm4-wsise-ofb" */
    1261,    /* "wbsm4-xiaolai-cbc" */
    1266,    /* "wbsm4-xiaolai-ccm" */
    1263,    /* "wbsm4-xiaolai-cfb" */
    1264,    /* "wbsm4-xiaolai-ctr" */
    1260,    /* "wbsm4-xiaolai-ecb" */
    1265,    /* "wbsm4-xiaolai-gcm" */
    1262,    /* "wbsm4-xiaolai-ofb" */
     868,    /* "x121Address" */
     503,    /* "x500UniqueIdentifier" */
     158,    /* "x509Certificate" */
     160,    /* "x509Crl" */
    1207,    /* "x942kdf" */
    1206,    /* "x963kdf" */
     125,    /* "zlib compression" */
    1248,    /* "zuc" */
    1249,    /* "zuc-128-eea3" */
    1255,    /* "zuc-128-eia3" */
};

#define NUM_OBJ 926
static const unsigned int obj_objs[NUM_OBJ] = {
       0,    /* OBJ_undef                        0 */
     181,    /* OBJ_iso                          1 */
     393,    /* OBJ_joint_iso_ccitt              OBJ_joint_iso_itu_t */
     404,    /* OBJ_ccitt                        OBJ_itu_t */
     645,    /* OBJ_itu_t                        0 */
     646,    /* OBJ_joint_iso_itu_t              2 */
     434,    /* OBJ_data                         0 9 */
     182,    /* OBJ_member_body                  1 2 */
     379,    /* OBJ_org                          1 3 */
     676,    /* OBJ_identified_organization      1 3 */
      11,    /* OBJ_X500                         2 5 */
     647,    /* OBJ_international_organizations  2 23 */
     380,    /* OBJ_dod                          1 3 6 */
    1170,    /* OBJ_ieee                         1 3 111 */
      12,    /* OBJ_X509                         2 5 4 */
     378,    /* OBJ_X500algorithms               2 5 8 */
      81,    /* OBJ_id_ce                        2 5 29 */
     512,    /* OBJ_id_set                       2 23 42 */
     678,    /* OBJ_wap                          2 23 43 */
     435,    /* OBJ_pss                          0 9 2342 */
    1140,    /* OBJ_ISO_CN                       1 2 156 */
    1150,    /* OBJ_ISO_UA                       1 2 804 */
     183,    /* OBJ_ISO_US                       1 2 840 */
     381,    /* OBJ_iana                         1 3 6 1 */
    1034,    /* OBJ_X25519                       1 3 101 110 */
    1035,    /* OBJ_X448                         1 3 101 111 */
    1087,    /* OBJ_ED25519                      1 3 101 112 */
    1088,    /* OBJ_ED448                        1 3 101 113 */
     677,    /* OBJ_certicom_arc                 1 3 132 */
     394,    /* OBJ_selected_attribute_types     2 5 1 5 */
      13,    /* OBJ_commonName                   2 5 4 3 */
     100,    /* OBJ_surname                      2 5 4 4 */
     105,    /* OBJ_serialNumber                 2 5 4 5 */
      14,    /* OBJ_countryName                  2 5 4 6 */
      15,    /* OBJ_localityName                 2 5 4 7 */
      16,    /* OBJ_stateOrProvinceName          2 5 4 8 */
     660,    /* OBJ_streetAddress                2 5 4 9 */
      17,    /* OBJ_organizationName             2 5 4 10 */
      18,    /* OBJ_organizationalUnitName       2 5 4 11 */
     106,    /* OBJ_title                        2 5 4 12 */
     107,    /* OBJ_description                  2 5 4 13 */
     859,    /* OBJ_searchGuide                  2 5 4 14 */
     860,    /* OBJ_businessCategory             2 5 4 15 */
     861,    /* OBJ_postalAddress                2 5 4 16 */
     661,    /* OBJ_postalCode                   2 5 4 17 */
     862,    /* OBJ_postOfficeBox                2 5 4 18 */
     863,    /* OBJ_physicalDeliveryOfficeName   2 5 4 19 */
     864,    /* OBJ_telephoneNumber              2 5 4 20 */
     865,    /* OBJ_telexNumber                  2 5 4 21 */
     866,    /* OBJ_teletexTerminalIdentifier    2 5 4 22 */
     867,    /* OBJ_facsimileTelephoneNumber     2 5 4 23 */
     868,    /* OBJ_x121Address                  2 5 4 24 */
     869,    /* OBJ_internationaliSDNNumber      2 5 4 25 */
     870,    /* OBJ_registeredAddress            2 5 4 26 */
     871,    /* OBJ_destinationIndicator         2 5 4 27 */
     872,    /* OBJ_preferredDeliveryMethod      2 5 4 28 */
     873,    /* OBJ_presentationAddress          2 5 4 29 */
     874,    /* OBJ_supportedApplicationContext  2 5 4 30 */
     875,    /* OBJ_member                       2 5 4 31 */
     876,    /* OBJ_owner                        2 5 4 32 */
     877,    /* OBJ_roleOccupant                 2 5 4 33 */
     878,    /* OBJ_seeAlso                      2 5 4 34 */
     879,    /* OBJ_userPassword                 2 5 4 35 */
     880,    /* OBJ_userCertificate              2 5 4 36 */
     881,    /* OBJ_cACertificate                2 5 4 37 */
     882,    /* OBJ_authorityRevocationList      2 5 4 38 */
     883,    /* OBJ_certificateRevocationList    2 5 4 39 */
     884,    /* OBJ_crossCertificatePair         2 5 4 40 */
     173,    /* OBJ_name                         2 5 4 41 */
      99,    /* OBJ_givenName                    2 5 4 42 */
     101,    /* OBJ_initials                     2 5 4 43 */
     509,    /* OBJ_generationQualifier          2 5 4 44 */
     503,    /* OBJ_x500UniqueIdentifier         2 5 4 45 */
     174,    /* OBJ_dnQualifier                  2 5 4 46 */
     885,    /* OBJ_enhancedSearchGuide          2 5 4 47 */
     886,    /* OBJ_protocolInformation          2 5 4 48 */
     887,    /* OBJ_distinguishedName            2 5 4 49 */
     888,    /* OBJ_uniqueMember                 2 5 4 50 */
     889,    /* OBJ_houseIdentifier              2 5 4 51 */
     890,    /* OBJ_supportedAlgorithms          2 5 4 52 */
     891,    /* OBJ_deltaRevocationList          2 5 4 53 */
     892,    /* OBJ_dmdName                      2 5 4 54 */
     510,    /* OBJ_pseudonym                    2 5 4 65 */
     400,    /* OBJ_role                         2 5 4 72 */
    1089,    /* OBJ_organizationIdentifier       2 5 4 97 */
    1090,    /* OBJ_countryCode3c                2 5 4 98 */
    1091,    /* OBJ_countryCode3n                2 5 4 99 */
    1092,    /* OBJ_dnsName                      2 5 4 100 */
     769,    /* OBJ_subject_directory_attributes 2 5 29 9 */
      82,    /* OBJ_subject_key_identifier       2 5 29 14 */
      83,    /* OBJ_key_usage                    2 5 29 15 */
      84,    /* OBJ_private_key_usage_period     2 5 29 16 */
      85,    /* OBJ_subject_alt_name             2 5 29 17 */
      86,    /* OBJ_issuer_alt_name              2 5 29 18 */
      87,    /* OBJ_basic_constraints            2 5 29 19 */
      88,    /* OBJ_crl_number                   2 5 29 20 */
     141,    /* OBJ_crl_reason                   2 5 29 21 */
     430,    /* OBJ_hold_instruction_code        2 5 29 23 */
     142,    /* OBJ_invalidity_date              2 5 29 24 */
     140,    /* OBJ_delta_crl                    2 5 29 27 */
     770,    /* OBJ_issuing_distribution_point   2 5 29 28 */
     771,    /* OBJ_certificate_issuer           2 5 29 29 */
     666,    /* OBJ_name_constraints             2 5 29 30 */
     103,    /* OBJ_crl_distribution_points      2 5 29 31 */
      89,    /* OBJ_certificate_policies         2 5 29 32 */
     747,    /* OBJ_policy_mappings              2 5 29 33 */
      90,    /* OBJ_authority_key_identifier     2 5 29 35 */
     401,    /* OBJ_policy_constraints           2 5 29 36 */
     126,    /* OBJ_ext_key_usage                2 5 29 37 */
     857,    /* OBJ_freshest_crl                 2 5 29 46 */
     748,    /* OBJ_inhibit_any_policy           2 5 29 54 */
     402,    /* OBJ_target_information           2 5 29 55 */
     403,    /* OBJ_no_rev_avail                 2 5 29 56 */
     513,    /* OBJ_set_ctype                    2 23 42 0 */
     514,    /* OBJ_set_msgExt                   2 23 42 1 */
     515,    /* OBJ_set_attr                     2 23 42 3 */
     516,    /* OBJ_set_policy                   2 23 42 5 */
     517,    /* OBJ_set_certExt                  2 23 42 7 */
     518,    /* OBJ_set_brand                    2 23 42 8 */
     679,    /* OBJ_wap_wsg                      2 23 43 1 */
     382,    /* OBJ_Directory                    1 3 6 1 1 */
     383,    /* OBJ_Management                   1 3 6 1 2 */
     384,    /* OBJ_Experimental                 1 3 6 1 3 */
     385,    /* OBJ_Private                      1 3 6 1 4 */
     386,    /* OBJ_Security                     1 3 6 1 5 */
     387,    /* OBJ_SNMPv2                       1 3 6 1 6 */
     388,    /* OBJ_Mail                         1 3 6 1 7 */
     376,    /* OBJ_algorithm                    1 3 14 3 2 */
     395,    /* OBJ_clearance                    2 5 1 5 55 */
      19,    /* OBJ_rsa                          2 5 8 1 1 */
     746,    /* OBJ_any_policy                   2 5 29 32 0 */
     910,    /* OBJ_anyExtendedKeyUsage          2 5 29 37 0 */
     519,    /* OBJ_setct_PANData                2 23 42 0 0 */
     520,    /* OBJ_setct_PANToken               2 23 42 0 1 */
     521,    /* OBJ_setct_PANOnly                2 23 42 0 2 */
     522,    /* OBJ_setct_OIData                 2 23 42 0 3 */
     523,    /* OBJ_setct_PI                     2 23 42 0 4 */
     524,    /* OBJ_setct_PIData                 2 23 42 0 5 */
     525,    /* OBJ_setct_PIDataUnsigned         2 23 42 0 6 */
     526,    /* OBJ_setct_HODInput               2 23 42 0 7 */
     527,    /* OBJ_setct_AuthResBaggage         2 23 42 0 8 */
     528,    /* OBJ_setct_AuthRevReqBaggage      2 23 42 0 9 */
     529,    /* OBJ_setct_AuthRevResBaggage      2 23 42 0 10 */
     530,    /* OBJ_setct_CapTokenSeq            2 23 42 0 11 */
     531,    /* OBJ_setct_PInitResData           2 23 42 0 12 */
     532,    /* OBJ_setct_PI_TBS                 2 23 42 0 13 */
     533,    /* OBJ_setct_PResData               2 23 42 0 14 */
     534,    /* OBJ_setct_AuthReqTBS             2 23 42 0 16 */
     535,    /* OBJ_setct_AuthResTBS             2 23 42 0 17 */
     536,    /* OBJ_setct_AuthResTBSX            2 23 42 0 18 */
     537,    /* OBJ_setct_AuthTokenTBS           2 23 42 0 19 */
     538,    /* OBJ_setct_CapTokenData           2 23 42 0 20 */
     539,    /* OBJ_setct_CapTokenTBS            2 23 42 0 21 */
     540,    /* OBJ_setct_AcqCardCodeMsg         2 23 42 0 22 */
     541,    /* OBJ_setct_AuthRevReqTBS          2 23 42 0 23 */
     542,    /* OBJ_setct_AuthRevResData         2 23 42 0 24 */
     543,    /* OBJ_setct_AuthRevResTBS          2 23 42 0 25 */
     544,    /* OBJ_setct_CapReqTBS              2 23 42 0 26 */
     545,    /* OBJ_setct_CapReqTBSX             2 23 42 0 27 */
     546,    /* OBJ_setct_CapResData             2 23 42 0 28 */
     547,    /* OBJ_setct_CapRevReqTBS           2 23 42 0 29 */
     548,    /* OBJ_setct_CapRevReqTBSX          2 23 42 0 30 */
     549,    /* OBJ_setct_CapRevResData          2 23 42 0 31 */
     550,    /* OBJ_setct_CredReqTBS             2 23 42 0 32 */
     551,    /* OBJ_setct_CredReqTBSX            2 23 42 0 33 */
     552,    /* OBJ_setct_CredResData            2 23 42 0 34 */
     553,    /* OBJ_setct_CredRevReqTBS          2 23 42 0 35 */
     554,    /* OBJ_setct_CredRevReqTBSX         2 23 42 0 36 */
     555,    /* OBJ_setct_CredRevResData         2 23 42 0 37 */
     556,    /* OBJ_setct_PCertReqData           2 23 42 0 38 */
     557,    /* OBJ_setct_PCertResTBS            2 23 42 0 39 */
     558,    /* OBJ_setct_BatchAdminReqData      2 23 42 0 40 */
     559,    /* OBJ_setct_BatchAdminResData      2 23 42 0 41 */
     560,    /* OBJ_setct_CardCInitResTBS        2 23 42 0 42 */
     561,    /* OBJ_setct_MeAqCInitResTBS        2 23 42 0 43 */
     562,    /* OBJ_setct_RegFormResTBS          2 23 42 0 44 */
     563,    /* OBJ_setct_CertReqData            2 23 42 0 45 */
     564,    /* OBJ_setct_CertReqTBS             2 23 42 0 46 */
     565,    /* OBJ_setct_CertResData            2 23 42 0 47 */
     566,    /* OBJ_setct_CertInqReqTBS          2 23 42 0 48 */
     567,    /* OBJ_setct_ErrorTBS               2 23 42 0 49 */
     568,    /* OBJ_setct_PIDualSignedTBE        2 23 42 0 50 */
     569,    /* OBJ_setct_PIUnsignedTBE          2 23 42 0 51 */
     570,    /* OBJ_setct_AuthReqTBE             2 23 42 0 52 */
     571,    /* OBJ_setct_AuthResTBE             2 23 42 0 53 */
     572,    /* OBJ_setct_AuthResTBEX            2 23 42 0 54 */
     573,    /* OBJ_setct_AuthTokenTBE           2 23 42 0 55 */
     574,    /* OBJ_setct_CapTokenTBE            2 23 42 0 56 */
     575,    /* OBJ_setct_CapTokenTBEX           2 23 42 0 57 */
     576,    /* OBJ_setct_AcqCardCodeMsgTBE      2 23 42 0 58 */
     577,    /* OBJ_setct_AuthRevReqTBE          2 23 42 0 59 */
     578,    /* OBJ_setct_AuthRevResTBE          2 23 42 0 60 */
     579,    /* OBJ_setct_AuthRevResTBEB         2 23 42 0 61 */
     580,    /* OBJ_setct_CapReqTBE              2 23 42 0 62 */
     581,    /* OBJ_setct_CapReqTBEX             2 23 42 0 63 */
     582,    /* OBJ_setct_CapResTBE              2 23 42 0 64 */
     583,    /* OBJ_setct_CapRevReqTBE           2 23 42 0 65 */
     584,    /* OBJ_setct_CapRevReqTBEX          2 23 42 0 66 */
     585,    /* OBJ_setct_CapRevResTBE           2 23 42 0 67 */
     586,    /* OBJ_setct_CredReqTBE             2 23 42 0 68 */
     587,    /* OBJ_setct_CredReqTBEX            2 23 42 0 69 */
     588,    /* OBJ_setct_CredResTBE             2 23 42 0 70 */
     589,    /* OBJ_setct_CredRevReqTBE          2 23 42 0 71 */
     590,    /* OBJ_setct_CredRevReqTBEX         2 23 42 0 72 */
     591,    /* OBJ_setct_CredRevResTBE          2 23 42 0 73 */
     592,    /* OBJ_setct_BatchAdminReqTBE       2 23 42 0 74 */
     593,    /* OBJ_setct_BatchAdminResTBE       2 23 42 0 75 */
     594,    /* OBJ_setct_RegFormReqTBE          2 23 42 0 76 */
     595,    /* OBJ_setct_CertReqTBE             2 23 42 0 77 */
     596,    /* OBJ_setct_CertReqTBEX            2 23 42 0 78 */
     597,    /* OBJ_setct_CertResTBE             2 23 42 0 79 */
     598,    /* OBJ_setct_CRLNotificationTBS     2 23 42 0 80 */
     599,    /* OBJ_setct_CRLNotificationResTBS  2 23 42 0 81 */
     600,    /* OBJ_setct_BCIDistributionTBS     2 23 42 0 82 */
     601,    /* OBJ_setext_genCrypt              2 23 42 1 1 */
     602,    /* OBJ_setext_miAuth                2 23 42 1 3 */
     603,    /* OBJ_setext_pinSecure             2 23 42 1 4 */
     604,    /* OBJ_setext_pinAny                2 23 42 1 5 */
     605,    /* OBJ_setext_track2                2 23 42 1 7 */
     606,    /* OBJ_setext_cv                    2 23 42 1 8 */
     620,    /* OBJ_setAttr_Cert                 2 23 42 3 0 */
     621,    /* OBJ_setAttr_PGWYcap              2 23 42 3 1 */
     622,    /* OBJ_setAttr_TokenType            2 23 42 3 2 */
     623,    /* OBJ_setAttr_IssCap               2 23 42 3 3 */
     607,    /* OBJ_set_policy_root              2 23 42 5 0 */
     608,    /* OBJ_setCext_hashedRoot           2 23 42 7 0 */
     609,    /* OBJ_setCext_certType             2 23 42 7 1 */
     610,    /* OBJ_setCext_merchData            2 23 42 7 2 */
     611,    /* OBJ_setCext_cCertRequired        2 23 42 7 3 */
     612,    /* OBJ_setCext_tunneling            2 23 42 7 4 */
     613,    /* OBJ_setCext_setExt               2 23 42 7 5 */
     614,    /* OBJ_setCext_setQualf             2 23 42 7 6 */
     615,    /* OBJ_setCext_PGWYcapabilities     2 23 42 7 7 */
     616,    /* OBJ_setCext_TokenIdentifier      2 23 42 7 8 */
     617,    /* OBJ_setCext_Track2Data           2 23 42 7 9 */
     618,    /* OBJ_setCext_TokenType            2 23 42 7 10 */
     619,    /* OBJ_setCext_IssuerCapabilities   2 23 42 7 11 */
     636,    /* OBJ_set_brand_IATA_ATA           2 23 42 8 1 */
     640,    /* OBJ_set_brand_Visa               2 23 42 8 4 */
     641,    /* OBJ_set_brand_MasterCard         2 23 42 8 5 */
     637,    /* OBJ_set_brand_Diners             2 23 42 8 30 */
     638,    /* OBJ_set_brand_AmericanExpress    2 23 42 8 34 */
     639,    /* OBJ_set_brand_JCB                2 23 42 8 35 */
    1195,    /* OBJ_gmac                         1 0 9797 3 4 */
    1141,    /* OBJ_oscca                        1 2 156 10197 */
     184,    /* OBJ_X9_57                        1 2 840 10040 */
     405,    /* OBJ_ansi_X9_62                   1 2 840 10045 */
     389,    /* OBJ_Enterprises                  1 3 6 1 4 1 */
     504,    /* OBJ_mime_mhs                     1 3 6 1 7 1 */
     104,    /* OBJ_md5WithRSA                   1 3 14 3 2 3 */
      29,    /* OBJ_des_ecb                      1 3 14 3 2 6 */
      31,    /* OBJ_des_cbc                      1 3 14 3 2 7 */
      45,    /* OBJ_des_ofb64                    1 3 14 3 2 8 */
      30,    /* OBJ_des_cfb64                    1 3 14 3 2 9 */
     377,    /* OBJ_rsaSignature                 1 3 14 3 2 11 */
      67,    /* OBJ_dsa_2                        1 3 14 3 2 12 */
      66,    /* OBJ_dsaWithSHA                   1 3 14 3 2 13 */
      42,    /* OBJ_shaWithRSAEncryption         1 3 14 3 2 15 */
      32,    /* OBJ_des_ede_ecb                  1 3 14 3 2 17 */
      41,    /* OBJ_sha                          1 3 14 3 2 18 */
      64,    /* OBJ_sha1                         1 3 14 3 2 26 */
      70,    /* OBJ_dsaWithSHA1_2                1 3 14 3 2 27 */
     115,    /* OBJ_sha1WithRSA                  1 3 14 3 2 29 */
    1093,    /* OBJ_x509ExtAdmission             1 3 36 8 3 3 */
     143,    /* OBJ_sxnet                        1 3 101 1 4 1 */
    1171,    /* OBJ_ieee_siswg                   1 3 111 2 1619 */
     721,    /* OBJ_sect163k1                    1 3 132 0 1 */
     722,    /* OBJ_sect163r1                    1 3 132 0 2 */
     728,    /* OBJ_sect239k1                    1 3 132 0 3 */
     717,    /* OBJ_sect113r1                    1 3 132 0 4 */
     718,    /* OBJ_sect113r2                    1 3 132 0 5 */
     704,    /* OBJ_secp112r1                    1 3 132 0 6 */
     705,    /* OBJ_secp112r2                    1 3 132 0 7 */
     709,    /* OBJ_secp160r1                    1 3 132 0 8 */
     708,    /* OBJ_secp160k1                    1 3 132 0 9 */
     714,    /* OBJ_secp256k1                    1 3 132 0 10 */
     723,    /* OBJ_sect163r2                    1 3 132 0 15 */
     729,    /* OBJ_sect283k1                    1 3 132 0 16 */
     730,    /* OBJ_sect283r1                    1 3 132 0 17 */
     719,    /* OBJ_sect131r1                    1 3 132 0 22 */
     720,    /* OBJ_sect131r2                    1 3 132 0 23 */
     724,    /* OBJ_sect193r1                    1 3 132 0 24 */
     725,    /* OBJ_sect193r2                    1 3 132 0 25 */
     726,    /* OBJ_sect233k1                    1 3 132 0 26 */
     727,    /* OBJ_sect233r1                    1 3 132 0 27 */
     706,    /* OBJ_secp128r1                    1 3 132 0 28 */
     707,    /* OBJ_secp128r2                    1 3 132 0 29 */
     710,    /* OBJ_secp160r2                    1 3 132 0 30 */
     711,    /* OBJ_secp192k1                    1 3 132 0 31 */
     712,    /* OBJ_secp224k1                    1 3 132 0 32 */
     713,    /* OBJ_secp224r1                    1 3 132 0 33 */
     715,    /* OBJ_secp384r1                    1 3 132 0 34 */
     716,    /* OBJ_secp521r1                    1 3 132 0 35 */
     731,    /* OBJ_sect409k1                    1 3 132 0 36 */
     732,    /* OBJ_sect409r1                    1 3 132 0 37 */
     733,    /* OBJ_sect571k1                    1 3 132 0 38 */
     734,    /* OBJ_sect571r1                    1 3 132 0 39 */
     624,    /* OBJ_set_rootKeyThumb             2 23 42 3 0 0 */
     625,    /* OBJ_set_addPolicy                2 23 42 3 0 1 */
     626,    /* OBJ_setAttr_Token_EMV            2 23 42 3 2 1 */
     627,    /* OBJ_setAttr_Token_B0Prime        2 23 42 3 2 2 */
     628,    /* OBJ_setAttr_IssCap_CVM           2 23 42 3 3 3 */
     629,    /* OBJ_setAttr_IssCap_T2            2 23 42 3 3 4 */
     630,    /* OBJ_setAttr_IssCap_Sig           2 23 42 3 3 5 */
     642,    /* OBJ_set_brand_Novus              2 23 42 8 6011 */
     735,    /* OBJ_wap_wsg_idm_ecid_wtls1       2 23 43 1 4 1 */
     736,    /* OBJ_wap_wsg_idm_ecid_wtls3       2 23 43 1 4 3 */
     737,    /* OBJ_wap_wsg_idm_ecid_wtls4       2 23 43 1 4 4 */
     738,    /* OBJ_wap_wsg_idm_ecid_wtls5       2 23 43 1 4 5 */
     739,    /* OBJ_wap_wsg_idm_ecid_wtls6       2 23 43 1 4 6 */
     740,    /* OBJ_wap_wsg_idm_ecid_wtls7       2 23 43 1 4 7 */
     741,    /* OBJ_wap_wsg_idm_ecid_wtls8       2 23 43 1 4 8 */
     742,    /* OBJ_wap_wsg_idm_ecid_wtls9       2 23 43 1 4 9 */
     743,    /* OBJ_wap_wsg_idm_ecid_wtls10      2 23 43 1 4 10 */
     744,    /* OBJ_wap_wsg_idm_ecid_wtls11      2 23 43 1 4 11 */
     745,    /* OBJ_wap_wsg_idm_ecid_wtls12      2 23 43 1 4 12 */
    1142,    /* OBJ_sm_scheme                    1 2 156 10197 1 */
       1,    /* OBJ_rsadsi                       1 2 ********** */
     185,    /* OBJ_X9cm                         1 2 840 10040 4 */
    1031,    /* OBJ_id_pkinit                    1 3 6 1 5 2 3 */
     127,    /* OBJ_id_pkix                      1 3 6 1 5 5 7 */
     505,    /* OBJ_mime_mhs_headings            1 3 6 1 7 1 1 */
     506,    /* OBJ_mime_mhs_bodies              1 3 6 1 7 1 2 */
     937,    /* OBJ_dhSinglePass_stdDH_sha224kdf_scheme 1 3 132 1 11 0 */
     938,    /* OBJ_dhSinglePass_stdDH_sha256kdf_scheme 1 3 132 1 11 1 */
     939,    /* OBJ_dhSinglePass_stdDH_sha384kdf_scheme 1 3 132 1 11 2 */
     940,    /* OBJ_dhSinglePass_stdDH_sha512kdf_scheme 1 3 132 1 11 3 */
     942,    /* OBJ_dhSinglePass_cofactorDH_sha224kdf_scheme 1 3 132 1 14 0 */
     943,    /* OBJ_dhSinglePass_cofactorDH_sha256kdf_scheme 1 3 132 1 14 1 */
     944,    /* OBJ_dhSinglePass_cofactorDH_sha384kdf_scheme 1 3 132 1 14 2 */
     945,    /* OBJ_dhSinglePass_cofactorDH_sha512kdf_scheme 1 3 132 1 14 3 */
     631,    /* OBJ_setAttr_GenCryptgrm          2 23 42 3 3 3 1 */
     632,    /* OBJ_setAttr_T2Enc                2 23 42 3 3 4 1 */
     633,    /* OBJ_setAttr_T2cleartxt           2 23 42 3 3 4 2 */
     634,    /* OBJ_setAttr_TokICCsig            2 23 42 3 3 5 1 */
     635,    /* OBJ_setAttr_SecDevSig            2 23 42 3 3 5 2 */
     436,    /* OBJ_ucl                          0 9 2342 ******** */
    1151,    /* OBJ_ua_pki                       1 2 804 2 1 1 1 */
       2,    /* OBJ_pkcs                         1 2 ********** 1 */
     431,    /* OBJ_hold_instruction_none        1 2 840 10040 2 1 */
     432,    /* OBJ_hold_instruction_call_issuer 1 2 840 10040 2 2 */
     433,    /* OBJ_hold_instruction_reject      1 2 840 10040 2 3 */
     116,    /* OBJ_dsa                          1 2 840 10040 4 1 */
     113,    /* OBJ_dsaWithSHA1                  1 2 840 10040 4 3 */
     406,    /* OBJ_X9_62_prime_field            1 2 840 10045 1 1 */
     407,    /* OBJ_X9_62_characteristic_two_field 1 2 840 10045 1 2 */
     408,    /* OBJ_X9_62_id_ecPublicKey         1 2 840 10045 2 1 */
     416,    /* OBJ_ecdsa_with_SHA1              1 2 840 10045 4 1 */
     791,    /* OBJ_ecdsa_with_Recommended       1 2 840 10045 4 2 */
     792,    /* OBJ_ecdsa_with_Specified         1 2 840 10045 4 3 */
     920,    /* OBJ_dhpublicnumber               1 2 840 10046 2 1 */
    1032,    /* OBJ_pkInitClientAuth             1 3 6 1 5 2 3 4 */
    1033,    /* OBJ_pkInitKDC                    1 3 6 1 5 2 3 5 */
     258,    /* OBJ_id_pkix_mod                  1 3 6 1 5 5 7 0 */
     175,    /* OBJ_id_pe                        1 3 6 1 5 5 7 1 */
     259,    /* OBJ_id_qt                        1 3 6 1 5 5 7 2 */
     128,    /* OBJ_id_kp                        1 3 6 1 5 5 7 3 */
     260,    /* OBJ_id_it                        1 3 6 1 5 5 7 4 */
     261,    /* OBJ_id_pkip                      1 3 6 1 5 5 7 5 */
     262,    /* OBJ_id_alg                       1 3 6 1 5 5 7 6 */
     263,    /* OBJ_id_cmc                       1 3 6 1 5 5 7 7 */
     264,    /* OBJ_id_on                        1 3 6 1 5 5 7 8 */
     265,    /* OBJ_id_pda                       1 3 6 1 5 5 7 9 */
     266,    /* OBJ_id_aca                       1 3 6 1 5 5 7 10 */
     267,    /* OBJ_id_qcs                       1 3 6 1 5 5 7 11 */
     268,    /* OBJ_id_cct                       1 3 6 1 5 5 7 12 */
    1238,    /* OBJ_id_cp                        1 3 6 1 5 5 7 14 */
     662,    /* OBJ_id_ppl                       1 3 6 1 5 5 7 21 */
     176,    /* OBJ_id_ad                        1 3 6 1 5 5 7 48 */
     507,    /* OBJ_id_hex_partial_message       1 3 6 1 7 1 1 1 */
     508,    /* OBJ_id_hex_multipart_message     1 3 6 1 7 1 1 2 */
      57,    /* OBJ_netscape                     2 16 840 1 113730 */
    1258,    /* OBJ_oracle                       2 16 840 1 113894 */
     437,    /* OBJ_pilot                        0 9 2342 ******** 100 */
    1133,    /* OBJ_sm4_ecb                      1 2 156 10197 1 104 1 */
    1134,    /* OBJ_sm4_cbc                      1 2 156 10197 1 104 2 */
    1135,    /* OBJ_sm4_ofb128                   1 2 156 10197 1 104 3 */
    1137,    /* OBJ_sm4_cfb128                   1 2 156 10197 1 104 4 */
    1136,    /* OBJ_sm4_cfb1                     1 2 156 10197 1 104 5 */
    1138,    /* OBJ_sm4_cfb8                     1 2 156 10197 1 104 6 */
    1139,    /* OBJ_sm4_ctr                      1 2 156 10197 1 104 7 */
    1250,    /* OBJ_sm4_gcm                      1 2 156 10197 1 104 8 */
    1251,    /* OBJ_sm4_ccm                      1 2 156 10197 1 104 9 */
    1248,    /* OBJ_zuc                          1 2 156 10197 1 201 */
    1172,    /* OBJ_sm2                          1 2 156 10197 1 301 */
    1143,    /* OBJ_sm3                          1 2 156 10197 1 401 */
    1204,    /* OBJ_SM2_with_SM3                 1 2 156 10197 1 501 */
    1144,    /* OBJ_sm3WithRSAEncryption         1 2 156 10197 1 504 */
    1249,    /* OBJ_zuc_128_eea3                 1 2 156 10197 1 801 */
    1255,    /* OBJ_zuc_128_eia3                 1 2 156 10197 1 802 */
     186,    /* OBJ_pkcs1                        1 2 ********** 1 1 */
      27,    /* OBJ_pkcs3                        1 2 ********** 1 3 */
     187,    /* OBJ_pkcs5                        1 2 ********** 1 5 */
      20,    /* OBJ_pkcs7                        1 2 ********** 1 7 */
      47,    /* OBJ_pkcs9                        1 2 ********** 1 9 */
       4,    /* OBJ_md5                          1 2 ********** 2 5 */
     797,    /* OBJ_hmacWithMD5                  1 2 ********** 2 6 */
     163,    /* OBJ_hmacWithSHA1                 1 2 ********** 2 7 */
     798,    /* OBJ_hmacWithSHA224               1 2 ********** 2 8 */
     799,    /* OBJ_hmacWithSHA256               1 2 ********** 2 9 */
     800,    /* OBJ_hmacWithSHA384               1 2 ********** 2 10 */
     801,    /* OBJ_hmacWithSHA512               1 2 ********** 2 11 */
    1193,    /* OBJ_hmacWithSHA512_224           1 2 ********** 2 12 */
    1194,    /* OBJ_hmacWithSHA512_256           1 2 ********** 2 13 */
       5,    /* OBJ_rc4                          1 2 ********** 3 4 */
      44,    /* OBJ_des_ede3_cbc                 1 2 ********** 3 7 */
     120,    /* OBJ_rc5_cbc                      1 2 ********** 3 8 */
     643,    /* OBJ_des_cdmf                     1 2 ********** 3 10 */
     680,    /* OBJ_X9_62_id_characteristic_two_basis 1 2 840 10045 1 2 3 */
     684,    /* OBJ_X9_62_c2pnb163v1             1 2 840 10045 3 0 1 */
     685,    /* OBJ_X9_62_c2pnb163v2             1 2 840 10045 3 0 2 */
     686,    /* OBJ_X9_62_c2pnb163v3             1 2 840 10045 3 0 3 */
     687,    /* OBJ_X9_62_c2pnb176v1             1 2 840 10045 3 0 4 */
     688,    /* OBJ_X9_62_c2tnb191v1             1 2 840 10045 3 0 5 */
     689,    /* OBJ_X9_62_c2tnb191v2             1 2 840 10045 3 0 6 */
     690,    /* OBJ_X9_62_c2tnb191v3             1 2 840 10045 3 0 7 */
     691,    /* OBJ_X9_62_c2onb191v4             1 2 840 10045 3 0 8 */
     692,    /* OBJ_X9_62_c2onb191v5             1 2 840 10045 3 0 9 */
     693,    /* OBJ_X9_62_c2pnb208w1             1 2 840 10045 3 0 10 */
     694,    /* OBJ_X9_62_c2tnb239v1             1 2 840 10045 3 0 11 */
     695,    /* OBJ_X9_62_c2tnb239v2             1 2 840 10045 3 0 12 */
     696,    /* OBJ_X9_62_c2tnb239v3             1 2 840 10045 3 0 13 */
     697,    /* OBJ_X9_62_c2onb239v4             1 2 840 10045 3 0 14 */
     698,    /* OBJ_X9_62_c2onb239v5             1 2 840 10045 3 0 15 */
     699,    /* OBJ_X9_62_c2pnb272w1             1 2 840 10045 3 0 16 */
     700,    /* OBJ_X9_62_c2pnb304w1             1 2 840 10045 3 0 17 */
     701,    /* OBJ_X9_62_c2tnb359v1             1 2 840 10045 3 0 18 */
     702,    /* OBJ_X9_62_c2pnb368w1             1 2 840 10045 3 0 19 */
     703,    /* OBJ_X9_62_c2tnb431r1             1 2 840 10045 3 0 20 */
     409,    /* OBJ_X9_62_prime192v1             1 2 840 10045 3 1 1 */
     410,    /* OBJ_X9_62_prime192v2             1 2 840 10045 3 1 2 */
     411,    /* OBJ_X9_62_prime192v3             1 2 840 10045 3 1 3 */
     412,    /* OBJ_X9_62_prime239v1             1 2 840 10045 3 1 4 */
     413,    /* OBJ_X9_62_prime239v2             1 2 840 10045 3 1 5 */
     414,    /* OBJ_X9_62_prime239v3             1 2 840 10045 3 1 6 */
     415,    /* OBJ_X9_62_prime256v1             1 2 840 10045 3 1 7 */
     793,    /* OBJ_ecdsa_with_SHA224            1 2 840 10045 4 3 1 */
     794,    /* OBJ_ecdsa_with_SHA256            1 2 840 10045 4 3 2 */
     795,    /* OBJ_ecdsa_with_SHA384            1 2 840 10045 4 3 3 */
     796,    /* OBJ_ecdsa_with_SHA512            1 2 840 10045 4 3 4 */
     269,    /* OBJ_id_pkix1_explicit_88         1 3 6 1 5 5 7 0 1 */
     270,    /* OBJ_id_pkix1_implicit_88         1 3 6 1 5 5 7 0 2 */
     271,    /* OBJ_id_pkix1_explicit_93         1 3 6 1 5 5 7 0 3 */
     272,    /* OBJ_id_pkix1_implicit_93         1 3 6 1 5 5 7 0 4 */
     273,    /* OBJ_id_mod_crmf                  1 3 6 1 5 5 7 0 5 */
     274,    /* OBJ_id_mod_cmc                   1 3 6 1 5 5 7 0 6 */
     275,    /* OBJ_id_mod_kea_profile_88        1 3 6 1 5 5 7 0 7 */
     276,    /* OBJ_id_mod_kea_profile_93        1 3 6 1 5 5 7 0 8 */
     277,    /* OBJ_id_mod_cmp                   1 3 6 1 5 5 7 0 9 */
     278,    /* OBJ_id_mod_qualified_cert_88     1 3 6 1 5 5 7 0 10 */
     279,    /* OBJ_id_mod_qualified_cert_93     1 3 6 1 5 5 7 0 11 */
     280,    /* OBJ_id_mod_attribute_cert        1 3 6 1 5 5 7 0 12 */
     281,    /* OBJ_id_mod_timestamp_protocol    1 3 6 1 5 5 7 0 13 */
     282,    /* OBJ_id_mod_ocsp                  1 3 6 1 5 5 7 0 14 */
     283,    /* OBJ_id_mod_dvcs                  1 3 6 1 5 5 7 0 15 */
     284,    /* OBJ_id_mod_cmp2000               1 3 6 1 5 5 7 0 16 */
     177,    /* OBJ_info_access                  1 3 6 1 5 5 7 1 1 */
     285,    /* OBJ_biometricInfo                1 3 6 1 5 5 7 1 2 */
     286,    /* OBJ_qcStatements                 1 3 6 1 5 5 7 1 3 */
     287,    /* OBJ_ac_auditEntity               1 3 6 1 5 5 7 1 4 */
     288,    /* OBJ_ac_targeting                 1 3 6 1 5 5 7 1 5 */
     289,    /* OBJ_aaControls                   1 3 6 1 5 5 7 1 6 */
     290,    /* OBJ_sbgp_ipAddrBlock             1 3 6 1 5 5 7 1 7 */
     291,    /* OBJ_sbgp_autonomousSysNum        1 3 6 1 5 5 7 1 8 */
     292,    /* OBJ_sbgp_routerIdentifier        1 3 6 1 5 5 7 1 9 */
     397,    /* OBJ_ac_proxying                  1 3 6 1 5 5 7 1 10 */
     398,    /* OBJ_sinfo_access                 1 3 6 1 5 5 7 1 11 */
     663,    /* OBJ_proxyCertInfo                1 3 6 1 5 5 7 1 14 */
    1020,    /* OBJ_tlsfeature                   1 3 6 1 5 5 7 1 24 */
    1239,    /* OBJ_sbgp_ipAddrBlockv2           1 3 6 1 5 5 7 1 28 */
    1240,    /* OBJ_sbgp_autonomousSysNumv2      1 3 6 1 5 5 7 1 29 */
     164,    /* OBJ_id_qt_cps                    1 3 6 1 5 5 7 2 1 */
     165,    /* OBJ_id_qt_unotice                1 3 6 1 5 5 7 2 2 */
     293,    /* OBJ_textNotice                   1 3 6 1 5 5 7 2 3 */
     129,    /* OBJ_server_auth                  1 3 6 1 5 5 7 3 1 */
     130,    /* OBJ_client_auth                  1 3 6 1 5 5 7 3 2 */
     131,    /* OBJ_code_sign                    1 3 6 1 5 5 7 3 3 */
     132,    /* OBJ_email_protect                1 3 6 1 5 5 7 3 4 */
     294,    /* OBJ_ipsecEndSystem               1 3 6 1 5 5 7 3 5 */
     295,    /* OBJ_ipsecTunnel                  1 3 6 1 5 5 7 3 6 */
     296,    /* OBJ_ipsecUser                    1 3 6 1 5 5 7 3 7 */
     133,    /* OBJ_time_stamp                   1 3 6 1 5 5 7 3 8 */
     180,    /* OBJ_OCSP_sign                    1 3 6 1 5 5 7 3 9 */
     297,    /* OBJ_dvcs                         1 3 6 1 5 5 7 3 10 */
    1022,    /* OBJ_ipsec_IKE                    1 3 6 1 5 5 7 3 17 */
    1023,    /* OBJ_capwapAC                     1 3 6 1 5 5 7 3 18 */
    1024,    /* OBJ_capwapWTP                    1 3 6 1 5 5 7 3 19 */
    1025,    /* OBJ_sshClient                    1 3 6 1 5 5 7 3 21 */
    1026,    /* OBJ_sshServer                    1 3 6 1 5 5 7 3 22 */
    1027,    /* OBJ_sendRouter                   1 3 6 1 5 5 7 3 23 */
    1028,    /* OBJ_sendProxiedRouter            1 3 6 1 5 5 7 3 24 */
    1029,    /* OBJ_sendOwner                    1 3 6 1 5 5 7 3 25 */
    1030,    /* OBJ_sendProxiedOwner             1 3 6 1 5 5 7 3 26 */
    1131,    /* OBJ_cmcCA                        1 3 6 1 5 5 7 3 27 */
    1132,    /* OBJ_cmcRA                        1 3 6 1 5 5 7 3 28 */
    1219,    /* OBJ_cmcArchive                   1 3 6 1 5 5 7 3 29 */
    1220,    /* OBJ_id_kp_bgpsec_router          1 3 6 1 5 5 7 3 30 */
    1221,    /* OBJ_id_kp_BrandIndicatorforMessageIdentification 1 3 6 1 5 5 7 3 31 */
    1222,    /* OBJ_cmKGA                        1 3 6 1 5 5 7 3 32 */
     298,    /* OBJ_id_it_caProtEncCert          1 3 6 1 5 5 7 4 1 */
     299,    /* OBJ_id_it_signKeyPairTypes       1 3 6 1 5 5 7 4 2 */
     300,    /* OBJ_id_it_encKeyPairTypes        1 3 6 1 5 5 7 4 3 */
     301,    /* OBJ_id_it_preferredSymmAlg       1 3 6 1 5 5 7 4 4 */
     302,    /* OBJ_id_it_caKeyUpdateInfo        1 3 6 1 5 5 7 4 5 */
     303,    /* OBJ_id_it_currentCRL             1 3 6 1 5 5 7 4 6 */
     304,    /* OBJ_id_it_unsupportedOIDs        1 3 6 1 5 5 7 4 7 */
     305,    /* OBJ_id_it_subscriptionRequest    1 3 6 1 5 5 7 4 8 */
     306,    /* OBJ_id_it_subscriptionResponse   1 3 6 1 5 5 7 4 9 */
     307,    /* OBJ_id_it_keyPairParamReq        1 3 6 1 5 5 7 4 10 */
     308,    /* OBJ_id_it_keyPairParamRep        1 3 6 1 5 5 7 4 11 */
     309,    /* OBJ_id_it_revPassphrase          1 3 6 1 5 5 7 4 12 */
     310,    /* OBJ_id_it_implicitConfirm        1 3 6 1 5 5 7 4 13 */
     311,    /* OBJ_id_it_confirmWaitTime        1 3 6 1 5 5 7 4 14 */
     312,    /* OBJ_id_it_origPKIMessage         1 3 6 1 5 5 7 4 15 */
     784,    /* OBJ_id_it_suppLangTags           1 3 6 1 5 5 7 4 16 */
    1223,    /* OBJ_id_it_caCerts                1 3 6 1 5 5 7 4 17 */
    1224,    /* OBJ_id_it_rootCaKeyUpdate        1 3 6 1 5 5 7 4 18 */
    1225,    /* OBJ_id_it_certReqTemplate        1 3 6 1 5 5 7 4 19 */
     313,    /* OBJ_id_regCtrl                   1 3 6 1 5 5 7 5 1 */
     314,    /* OBJ_id_regInfo                   1 3 6 1 5 5 7 5 2 */
     323,    /* OBJ_id_alg_des40                 1 3 6 1 5 5 7 6 1 */
     324,    /* OBJ_id_alg_noSignature           1 3 6 1 5 5 7 6 2 */
     325,    /* OBJ_id_alg_dh_sig_hmac_sha1      1 3 6 1 5 5 7 6 3 */
     326,    /* OBJ_id_alg_dh_pop                1 3 6 1 5 5 7 6 4 */
     327,    /* OBJ_id_cmc_statusInfo            1 3 6 1 5 5 7 7 1 */
     328,    /* OBJ_id_cmc_identification        1 3 6 1 5 5 7 7 2 */
     329,    /* OBJ_id_cmc_identityProof         1 3 6 1 5 5 7 7 3 */
     330,    /* OBJ_id_cmc_dataReturn            1 3 6 1 5 5 7 7 4 */
     331,    /* OBJ_id_cmc_transactionId         1 3 6 1 5 5 7 7 5 */
     332,    /* OBJ_id_cmc_senderNonce           1 3 6 1 5 5 7 7 6 */
     333,    /* OBJ_id_cmc_recipientNonce        1 3 6 1 5 5 7 7 7 */
     334,    /* OBJ_id_cmc_addExtensions         1 3 6 1 5 5 7 7 8 */
     335,    /* OBJ_id_cmc_encryptedPOP          1 3 6 1 5 5 7 7 9 */
     336,    /* OBJ_id_cmc_decryptedPOP          1 3 6 1 5 5 7 7 10 */
     337,    /* OBJ_id_cmc_lraPOPWitness         1 3 6 1 5 5 7 7 11 */
     338,    /* OBJ_id_cmc_getCert               1 3 6 1 5 5 7 7 15 */
     339,    /* OBJ_id_cmc_getCRL                1 3 6 1 5 5 7 7 16 */
     340,    /* OBJ_id_cmc_revokeRequest         1 3 6 1 5 5 7 7 17 */
     341,    /* OBJ_id_cmc_regInfo               1 3 6 1 5 5 7 7 18 */
     342,    /* OBJ_id_cmc_responseInfo          1 3 6 1 5 5 7 7 19 */
     343,    /* OBJ_id_cmc_queryPending          1 3 6 1 5 5 7 7 21 */
     344,    /* OBJ_id_cmc_popLinkRandom         1 3 6 1 5 5 7 7 22 */
     345,    /* OBJ_id_cmc_popLinkWitness        1 3 6 1 5 5 7 7 23 */
     346,    /* OBJ_id_cmc_confirmCertAcceptance 1 3 6 1 5 5 7 7 24 */
     347,    /* OBJ_id_on_personalData           1 3 6 1 5 5 7 8 1 */
     858,    /* OBJ_id_on_permanentIdentifier    1 3 6 1 5 5 7 8 3 */
    1209,    /* OBJ_XmppAddr                     1 3 6 1 5 5 7 8 5 */
    1210,    /* OBJ_SRVName                      1 3 6 1 5 5 7 8 7 */
    1211,    /* OBJ_NAIRealm                     1 3 6 1 5 5 7 8 8 */
    1208,    /* OBJ_id_on_SmtpUTF8Mailbox        1 3 6 1 5 5 7 8 9 */
     348,    /* OBJ_id_pda_dateOfBirth           1 3 6 1 5 5 7 9 1 */
     349,    /* OBJ_id_pda_placeOfBirth          1 3 6 1 5 5 7 9 2 */
     351,    /* OBJ_id_pda_gender                1 3 6 1 5 5 7 9 3 */
     352,    /* OBJ_id_pda_countryOfCitizenship  1 3 6 1 5 5 7 9 4 */
     353,    /* OBJ_id_pda_countryOfResidence    1 3 6 1 5 5 7 9 5 */
     354,    /* OBJ_id_aca_authenticationInfo    1 3 6 1 5 5 7 10 1 */
     355,    /* OBJ_id_aca_accessIdentity        1 3 6 1 5 5 7 10 2 */
     356,    /* OBJ_id_aca_chargingIdentity      1 3 6 1 5 5 7 10 3 */
     357,    /* OBJ_id_aca_group                 1 3 6 1 5 5 7 10 4 */
     358,    /* OBJ_id_aca_role                  1 3 6 1 5 5 7 10 5 */
     399,    /* OBJ_id_aca_encAttrs              1 3 6 1 5 5 7 10 6 */
     359,    /* OBJ_id_qcs_pkixQCSyntax_v1       1 3 6 1 5 5 7 11 1 */
     360,    /* OBJ_id_cct_crs                   1 3 6 1 5 5 7 12 1 */
     361,    /* OBJ_id_cct_PKIData               1 3 6 1 5 5 7 12 2 */
     362,    /* OBJ_id_cct_PKIResponse           1 3 6 1 5 5 7 12 3 */
    1241,    /* OBJ_ipAddr_asNumber              1 3 6 1 5 5 7 14 2 */
    1242,    /* OBJ_ipAddr_asNumberv2            1 3 6 1 5 5 7 14 3 */
     664,    /* OBJ_id_ppl_anyLanguage           1 3 6 1 5 5 7 21 0 */
     665,    /* OBJ_id_ppl_inheritAll            1 3 6 1 5 5 7 21 1 */
     667,    /* OBJ_Independent                  1 3 6 1 5 5 7 21 2 */
     178,    /* OBJ_ad_OCSP                      1 3 6 1 5 5 7 48 1 */
     179,    /* OBJ_ad_ca_issuers                1 3 6 1 5 5 7 48 2 */
     363,    /* OBJ_ad_timeStamping              1 3 6 1 5 5 7 48 3 */
     364,    /* OBJ_ad_dvcs                      1 3 6 1 5 5 7 48 4 */
     785,    /* OBJ_caRepository                 1 3 6 1 5 5 7 48 5 */
    1243,    /* OBJ_rpkiManifest                 1 3 6 1 5 5 7 48 10 */
    1244,    /* OBJ_signedObject                 1 3 6 1 5 5 7 48 11 */
    1245,    /* OBJ_rpkiNotify                   1 3 6 1 5 5 7 48 13 */
     780,    /* OBJ_hmac_md5                     1 3 6 1 5 5 8 1 1 */
     781,    /* OBJ_hmac_sha1                    1 3 6 1 5 5 8 1 2 */
     913,    /* OBJ_aes_128_xts                  1 3 111 2 1619 0 1 1 */
     914,    /* OBJ_aes_256_xts                  1 3 111 2 1619 0 1 2 */
      58,    /* OBJ_netscape_cert_extension      2 16 840 1 113730 1 */
      59,    /* OBJ_netscape_data_type           2 16 840 1 113730 2 */
     438,    /* OBJ_pilotAttributeType           0 9 2342 ******** 100 1 */
     439,    /* OBJ_pilotAttributeSyntax         0 9 2342 ******** 100 3 */
     440,    /* OBJ_pilotObjectClass             0 9 2342 ******** 100 4 */
     441,    /* OBJ_pilotGroups                  0 9 2342 ******** 100 10 */
     782,    /* OBJ_id_PasswordBasedMAC          1 2 ********** 7 66 13 */
     783,    /* OBJ_id_DHBasedMac                1 2 ********** 7 66 30 */
       6,    /* OBJ_rsaEncryption                1 2 ********** 1 1 1 */
       8,    /* OBJ_md5WithRSAEncryption         1 2 ********** 1 1 4 */
      65,    /* OBJ_sha1WithRSAEncryption        1 2 ********** 1 1 5 */
     644,    /* OBJ_rsaOAEPEncryptionSET         1 2 ********** 1 1 6 */
     919,    /* OBJ_rsaesOaep                    1 2 ********** 1 1 7 */
     911,    /* OBJ_mgf1                         1 2 ********** 1 1 8 */
     935,    /* OBJ_pSpecified                   1 2 ********** 1 1 9 */
     912,    /* OBJ_rsassaPss                    1 2 ********** 1 1 10 */
     668,    /* OBJ_sha256WithRSAEncryption      1 2 ********** 1 1 11 */
     669,    /* OBJ_sha384WithRSAEncryption      1 2 ********** 1 1 12 */
     670,    /* OBJ_sha512WithRSAEncryption      1 2 ********** 1 1 13 */
     671,    /* OBJ_sha224WithRSAEncryption      1 2 ********** 1 1 14 */
    1145,    /* OBJ_sha512_224WithRSAEncryption  1 2 ********** 1 1 15 */
    1146,    /* OBJ_sha512_256WithRSAEncryption  1 2 ********** 1 1 16 */
      28,    /* OBJ_dhKeyAgreement               1 2 ********** 1 3 1 */
      10,    /* OBJ_pbeWithMD5AndDES_CBC         1 2 ********** 1 5 3 */
     170,    /* OBJ_pbeWithSHA1AndDES_CBC        1 2 ********** 1 5 10 */
      69,    /* OBJ_id_pbkdf2                    1 2 ********** 1 5 12 */
     161,    /* OBJ_pbes2                        1 2 ********** 1 5 13 */
     162,    /* OBJ_pbmac1                       1 2 ********** 1 5 14 */
      21,    /* OBJ_pkcs7_data                   1 2 ********** 1 7 1 */
      22,    /* OBJ_pkcs7_signed                 1 2 ********** 1 7 2 */
      23,    /* OBJ_pkcs7_enveloped              1 2 ********** 1 7 3 */
      24,    /* OBJ_pkcs7_signedAndEnveloped     1 2 ********** 1 7 4 */
      25,    /* OBJ_pkcs7_digest                 1 2 ********** 1 7 5 */
      26,    /* OBJ_pkcs7_encrypted              1 2 ********** 1 7 6 */
      48,    /* OBJ_pkcs9_emailAddress           1 2 ********** 1 9 1 */
      49,    /* OBJ_pkcs9_unstructuredName       1 2 ********** 1 9 2 */
      50,    /* OBJ_pkcs9_contentType            1 2 ********** 1 9 3 */
      51,    /* OBJ_pkcs9_messageDigest          1 2 ********** 1 9 4 */
      52,    /* OBJ_pkcs9_signingTime            1 2 ********** 1 9 5 */
      53,    /* OBJ_pkcs9_countersignature       1 2 ********** 1 9 6 */
      54,    /* OBJ_pkcs9_challengePassword      1 2 ********** 1 9 7 */
      55,    /* OBJ_pkcs9_unstructuredAddress    1 2 ********** 1 9 8 */
      56,    /* OBJ_pkcs9_extCertAttributes      1 2 ********** 1 9 9 */
     172,    /* OBJ_ext_req                      1 2 ********** 1 9 14 */
     167,    /* OBJ_SMIMECapabilities            1 2 ********** 1 9 15 */
     188,    /* OBJ_SMIME                        1 2 ********** 1 9 16 */
     156,    /* OBJ_friendlyName                 1 2 ********** 1 9 20 */
     157,    /* OBJ_localKeyID                   1 2 ********** 1 9 21 */
     681,    /* OBJ_X9_62_onBasis                1 2 840 10045 1 2 3 1 */
     682,    /* OBJ_X9_62_tpBasis                1 2 840 10045 1 2 3 2 */
     683,    /* OBJ_X9_62_ppBasis                1 2 840 10045 1 2 3 3 */
     417,    /* OBJ_ms_csp_name                  1 3 6 1 4 1 311 17 1 */
     856,    /* OBJ_LocalKeySet                  1 3 6 1 4 1 311 17 2 */
    1256,    /* OBJ_delegation_usage             1 3 6 1 4 1 44363 44 */
     390,    /* OBJ_dcObject                     1 3 6 1 4 1 1466 344 */
     973,    /* OBJ_id_scrypt                    1 3 6 1 4 1 11591 4 11 */
     315,    /* OBJ_id_regCtrl_regToken          1 3 6 1 5 5 7 5 1 1 */
     316,    /* OBJ_id_regCtrl_authenticator     1 3 6 1 5 5 7 5 1 2 */
     317,    /* OBJ_id_regCtrl_pkiPublicationInfo 1 3 6 1 5 5 7 5 1 3 */
     318,    /* OBJ_id_regCtrl_pkiArchiveOptions 1 3 6 1 5 5 7 5 1 4 */
     319,    /* OBJ_id_regCtrl_oldCertID         1 3 6 1 5 5 7 5 1 5 */
     320,    /* OBJ_id_regCtrl_protocolEncrKey   1 3 6 1 5 5 7 5 1 6 */
     321,    /* OBJ_id_regInfo_utf8Pairs         1 3 6 1 5 5 7 5 2 1 */
     322,    /* OBJ_id_regInfo_certReq           1 3 6 1 5 5 7 5 2 2 */
     365,    /* OBJ_id_pkix_OCSP_basic           1 3 6 1 5 5 7 48 1 1 */
     366,    /* OBJ_id_pkix_OCSP_Nonce           1 3 6 1 5 5 7 48 1 2 */
     367,    /* OBJ_id_pkix_OCSP_CrlID           1 3 6 1 5 5 7 48 1 3 */
     368,    /* OBJ_id_pkix_OCSP_acceptableResponses 1 3 6 1 5 5 7 48 1 4 */
     369,    /* OBJ_id_pkix_OCSP_noCheck         1 3 6 1 5 5 7 48 1 5 */
     370,    /* OBJ_id_pkix_OCSP_archiveCutoff   1 3 6 1 5 5 7 48 1 6 */
     371,    /* OBJ_id_pkix_OCSP_serviceLocator  1 3 6 1 5 5 7 48 1 7 */
     372,    /* OBJ_id_pkix_OCSP_extendedStatus  1 3 6 1 5 5 7 48 1 8 */
     373,    /* OBJ_id_pkix_OCSP_valid           1 3 6 1 5 5 7 48 1 9 */
     374,    /* OBJ_id_pkix_OCSP_path            1 3 6 1 5 5 7 48 1 10 */
     375,    /* OBJ_id_pkix_OCSP_trustRoot       1 3 6 1 5 5 7 48 1 11 */
     921,    /* OBJ_brainpoolP160r1              1 3 36 3 3 2 8 1 1 1 */
     922,    /* OBJ_brainpoolP160t1              1 3 36 3 3 2 8 1 1 2 */
     923,    /* OBJ_brainpoolP192r1              1 3 36 3 3 2 8 1 1 3 */
     924,    /* OBJ_brainpoolP192t1              1 3 36 3 3 2 8 1 1 4 */
     925,    /* OBJ_brainpoolP224r1              1 3 36 3 3 2 8 1 1 5 */
     926,    /* OBJ_brainpoolP224t1              1 3 36 3 3 2 8 1 1 6 */
     927,    /* OBJ_brainpoolP256r1              1 3 36 3 3 2 8 1 1 7 */
     928,    /* OBJ_brainpoolP256t1              1 3 36 3 3 2 8 1 1 8 */
     929,    /* OBJ_brainpoolP320r1              1 3 36 3 3 2 8 1 1 9 */
     930,    /* OBJ_brainpoolP320t1              1 3 36 3 3 2 8 1 1 10 */
     931,    /* OBJ_brainpoolP384r1              1 3 36 3 3 2 8 1 1 11 */
     932,    /* OBJ_brainpoolP384t1              1 3 36 3 3 2 8 1 1 12 */
     933,    /* OBJ_brainpoolP512r1              1 3 36 3 3 2 8 1 1 13 */
     934,    /* OBJ_brainpoolP512t1              1 3 36 3 3 2 8 1 1 14 */
     936,    /* OBJ_dhSinglePass_stdDH_sha1kdf_scheme 1 3 133 16 840 63 0 2 */
     941,    /* OBJ_dhSinglePass_cofactorDH_sha1kdf_scheme 1 3 133 16 840 63 0 3 */
     418,    /* OBJ_aes_128_ecb                  2 16 840 1 101 3 4 1 1 */
     419,    /* OBJ_aes_128_cbc                  2 16 840 1 101 3 4 1 2 */
     420,    /* OBJ_aes_128_ofb128               2 16 840 1 101 3 4 1 3 */
     421,    /* OBJ_aes_128_cfb128               2 16 840 1 101 3 4 1 4 */
     788,    /* OBJ_id_aes128_wrap               2 16 840 1 101 3 4 1 5 */
     895,    /* OBJ_aes_128_gcm                  2 16 840 1 101 3 4 1 6 */
     896,    /* OBJ_aes_128_ccm                  2 16 840 1 101 3 4 1 7 */
     897,    /* OBJ_id_aes128_wrap_pad           2 16 840 1 101 3 4 1 8 */
     422,    /* OBJ_aes_192_ecb                  2 16 840 1 101 3 4 1 21 */
     423,    /* OBJ_aes_192_cbc                  2 16 840 1 101 3 4 1 22 */
     424,    /* OBJ_aes_192_ofb128               2 16 840 1 101 3 4 1 23 */
     425,    /* OBJ_aes_192_cfb128               2 16 840 1 101 3 4 1 24 */
     789,    /* OBJ_id_aes192_wrap               2 16 840 1 101 3 4 1 25 */
     898,    /* OBJ_aes_192_gcm                  2 16 840 1 101 3 4 1 26 */
     899,    /* OBJ_aes_192_ccm                  2 16 840 1 101 3 4 1 27 */
     900,    /* OBJ_id_aes192_wrap_pad           2 16 840 1 101 3 4 1 28 */
     426,    /* OBJ_aes_256_ecb                  2 16 840 1 101 3 4 1 41 */
     427,    /* OBJ_aes_256_cbc                  2 16 840 1 101 3 4 1 42 */
     428,    /* OBJ_aes_256_ofb128               2 16 840 1 101 3 4 1 43 */
     429,    /* OBJ_aes_256_cfb128               2 16 840 1 101 3 4 1 44 */
     790,    /* OBJ_id_aes256_wrap               2 16 840 1 101 3 4 1 45 */
     901,    /* OBJ_aes_256_gcm                  2 16 840 1 101 3 4 1 46 */
     902,    /* OBJ_aes_256_ccm                  2 16 840 1 101 3 4 1 47 */
     903,    /* OBJ_id_aes256_wrap_pad           2 16 840 1 101 3 4 1 48 */
     672,    /* OBJ_sha256                       2 16 840 1 101 3 4 2 1 */
     673,    /* OBJ_sha384                       2 16 840 1 101 3 4 2 2 */
     674,    /* OBJ_sha512                       2 16 840 1 101 3 4 2 3 */
     675,    /* OBJ_sha224                       2 16 840 1 101 3 4 2 4 */
    1094,    /* OBJ_sha512_224                   2 16 840 1 101 3 4 2 5 */
    1095,    /* OBJ_sha512_256                   2 16 840 1 101 3 4 2 6 */
    1096,    /* OBJ_sha3_224                     2 16 840 1 101 3 4 2 7 */
    1097,    /* OBJ_sha3_256                     2 16 840 1 101 3 4 2 8 */
    1098,    /* OBJ_sha3_384                     2 16 840 1 101 3 4 2 9 */
    1099,    /* OBJ_sha3_512                     2 16 840 1 101 3 4 2 10 */
    1100,    /* OBJ_shake128                     2 16 840 1 101 3 4 2 11 */
    1101,    /* OBJ_shake256                     2 16 840 1 101 3 4 2 12 */
    1102,    /* OBJ_hmac_sha3_224                2 16 840 1 101 3 4 2 13 */
    1103,    /* OBJ_hmac_sha3_256                2 16 840 1 101 3 4 2 14 */
    1104,    /* OBJ_hmac_sha3_384                2 16 840 1 101 3 4 2 15 */
    1105,    /* OBJ_hmac_sha3_512                2 16 840 1 101 3 4 2 16 */
    1196,    /* OBJ_kmac128                      2 16 840 1 101 3 4 2 19 */
    1197,    /* OBJ_kmac256                      2 16 840 1 101 3 4 2 20 */
     802,    /* OBJ_dsa_with_SHA224              2 16 840 1 101 3 4 3 1 */
     803,    /* OBJ_dsa_with_SHA256              2 16 840 1 101 3 4 3 2 */
    1106,    /* OBJ_dsa_with_SHA384              2 16 840 1 101 3 4 3 3 */
    1107,    /* OBJ_dsa_with_SHA512              2 16 840 1 101 3 4 3 4 */
    1108,    /* OBJ_dsa_with_SHA3_224            2 16 840 1 101 3 4 3 5 */
    1109,    /* OBJ_dsa_with_SHA3_256            2 16 840 1 101 3 4 3 6 */
    1110,    /* OBJ_dsa_with_SHA3_384            2 16 840 1 101 3 4 3 7 */
    1111,    /* OBJ_dsa_with_SHA3_512            2 16 840 1 101 3 4 3 8 */
    1112,    /* OBJ_ecdsa_with_SHA3_224          2 16 840 1 101 3 4 3 9 */
    1113,    /* OBJ_ecdsa_with_SHA3_256          2 16 840 1 101 3 4 3 10 */
    1114,    /* OBJ_ecdsa_with_SHA3_384          2 16 840 1 101 3 4 3 11 */
    1115,    /* OBJ_ecdsa_with_SHA3_512          2 16 840 1 101 3 4 3 12 */
    1116,    /* OBJ_RSA_SHA3_224                 2 16 840 1 101 3 4 3 13 */
    1117,    /* OBJ_RSA_SHA3_256                 2 16 840 1 101 3 4 3 14 */
    1118,    /* OBJ_RSA_SHA3_384                 2 16 840 1 101 3 4 3 15 */
    1119,    /* OBJ_RSA_SHA3_512                 2 16 840 1 101 3 4 3 16 */
      71,    /* OBJ_netscape_cert_type           2 16 840 1 113730 1 1 */
      72,    /* OBJ_netscape_base_url            2 16 840 1 113730 1 2 */
      73,    /* OBJ_netscape_revocation_url      2 16 840 1 113730 1 3 */
      74,    /* OBJ_netscape_ca_revocation_url   2 16 840 1 113730 1 4 */
      75,    /* OBJ_netscape_renewal_url         2 16 840 1 113730 1 7 */
      76,    /* OBJ_netscape_ca_policy_url       2 16 840 1 113730 1 8 */
      77,    /* OBJ_netscape_ssl_server_name     2 16 840 1 113730 1 12 */
      78,    /* OBJ_netscape_comment             2 16 840 1 113730 1 13 */
      79,    /* OBJ_netscape_cert_sequence       2 16 840 1 113730 2 5 */
     139,    /* OBJ_ns_sgc                       2 16 840 1 113730 4 1 */
     458,    /* OBJ_userId                       0 9 2342 ******** 100 1 1 */
     459,    /* OBJ_textEncodedORAddress         0 9 2342 ******** 100 1 2 */
     460,    /* OBJ_rfc822Mailbox                0 9 2342 ******** 100 1 3 */
     461,    /* OBJ_info                         0 9 2342 ******** 100 1 4 */
     462,    /* OBJ_favouriteDrink               0 9 2342 ******** 100 1 5 */
     463,    /* OBJ_roomNumber                   0 9 2342 ******** 100 1 6 */
     464,    /* OBJ_photo                        0 9 2342 ******** 100 1 7 */
     465,    /* OBJ_userClass                    0 9 2342 ******** 100 1 8 */
     466,    /* OBJ_host                         0 9 2342 ******** 100 1 9 */
     467,    /* OBJ_manager                      0 9 2342 ******** 100 1 10 */
     468,    /* OBJ_documentIdentifier           0 9 2342 ******** 100 1 11 */
     469,    /* OBJ_documentTitle                0 9 2342 ******** 100 1 12 */
     470,    /* OBJ_documentVersion              0 9 2342 ******** 100 1 13 */
     471,    /* OBJ_documentAuthor               0 9 2342 ******** 100 1 14 */
     472,    /* OBJ_documentLocation             0 9 2342 ******** 100 1 15 */
     473,    /* OBJ_homeTelephoneNumber          0 9 2342 ******** 100 1 20 */
     474,    /* OBJ_secretary                    0 9 2342 ******** 100 1 21 */
     475,    /* OBJ_otherMailbox                 0 9 2342 ******** 100 1 22 */
     476,    /* OBJ_lastModifiedTime             0 9 2342 ******** 100 1 23 */
     477,    /* OBJ_lastModifiedBy               0 9 2342 ******** 100 1 24 */
     391,    /* OBJ_domainComponent              0 9 2342 ******** 100 1 25 */
     478,    /* OBJ_aRecord                      0 9 2342 ******** 100 1 26 */
     479,    /* OBJ_pilotAttributeType27         0 9 2342 ******** 100 1 27 */
     480,    /* OBJ_mXRecord                     0 9 2342 ******** 100 1 28 */
     481,    /* OBJ_nSRecord                     0 9 2342 ******** 100 1 29 */
     482,    /* OBJ_sOARecord                    0 9 2342 ******** 100 1 30 */
     483,    /* OBJ_cNAMERecord                  0 9 2342 ******** 100 1 31 */
     484,    /* OBJ_associatedDomain             0 9 2342 ******** 100 1 37 */
     485,    /* OBJ_associatedName               0 9 2342 ******** 100 1 38 */
     486,    /* OBJ_homePostalAddress            0 9 2342 ******** 100 1 39 */
     487,    /* OBJ_personalTitle                0 9 2342 ******** 100 1 40 */
     488,    /* OBJ_mobileTelephoneNumber        0 9 2342 ******** 100 1 41 */
     489,    /* OBJ_pagerTelephoneNumber         0 9 2342 ******** 100 1 42 */
     490,    /* OBJ_friendlyCountryName          0 9 2342 ******** 100 1 43 */
     102,    /* OBJ_uniqueIdentifier             0 9 2342 ******** 100 1 44 */
     491,    /* OBJ_organizationalStatus         0 9 2342 ******** 100 1 45 */
     492,    /* OBJ_janetMailbox                 0 9 2342 ******** 100 1 46 */
     493,    /* OBJ_mailPreferenceOption         0 9 2342 ******** 100 1 47 */
     494,    /* OBJ_buildingName                 0 9 2342 ******** 100 1 48 */
     495,    /* OBJ_dSAQuality                   0 9 2342 ******** 100 1 49 */
     496,    /* OBJ_singleLevelQuality           0 9 2342 ******** 100 1 50 */
     497,    /* OBJ_subtreeMinimumQuality        0 9 2342 ******** 100 1 51 */
     498,    /* OBJ_subtreeMaximumQuality        0 9 2342 ******** 100 1 52 */
     499,    /* OBJ_personalSignature            0 9 2342 ******** 100 1 53 */
     500,    /* OBJ_dITRedirect                  0 9 2342 ******** 100 1 54 */
     501,    /* OBJ_audio                        0 9 2342 ******** 100 1 55 */
     502,    /* OBJ_documentPublisher            0 9 2342 ******** 100 1 56 */
     442,    /* OBJ_iA5StringSyntax              0 9 2342 ******** 100 3 4 */
     443,    /* OBJ_caseIgnoreIA5StringSyntax    0 9 2342 ******** 100 3 5 */
     444,    /* OBJ_pilotObject                  0 9 2342 ******** 100 4 3 */
     445,    /* OBJ_pilotPerson                  0 9 2342 ******** 100 4 4 */
     446,    /* OBJ_account                      0 9 2342 ******** 100 4 5 */
     447,    /* OBJ_document                     0 9 2342 ******** 100 4 6 */
     448,    /* OBJ_room                         0 9 2342 ******** 100 4 7 */
     449,    /* OBJ_documentSeries               0 9 2342 ******** 100 4 9 */
     392,    /* OBJ_Domain                       0 9 2342 ******** 100 4 13 */
     450,    /* OBJ_rFC822localPart              0 9 2342 ******** 100 4 14 */
     451,    /* OBJ_dNSDomain                    0 9 2342 ******** 100 4 15 */
     452,    /* OBJ_domainRelatedObject          0 9 2342 ******** 100 4 17 */
     453,    /* OBJ_friendlyCountry              0 9 2342 ******** 100 4 18 */
     454,    /* OBJ_simpleSecurityObject         0 9 2342 ******** 100 4 19 */
     455,    /* OBJ_pilotOrganization            0 9 2342 ******** 100 4 20 */
     456,    /* OBJ_pilotDSA                     0 9 2342 ******** 100 4 21 */
     457,    /* OBJ_qualityLabelledData          0 9 2342 ******** 100 4 22 */
    1257,    /* OBJ_hmacWithSM3                  1 2 156 10197 1 401 3 1 */
     189,    /* OBJ_id_smime_mod                 1 2 ********** 1 9 16 0 */
     190,    /* OBJ_id_smime_ct                  1 2 ********** 1 9 16 1 */
     191,    /* OBJ_id_smime_aa                  1 2 ********** 1 9 16 2 */
     192,    /* OBJ_id_smime_alg                 1 2 ********** 1 9 16 3 */
     193,    /* OBJ_id_smime_cd                  1 2 ********** 1 9 16 4 */
     194,    /* OBJ_id_smime_spq                 1 2 ********** 1 9 16 5 */
     195,    /* OBJ_id_smime_cti                 1 2 ********** 1 9 16 6 */
     158,    /* OBJ_x509Certificate              1 2 ********** 1 9 22 1 */
     159,    /* OBJ_sdsiCertificate              1 2 ********** 1 9 22 2 */
     160,    /* OBJ_x509Crl                      1 2 ********** 1 9 23 1 */
     144,    /* OBJ_pbe_WithSHA1And128BitRC4     1 2 ********** 1 12 1 1 */
     145,    /* OBJ_pbe_WithSHA1And40BitRC4      1 2 ********** 1 12 1 2 */
     146,    /* OBJ_pbe_WithSHA1And3_Key_TripleDES_CBC 1 2 ********** 1 12 1 3 */
     147,    /* OBJ_pbe_WithSHA1And2_Key_TripleDES_CBC 1 2 ********** 1 12 1 4 */
     171,    /* OBJ_ms_ext_req                   1 3 6 1 4 1 311 2 1 14 */
     134,    /* OBJ_ms_code_ind                  1 3 6 1 4 1 311 2 1 21 */
     135,    /* OBJ_ms_code_com                  1 3 6 1 4 1 311 2 1 22 */
     136,    /* OBJ_ms_ctl_sign                  1 3 6 1 4 1 311 10 3 1 */
     137,    /* OBJ_ms_sgc                       1 3 6 1 4 1 311 10 3 3 */
     138,    /* OBJ_ms_efs                       1 3 6 1 4 1 311 10 3 4 */
     648,    /* OBJ_ms_smartcard_login           1 3 6 1 4 1 311 20 2 2 */
     649,    /* OBJ_ms_upn                       1 3 6 1 4 1 311 20 2 3 */
     951,    /* OBJ_ct_precert_scts              1 3 6 1 4 1 11129 2 4 2 */
     952,    /* OBJ_ct_precert_poison            1 3 6 1 4 1 11129 2 4 3 */
     953,    /* OBJ_ct_precert_signer            1 3 6 1 4 1 11129 2 4 4 */
     954,    /* OBJ_ct_cert_scts                 1 3 6 1 4 1 11129 2 4 5 */
    1158,    /* OBJ_dstu4145le                   1 2 804 2 1 1 1 1 3 1 1 */
     196,    /* OBJ_id_smime_mod_cms             1 2 ********** 1 9 16 0 1 */
     197,    /* OBJ_id_smime_mod_ess             1 2 ********** 1 9 16 0 2 */
     198,    /* OBJ_id_smime_mod_oid             1 2 ********** 1 9 16 0 3 */
     199,    /* OBJ_id_smime_mod_msg_v3          1 2 ********** 1 9 16 0 4 */
     200,    /* OBJ_id_smime_mod_ets_eSignature_88 1 2 ********** 1 9 16 0 5 */
     201,    /* OBJ_id_smime_mod_ets_eSignature_97 1 2 ********** 1 9 16 0 6 */
     202,    /* OBJ_id_smime_mod_ets_eSigPolicy_88 1 2 ********** 1 9 16 0 7 */
     203,    /* OBJ_id_smime_mod_ets_eSigPolicy_97 1 2 ********** 1 9 16 0 8 */
     204,    /* OBJ_id_smime_ct_receipt          1 2 ********** 1 9 16 1 1 */
     205,    /* OBJ_id_smime_ct_authData         1 2 ********** 1 9 16 1 2 */
     206,    /* OBJ_id_smime_ct_publishCert      1 2 ********** 1 9 16 1 3 */
     207,    /* OBJ_id_smime_ct_TSTInfo          1 2 ********** 1 9 16 1 4 */
     208,    /* OBJ_id_smime_ct_TDTInfo          1 2 ********** 1 9 16 1 5 */
     209,    /* OBJ_id_smime_ct_contentInfo      1 2 ********** 1 9 16 1 6 */
     210,    /* OBJ_id_smime_ct_DVCSRequestData  1 2 ********** 1 9 16 1 7 */
     211,    /* OBJ_id_smime_ct_DVCSResponseData 1 2 ********** 1 9 16 1 8 */
     786,    /* OBJ_id_smime_ct_compressedData   1 2 ********** 1 9 16 1 9 */
    1058,    /* OBJ_id_smime_ct_contentCollection 1 2 ********** 1 9 16 1 19 */
    1059,    /* OBJ_id_smime_ct_authEnvelopedData 1 2 ********** 1 9 16 1 23 */
    1234,    /* OBJ_id_ct_routeOriginAuthz       1 2 ********** 1 9 16 1 24 */
    1235,    /* OBJ_id_ct_rpkiManifest           1 2 ********** 1 9 16 1 26 */
     787,    /* OBJ_id_ct_asciiTextWithCRLF      1 2 ********** 1 9 16 1 27 */
    1060,    /* OBJ_id_ct_xml                    1 2 ********** 1 9 16 1 28 */
    1236,    /* OBJ_id_ct_rpkiGhostbusters       1 2 ********** 1 9 16 1 35 */
    1237,    /* OBJ_id_ct_resourceTaggedAttest   1 2 ********** 1 9 16 1 36 */
    1246,    /* OBJ_id_ct_geofeedCSVwithCRLF     1 2 ********** 1 9 16 1 47 */
    1247,    /* OBJ_id_ct_signedChecklist        1 2 ********** 1 9 16 1 48 */
     212,    /* OBJ_id_smime_aa_receiptRequest   1 2 ********** 1 9 16 2 1 */
     213,    /* OBJ_id_smime_aa_securityLabel    1 2 ********** 1 9 16 2 2 */
     214,    /* OBJ_id_smime_aa_mlExpandHistory  1 2 ********** 1 9 16 2 3 */
     215,    /* OBJ_id_smime_aa_contentHint      1 2 ********** 1 9 16 2 4 */
     216,    /* OBJ_id_smime_aa_msgSigDigest     1 2 ********** 1 9 16 2 5 */
     217,    /* OBJ_id_smime_aa_encapContentType 1 2 ********** 1 9 16 2 6 */
     218,    /* OBJ_id_smime_aa_contentIdentifier 1 2 ********** 1 9 16 2 7 */
     219,    /* OBJ_id_smime_aa_macValue         1 2 ********** 1 9 16 2 8 */
     220,    /* OBJ_id_smime_aa_equivalentLabels 1 2 ********** 1 9 16 2 9 */
     221,    /* OBJ_id_smime_aa_contentReference 1 2 ********** 1 9 16 2 10 */
     222,    /* OBJ_id_smime_aa_encrypKeyPref    1 2 ********** 1 9 16 2 11 */
     223,    /* OBJ_id_smime_aa_signingCertificate 1 2 ********** 1 9 16 2 12 */
     224,    /* OBJ_id_smime_aa_smimeEncryptCerts 1 2 ********** 1 9 16 2 13 */
     225,    /* OBJ_id_smime_aa_timeStampToken   1 2 ********** 1 9 16 2 14 */
     226,    /* OBJ_id_smime_aa_ets_sigPolicyId  1 2 ********** 1 9 16 2 15 */
     227,    /* OBJ_id_smime_aa_ets_commitmentType 1 2 ********** 1 9 16 2 16 */
     228,    /* OBJ_id_smime_aa_ets_signerLocation 1 2 ********** 1 9 16 2 17 */
     229,    /* OBJ_id_smime_aa_ets_signerAttr   1 2 ********** 1 9 16 2 18 */
     230,    /* OBJ_id_smime_aa_ets_otherSigCert 1 2 ********** 1 9 16 2 19 */
     231,    /* OBJ_id_smime_aa_ets_contentTimestamp 1 2 ********** 1 9 16 2 20 */
     232,    /* OBJ_id_smime_aa_ets_CertificateRefs 1 2 ********** 1 9 16 2 21 */
     233,    /* OBJ_id_smime_aa_ets_RevocationRefs 1 2 ********** 1 9 16 2 22 */
     234,    /* OBJ_id_smime_aa_ets_certValues   1 2 ********** 1 9 16 2 23 */
     235,    /* OBJ_id_smime_aa_ets_revocationValues 1 2 ********** 1 9 16 2 24 */
     236,    /* OBJ_id_smime_aa_ets_escTimeStamp 1 2 ********** 1 9 16 2 25 */
     237,    /* OBJ_id_smime_aa_ets_certCRLTimestamp 1 2 ********** 1 9 16 2 26 */
     238,    /* OBJ_id_smime_aa_ets_archiveTimeStamp 1 2 ********** 1 9 16 2 27 */
     239,    /* OBJ_id_smime_aa_signatureType    1 2 ********** 1 9 16 2 28 */
     240,    /* OBJ_id_smime_aa_dvcs_dvc         1 2 ********** 1 9 16 2 29 */
    1086,    /* OBJ_id_smime_aa_signingCertificateV2 1 2 ********** 1 9 16 2 47 */
     241,    /* OBJ_id_smime_alg_ESDHwith3DES    1 2 ********** 1 9 16 3 1 */
     243,    /* OBJ_id_smime_alg_3DESwrap        1 2 ********** 1 9 16 3 3 */
     245,    /* OBJ_id_smime_alg_ESDH            1 2 ********** 1 9 16 3 5 */
     246,    /* OBJ_id_smime_alg_CMS3DESwrap     1 2 ********** 1 9 16 3 6 */
     125,    /* OBJ_zlib_compression             1 2 ********** 1 9 16 3 8 */
     893,    /* OBJ_id_alg_PWRI_KEK              1 2 ********** 1 9 16 3 9 */
     248,    /* OBJ_id_smime_cd_ldap             1 2 ********** 1 9 16 4 1 */
     249,    /* OBJ_id_smime_spq_ets_sqt_uri     1 2 ********** 1 9 16 5 1 */
     250,    /* OBJ_id_smime_spq_ets_sqt_unotice 1 2 ********** 1 9 16 5 2 */
     251,    /* OBJ_id_smime_cti_ets_proofOfOrigin 1 2 ********** 1 9 16 6 1 */
     252,    /* OBJ_id_smime_cti_ets_proofOfReceipt 1 2 ********** 1 9 16 6 2 */
     253,    /* OBJ_id_smime_cti_ets_proofOfDelivery 1 2 ********** 1 9 16 6 3 */
     254,    /* OBJ_id_smime_cti_ets_proofOfSender 1 2 ********** 1 9 16 6 4 */
     255,    /* OBJ_id_smime_cti_ets_proofOfApproval 1 2 ********** 1 9 16 6 5 */
     256,    /* OBJ_id_smime_cti_ets_proofOfCreation 1 2 ********** 1 9 16 6 6 */
     150,    /* OBJ_keyBag                       1 2 ********** 1 12 10 1 1 */
     151,    /* OBJ_pkcs8ShroudedKeyBag          1 2 ********** 1 12 10 1 2 */
     152,    /* OBJ_certBag                      1 2 ********** 1 12 10 1 3 */
     153,    /* OBJ_crlBag                       1 2 ********** 1 12 10 1 4 */
     154,    /* OBJ_secretBag                    1 2 ********** 1 12 10 1 5 */
     155,    /* OBJ_safeContentsBag              1 2 ********** 1 12 10 1 6 */
     955,    /* OBJ_jurisdictionLocalityName     1 3 6 1 4 1 311 60 2 1 1 */
     956,    /* OBJ_jurisdictionStateOrProvinceName 1 3 6 1 4 1 311 60 2 1 2 */
     957,    /* OBJ_jurisdictionCountryName      1 3 6 1 4 1 311 60 2 1 3 */
    1259,    /* OBJ_oracle_jdk_trustedkeyusage   2 16 840 1 113894 746875 1 1 */
    1159,    /* OBJ_dstu4145be                   1 2 804 2 1 1 1 1 3 1 1 1 1 */
    1160,    /* OBJ_uacurve0                     1 2 804 2 1 1 1 1 3 1 1 2 0 */
    1161,    /* OBJ_uacurve1                     1 2 804 2 1 1 1 1 3 1 1 2 1 */
    1162,    /* OBJ_uacurve2                     1 2 804 2 1 1 1 1 3 1 1 2 2 */
    1163,    /* OBJ_uacurve3                     1 2 804 2 1 1 1 1 3 1 1 2 3 */
    1164,    /* OBJ_uacurve4                     1 2 804 2 1 1 1 1 3 1 1 2 4 */
    1165,    /* OBJ_uacurve5                     1 2 804 2 1 1 1 1 3 1 1 2 5 */
    1166,    /* OBJ_uacurve6                     1 2 804 2 1 1 1 1 3 1 1 2 6 */
    1167,    /* OBJ_uacurve7                     1 2 804 2 1 1 1 1 3 1 1 2 7 */
    1168,    /* OBJ_uacurve8                     1 2 804 2 1 1 1 1 3 1 1 2 8 */
    1169,    /* OBJ_uacurve9                     1 2 804 2 1 1 1 1 3 1 1 2 9 */
};
