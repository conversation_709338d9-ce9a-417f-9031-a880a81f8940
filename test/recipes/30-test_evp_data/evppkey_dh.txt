#
# Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = DH tests (with random keys)

PrivateKey=ALICE_dh2048
-----BEGIN PRIVATE KEY-----
MIICJgIBADCCARcGCSqGSIb3DQEDATCCAQgCggEBAIkq4hnfaUGeltLDB1eo3SWH
Mm9pJsCYb2E/gCXNgTWQBKebkg4ZXzEYUng889PfzNpxmDinl/H4WoBmoM/hJasJ
2PElcKejIAnYUi0SHHDkYKOJMo0W5H8fHcRk893vR0kllO+hP0e1Gur02TsdEF7w
bIPc+VV/7bMg9VxhQh9+ESeYgQXn/n3NSniUkO5aCjyzGy6Ex2/9dc+Xg6C08YJD
cerI660enEe0NaI50N1W65bbxdl3d6vHT9CGteXYiIuj0I5CG7CwnU2WV42we/Uw
YSq/6DfU93y5LSBZuOif8iLpGXfgE7TCAeY3H+WBAu43+gWz6xNKtbF0RMmT2VcC
AQIEggEEAoIBAH+HysSpzn94mlyNeevhb29SFyu3/maES+JRlVuHvpVOcHCrGR5J
fbXWiWnn03geb1hC9HIta6l9YKwWGQYLmVu/0bRedYeqC1JfgEvwYrZME9FO+AqQ
4CpXcG/mSGtHoMPtcsUakpprNxoh3xwI+GAfSDK1lW/aA0R/A7jNCV9+lmYJAJL6
5FJvhBAODSf7JUetlWvhwDBnvram7jRGJtlvpiwmmfk+Kb4AA1ZQMIHTnPNNPLJh
NTtqELKjk3i3xxlLLlWJEbF+ZAuseDPZITtwMr4fhoeKegez+lucu6uoMm7Wek7F
G0Ne0unywftCLUrmQ/Lboqaqvlv3PAFRaLs=
-----END PRIVATE KEY-----

PublicKey=ALICE_dh2048_pub
-----BEGIN PUBLIC KEY-----
MIICJDCCARcGCSqGSIb3DQEDATCCAQgCggEBAIkq4hnfaUGeltLDB1eo3SWHMm9p
JsCYb2E/gCXNgTWQBKebkg4ZXzEYUng889PfzNpxmDinl/H4WoBmoM/hJasJ2PEl
cKejIAnYUi0SHHDkYKOJMo0W5H8fHcRk893vR0kllO+hP0e1Gur02TsdEF7wbIPc
+VV/7bMg9VxhQh9+ESeYgQXn/n3NSniUkO5aCjyzGy6Ex2/9dc+Xg6C08YJDcerI
660enEe0NaI50N1W65bbxdl3d6vHT9CGteXYiIuj0I5CG7CwnU2WV42we/UwYSq/
6DfU93y5LSBZuOif8iLpGXfgE7TCAeY3H+WBAu43+gWz6xNKtbF0RMmT2VcCAQID
ggEFAAKCAQBT3K38hHa4aWKerb3st8S8Jy/abEwn3kRnLtWins75l9K4YDnIKHV9
/zTO0a75SxhvDDQqRiekw99Gel1zkxdo1/OJOTJs2KJ6Itedn3bU4p7k2599xstL
02OHWzvu4J9LQXZ0NVnhX3RzhJH/ZMZAchIVx4joRmaY7fGCXwYGsfrq7lrhYOwp
SSALvTdiNJLGm8nSOPXRWnAarTJfBhH/A38OsBY8a3bMHAlxigZWKtzY8GFIPh6O
Xu/+5iasto/aUiK6Oshcf7t4la3TY6GAbPz9SyPETV2jRn2+ujrkFON8I1wuz9ud
9d5iI/cRcKKrtLdIPGc7uqGy2V2qIp3v
-----END PUBLIC KEY-----

PrivateKey=BOB_dh2048
-----BEGIN PRIVATE KEY-----
MIICJgIBADCCARcGCSqGSIb3DQEDATCCAQgCggEBAIkq4hnfaUGeltLDB1eo3SWH
Mm9pJsCYb2E/gCXNgTWQBKebkg4ZXzEYUng889PfzNpxmDinl/H4WoBmoM/hJasJ
2PElcKejIAnYUi0SHHDkYKOJMo0W5H8fHcRk893vR0kllO+hP0e1Gur02TsdEF7w
bIPc+VV/7bMg9VxhQh9+ESeYgQXn/n3NSniUkO5aCjyzGy6Ex2/9dc+Xg6C08YJD
cerI660enEe0NaI50N1W65bbxdl3d6vHT9CGteXYiIuj0I5CG7CwnU2WV42we/Uw
YSq/6DfU93y5LSBZuOif8iLpGXfgE7TCAeY3H+WBAu43+gWz6xNKtbF0RMmT2VcC
AQIEggEEAoIBAE3A6u2we1mxSxDLkiakPs2zZqfhb2ejP2TvuA4DX/+knqrJRK7s
TqKBz80CvR7QA5CL4r/5BWJrmFet7cmF6Eh4PXE+sNswevV2C16btzAbGSGzSR5u
bu6vv06Ah1rPrHrv+UjvRe2bxR/Z/PavbI71aQ+lqDkGKC/Uyr3u+PFLRwos2HfK
nEX/qfnqo3N3YsPFvLidkbSJOkq6CayLT4ycTRdPxTVKELfanSEeenKJ9rKTiPdz
3y0ycyyh0kAKs2bBViq3UlVRE3kdNAQ3nCpRl9O52hCsA+BMmnAHEW8z81lEZugN
9X4uLV3XuflMrKxBGxfiCEVGcvnjpBTZXas=
-----END PRIVATE KEY-----

PublicKey=BOB_dh2048_pub
-----BEGIN PUBLIC KEY-----
MIICJDCCARcGCSqGSIb3DQEDATCCAQgCggEBAIkq4hnfaUGeltLDB1eo3SWHMm9p
JsCYb2E/gCXNgTWQBKebkg4ZXzEYUng889PfzNpxmDinl/H4WoBmoM/hJasJ2PEl
cKejIAnYUi0SHHDkYKOJMo0W5H8fHcRk893vR0kllO+hP0e1Gur02TsdEF7wbIPc
+VV/7bMg9VxhQh9+ESeYgQXn/n3NSniUkO5aCjyzGy6Ex2/9dc+Xg6C08YJDcerI
660enEe0NaI50N1W65bbxdl3d6vHT9CGteXYiIuj0I5CG7CwnU2WV42we/UwYSq/
6DfU93y5LSBZuOif8iLpGXfgE7TCAeY3H+WBAu43+gWz6xNKtbF0RMmT2VcCAQID
ggEFAAKCAQAJ1Ko5/dRMpowe/7k1T+11ZNszNMEbKtXGq9KsZAqSFp6h8on/kCFe
3/SK520B/cDIjLV0W4q33iimSmG/TjwLG0jpsIKwxbzIfxjs4e1Y4pPaKfbKuCcK
BBu8Thz2brhTxooU4CPVBJerX3mJX6hbAPAl0XcIScZPcyYYHkvthjZvO0djEn1z
inhUQ37JKc74+56w/nUDjDiDFZMnwkPCtkdzLsbWW+Mu1Ysd1qQyb1XcNfkO0d/t
rX00AaHwYqM7uM4slSqZhCi2w4zVvwP0BPD/TrPAjEFYEk0l2eNxBBmW5Mlss2Od
wtMjfaolhtG/beqmAneP14mCEM4lwH4a
-----END PUBLIC KEY-----

PrivateKey=BOB_dh2048_different_pg
-----BEGIN PRIVATE KEY-----
MIICJgIBADCCARcGCSqGSIb3DQEDATCCAQgCggEBAM/ZBdjCzvFon8sEMWEXQ4vw
bFV3VCq3nhCuHSLb/ZsHIuKFy/ma173ttMdN1qSOL2XJazi8l+4whU0Wga8FticR
z9U0t8ExC+0f90QRXsjXGSuDFJY6i5m6YL/xZf09g7522RqNwt4WWACgKrG7g31b
+43eXosvv5okgw5gmMrlGNZYaqnn094Ifu0UhUro6vXmlcfhnv1JpASNmxNmzrQJ
TE8W5miG5c1+6MQJdmSVKEitdUX202mRukj2Nzq16e6iOGmGMTWYGV+sK/Hnh+BD
hPVIqLWW5Wfz3y5ysCsGmSKuS9bssZXWmlwRiNp/NqliyzBznG168VoKMlDQxG8C
AQIEggEEAoIBAHLPQHe9mySBSeNAqUMzKAXgxhPYPl9zC7QiyxwGiZcu/4IQ8Elr
cyRhMuJBbu0T6NNruG2zUzDFWp/0dPGWq7YiOauau5iYCpKt4KZgiELDEwZqlxF+
wXKXxSw3rJVemZyYuLmRzmUMcvBr5ZspdxsDRipN9mHSGTtQNGAiSPXB4P/bkaSW
YELavS+cHZfH4nE7DAP8b8dEID81xBLGEKAyUilDE2Nv4PamhCU5GmVSv2HXVSdI
bGlj1BpvrfJyuK5EyyOqMKvwXiEQweqrVICGNtQQs+bmNpaVBCu/xRomWn5YZkqc
YjQBeOQwrhEFzbFwWi8vai9HB85tBLKEMiE=
-----END PRIVATE KEY-----

PublicKey=BOB_dh2048_different_pg_pub
-----BEGIN PUBLIC KEY-----
MIICJTCCARcGCSqGSIb3DQEDATCCAQgCggEBAM/ZBdjCzvFon8sEMWEXQ4vwbFV3
VCq3nhCuHSLb/ZsHIuKFy/ma173ttMdN1qSOL2XJazi8l+4whU0Wga8FticRz9U0
t8ExC+0f90QRXsjXGSuDFJY6i5m6YL/xZf09g7522RqNwt4WWACgKrG7g31b+43e
Xosvv5okgw5gmMrlGNZYaqnn094Ifu0UhUro6vXmlcfhnv1JpASNmxNmzrQJTE8W
5miG5c1+6MQJdmSVKEitdUX202mRukj2Nzq16e6iOGmGMTWYGV+sK/Hnh+BDhPVI
qLWW5Wfz3y5ysCsGmSKuS9bssZXWmlwRiNp/NqliyzBznG168VoKMlDQxG8CAQID
ggEGAAKCAQEAr5J9x7z9FWCQ27LF3pzTR+4ZdOKJJpg1qD8Vp78sg6VkoZLl+j57
uTQVgWlCEQr5tOVCBA+F0i3DCVGpE0ExkO/lVYjf6+mwTsyZWZj6IKkxpxwm9CIR
NshubotT9vagLxdRM9jepmt6A8Q2MfI1xm4yrYLZ0xQpNPT6076a+3gl6UysaDv8
wpryAiQdIexC1jz4Z7yIAND3PHWFAiAIOdipIMmuvJz2exgP1KsYTZFjJUTMBZxN
fSO65JSqf6LoQ91cB5RtgwD2pjehoAl2wumjdMcdNJyYIPvcTd1BFMjUmR3vJe/n
di2IvD9wSm5tY542PWcf/7GV0bMbnykaPQ==
-----END PUBLIC KEY-----

PublicKey=BOB_dh2048_badpub_toolarge
-----BEGIN PUBLIC KEY-----
MIICJTCCARcGCSqGSIb3DQEDATCCAQgCggEBAIkq4hnfaUGeltLDB1eo3SWHMm9p
JsCYb2E/gCXNgTWQBKebkg4ZXzEYUng889PfzNpxmDinl/H4WoBmoM/hJasJ2PEl
cKejIAnYUi0SHHDkYKOJMo0W5H8fHcRk893vR0kllO+hP0e1Gur02TsdEF7wbIPc
+VV/7bMg9VxhQh9+ESeYgQXn/n3NSniUkO5aCjyzGy6Ex2/9dc+Xg6C08YJDcerI
660enEe0NaI50N1W65bbxdl3d6vHT9CGteXYiIuj0I5CG7CwnU2WV42we/UwYSq/
6DfU93y5LSBZuOif8iLpGXfgE7TCAeY3H+WBAu43+gWz6xNKtbF0RMmT2VcCAQID
ggEGAAKCAQEAiSriGd9pQZ6W0sMHV6jdJYcyb2kmwJhvYT+AJc2BNZAEp5uSDhlf
MRhSeDzz09/M2nGYOKeX8fhagGagz+ElqwnY8SVwp6MgCdhSLRIccORgo4kyjRbk
fx8dxGTz3e9HSSWU76E/R7Ua6vTZOx0QXvBsg9z5VX/tsyD1XGFCH34RJ5iBBef+
fc1KeJSQ7loKPLMbLoTHb/11z5eDoLTxgkNx6sjrrR6cR7Q1ojnQ3VbrltvF2Xd3
q8dP0Ia15diIi6PQjkIbsLCdTZZXjbB79TBhKr/oN9T3fLktIFm46J/yIukZd+AT
tMIB5jcf5YEC7jf6BbPrE0q1sXREyZPZVg==
-----END PUBLIC KEY-----

PublicKey=BOB_dh2048_badpub_toosmall
-----BEGIN PUBLIC KEY-----
MIIBITCCARcGCSqGSIb3DQEDATCCAQgCggEBAIkq4hnfaUGeltLDB1eo3SWHMm9p
JsCYb2E/gCXNgTWQBKebkg4ZXzEYUng889PfzNpxmDinl/H4WoBmoM/hJasJ2PEl
cKejIAnYUi0SHHDkYKOJMo0W5H8fHcRk893vR0kllO+hP0e1Gur02TsdEF7wbIPc
+VV/7bMg9VxhQh9+ESeYgQXn/n3NSniUkO5aCjyzGy6Ex2/9dc+Xg6C08YJDcerI
660enEe0NaI50N1W65bbxdl3d6vHT9CGteXYiIuj0I5CG7CwnU2WV42we/UwYSq/
6DfU93y5LSBZuOif8iLpGXfgE7TCAeY3H+WBAu43+gWz6xNKtbF0RMmT2VcCAQID
BAACAQE=
-----END PUBLIC KEY-----

# DH Alice with Bob peer
Availablein = default
Derive=ALICE_dh2048
PeerKeyValidate=BOB_dh2048_pub
SharedSecret=28f1f890a14899b5fb600dc43ef28cdc065535bc5ee2b3e08ebb53f7ee93618f3471c0696bce289c3839ae6ced374a799c61d76cb9c60ecdc3bd75ac4ed9f060fcfa972a5ce899ff17120ce70e35d720797a62d2b3d724b9d21b9dc0f4f4ec1cbf4730d57955dc1be53210e3d10ed3e78a96914e0a201e0cc0d75744e2d8f6ed8a301bca465d4d1a518a5cda8219bdf562796842bd6a839369b5cacc77e44a9ac8475d50df6d7bddfb661241c566acd025642bc6b1bbcecb1c1e5a1429c9df552ad6a39194074b21f4e890bd79e934150850561932fee3c44ccf0e8bcd22df2f9cafad2e2f19364344fe588523a9da8545081c2118fd6b8c0e12ede6b9f5f258

# DH Bob with Alice peer
Availablein = default
Derive=BOB_dh2048
PeerKeyValidate=ALICE_dh2048_pub
SharedSecret=28f1f890a14899b5fb600dc43ef28cdc065535bc5ee2b3e08ebb53f7ee93618f3471c0696bce289c3839ae6ced374a799c61d76cb9c60ecdc3bd75ac4ed9f060fcfa972a5ce899ff17120ce70e35d720797a62d2b3d724b9d21b9dc0f4f4ec1cbf4730d57955dc1be53210e3d10ed3e78a96914e0a201e0cc0d75744e2d8f6ed8a301bca465d4d1a518a5cda8219bdf562796842bd6a839369b5cacc77e44a9ac8475d50df6d7bddfb661241c566acd025642bc6b1bbcecb1c1e5a1429c9df552ad6a39194074b21f4e890bd79e934150850561932fee3c44ccf0e8bcd22df2f9cafad2e2f19364344fe588523a9da8545081c2118fd6b8c0e12ede6b9f5f258

# DH Alice with Bob peer - mismatching domain parameters
Availablein = default
Derive=BOB_dh2048
PeerKeyValidate=BOB_dh2048_different_pg_pub
Result = DERIVE_SET_PEER_ERROR

# DH Alice with Bob peer - pub > p - 2
Availablein = default
Derive=ALICE_dh2048
PeerKeyValidate=BOB_dh2048_badpub_toolarge
Result = DERIVE_SET_PEER_ERROR

# DH Alice with Bob peer - pub < 2 should fail
Availablein = default
Derive=ALICE_dh2048
PeerKeyValidate=BOB_dh2048_badpub_toosmall
Result = DERIVE_SET_PEER_ERROR
