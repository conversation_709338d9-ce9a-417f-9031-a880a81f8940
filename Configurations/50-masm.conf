# We can't make commitment to supporting Microsoft assembler,
# because it would mean supporting all masm versions. This in
# in turn is because masm is not really an interchangeable option,
# while users tend to have reasons to stick with specific Visual
# Studio versions. It's usually lesser hassle to make it work
# with latest assembler, but tweaking for older versions had
# proven to be daunting task. This is experimental target, for
# production builds stick with [up-to-date version of] nasm.

my %targets = (
    "VC-WIN64A-masm" => {
        inherit_from    => [ "VC-WIN64-common" ],
        AS              => "ml64",
        ASFLAGS         => "/nologo /Zi",
        asoutflag       => "/Fo",
        asflags         => "/c /Cp /Cx",
        sys_id          => "WIN64A",
        uplink_arch      => 'x86_64',
        asm_arch         => 'x86_64',
        perlasm_scheme   => "masm",
    },
);
