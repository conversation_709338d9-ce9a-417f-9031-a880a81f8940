## -*- mode: perl; -*-
## Standard openssl configuration targets.

# Helper functions for the Windows configs
my $vc_win64a_info = {};
sub vc_win64a_info {
    unless (%$vc_win64a_info) {
        if (`nasm -v 2>NUL` =~ /NASM version ([0-9]+\.[0-9]+)/ && $1 >= 2.0) {
            $vc_win64a_info = { AS        => "nasm",
                                ASFLAGS   => "-g",
                                asflags   => "-Ox -f win64 -DNEAR",
                                asoutflag => "-o " };
        } elsif ($disabled{asm}) {
            # assembler is still used to compile uplink shim
            $vc_win64a_info = { AS        => "ml64",
                                ASFLAGS   => "/nologo /Zi",
                                asflags   => "/c /Cp /Cx",
                                asoutflag => "/Fo" };
        } else {
            $die->("NASM not found - make sure it's installed and available on %PATH%\n");
            $vc_win64a_info = { AS        => "{unknown}",
                                ASFLAGS   => "",
                                asflags   => "",
                                asoutflag => "" };
        }
    }
    return $vc_win64a_info;
}

my $vc_win32_info = {};
sub vc_win32_info {
    unless (%$vc_win32_info) {
        my $ver=`nasm -v 2>NUL`;
        my $vew=`nasmw -v 2>NUL`;
        if ($ver ne "" || $vew ne "") {
            $vc_win32_info = { AS        => $ver ge $vew ? "nasm" : "nasmw",
                               ASFLAGS   => "",
                               asflags   => "-f win32",
                               asoutflag => "-o ",
                               perlasm_scheme => "win32n" };
        } elsif ($disabled{asm}) {
            # not actually used, uplink shim is inlined into C code
            $vc_win32_info = { AS        => "ml",
                               ASFLAGS   => "/nologo /Zi",
                               asflags   => "/Cp /coff /c /Cx",
                               asoutflag => "/Fo",
                               perlasm_scheme => "win32" };
        } else {
            $die->("NASM not found - make sure it's installed and available on %PATH%\n");
            $vc_win32_info = { AS        => "{unknown}",
                               ASFLAGS   => "",
                               asflags   => "",
                               asoutflag => "",
                               perlasm_scheme => "win32" };
        }
    }
    return $vc_win32_info;
}

my $vc_wince_info = {};
sub vc_wince_info {
    unless (%$vc_wince_info) {
        # sanity check
        $die->('%OSVERSION% is not defined') if (!defined(env('OSVERSION')));
        $die->('%PLATFORM% is not defined')  if (!defined(env('PLATFORM')));
        $die->('%TARGETCPU% is not defined') if (!defined(env('TARGETCPU')));

        #
        # Idea behind this is to mimic flags set by eVC++ IDE...
        #
        my $wcevers = env('OSVERSION');                     # WCENNN
	my $wcevernum;
	my $wceverdotnum;
	if ($wcevers =~ /^WCE([1-9])([0-9]{2})$/) {
	    $wcevernum = "$1$2";
	    $wceverdotnum = "$1.$2";
	} else {
	    $die->('%OSVERSION% value is insane');
	    $wcevernum = "{unknown}";
	    $wceverdotnum = "{unknown}";
	}
        my $wcecdefs = "-D_WIN32_WCE=$wcevernum -DUNDER_CE=$wcevernum"; # -D_WIN32_WCE=NNN
        my $wcelflag = "/subsystem:windowsce,$wceverdotnum";        # ...,N.NN

        my $wceplatf =  env('PLATFORM');

        $wceplatf =~ tr/a-z0-9 /A-Z0-9_/;
        $wcecdefs .= " -DWCE_PLATFORM_$wceplatf";

        my $wcetgt = env('TARGETCPU');                      # just shorter name...
      SWITCH: for($wcetgt) {
          /^X86/        && do { $wcecdefs.=" -Dx86 -D_X86_ -D_i386_ -Di_386_";
                                $wcelflag.=" /machine:X86";     last; };
          /^ARMV4[IT]/  && do { $wcecdefs.=" -DARM -D_ARM_ -D$wcetgt";
                                $wcecdefs.=" -DTHUMB -D_THUMB_" if($wcetgt=~/T$/);
                                $wcecdefs.=" -QRarch4T -QRinterwork-return";
                                $wcelflag.=" /machine:THUMB";   last; };
          /^ARM/        && do { $wcecdefs.=" -DARM -D_ARM_ -D$wcetgt";
                                $wcelflag.=" /machine:ARM";     last; };
          /^MIPSIV/     && do { $wcecdefs.=" -DMIPS -D_MIPS_ -DR4000 -D$wcetgt";
                                $wcecdefs.=" -D_MIPS64 -QMmips4 -QMn32";
                                $wcelflag.=" /machine:MIPSFPU"; last; };
          /^MIPS16/     && do { $wcecdefs.=" -DMIPS -D_MIPS_ -DR4000 -D$wcetgt";
                                $wcecdefs.=" -DMIPSII -QMmips16";
                                $wcelflag.=" /machine:MIPS16";  last; };
          /^MIPSII/     && do { $wcecdefs.=" -DMIPS -D_MIPS_ -DR4000 -D$wcetgt";
                                $wcecdefs.=" -QMmips2";
                                $wcelflag.=" /machine:MIPS";    last; };
          /^R4[0-9]{3}/ && do { $wcecdefs.=" -DMIPS -D_MIPS_ -DR4000";
                                $wcelflag.=" /machine:MIPS";    last; };
          /^SH[0-9]/    && do { $wcecdefs.=" -D$wcetgt -D_${wcetgt}_ -DSHx";
                                $wcecdefs.=" -Qsh4" if ($wcetgt =~ /^SH4/);
                                $wcelflag.=" /machine:$wcetgt"; last; };
          { $wcecdefs.=" -D$wcetgt -D_${wcetgt}_";
            $wcelflag.=" /machine:$wcetgt";                     last; };
      }

        $vc_wince_info = { cppflags => $wcecdefs,
                           lflags => $wcelflag };
    }
    return $vc_wince_info;
}

my %targets = (

#### Basic configs that should work on any 32-bit box
    "gcc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => picker(debug   => "-O0 -g",
                                   release => "-O3"),
        thread_scheme    => "(unknown)",
        bn_ops           => "BN_LLONG",
    },
    "cc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        CFLAGS           => "-O",
        thread_scheme    => "(unknown)",
    },

#### VOS Configurations
    "vos-gcc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => picker(default => "-Wall",
                                   debug   => "-O0 -g",
                                   release => "-O3"),
        cppflags         => "-D_POSIX_C_SOURCE=200112L -D_BSD -D_VOS_EXTENDED_NAMES",
        lib_cppflags     => "-DB_ENDIAN",
        thread_scheme    => "(unknown)",
        sys_id           => "VOS",
        lflags           => add("-Wl,-map"),
        bn_ops           => "BN_LLONG",
        shared_extension => ".so",
    },

#### Solaris configurations
    "solaris-common" => {
        inherit_from     => [ "BASE_unix" ],
        template         => 1,
        lib_cppflags     => "-DFILIO_H",
        ex_libs          => add("-lsocket -lnsl -ldl"),
        dso_scheme       => "dlfcn",
        thread_scheme    => "pthreads",
    },
#### Solaris common with Sun C setups
    "solaris-common-cc" => {
        inherit_from     => [ "solaris-common" ],
        template         => 1,
        shared_target    => "solaris",
        shared_ldflag    => "-Wl,-Bsymbolic",
        shared_defflag   => "-Wl,-M,",
        shared_sonameflag=> "-Wl,-h,",
    },
#### Solaris common with GNU C setups
    "solaris-common-gcc" => {
        inherit_from     => [ "solaris-common" ],
        template         => 1,
        shared_target    => "solaris-gcc-shared", # The rest is on shared_info.pl
    },
#### Solaris x86 with GNU C setups
    "solaris-x86-gcc" => {
        # NB. GNU C has to be configured to use GNU assembler, and not
        # /usr/ccs/bin/as. Failure to comply will result in compile
        # failures [at least] in 32-bit build.
        inherit_from     => [ "solaris-common-gcc" ],
        CC               => "gcc",
        CFLAGS           => add_before(picker(default => "-Wall",
                                              debug   => "-O0 -g",
                                              release => "-O3 -fomit-frame-pointer")),
        cflags           => add(threads("-pthread")),
        lib_cppflags     => add("-DL_ENDIAN"),
        ex_libs          => add(threads("-pthread")),
        bn_ops           => "BN_LLONG",
        shared_cflag     => "-fPIC",
        shared_ldflag    => add_before("-shared -static-libgcc"),
        asm_arch         => 'x86',
        perlasm_scheme   => 'elf',
    },
    "solaris64-x86_64-gcc" => {
        # -shared -static-libgcc might appear controversial, but modules
        # taken from static libgcc do not have relocations and linking
        # them into our shared objects doesn't have any negative side
        # effects. On the contrary, doing so makes it possible to use
        # gcc shared build with Sun C. Given that gcc generates faster
        # code [thanks to inline assembler], I would actually recommend
        # to consider using gcc shared build even with vendor compiler:-)
        # <AUTHOR> <EMAIL>
        inherit_from     => [ "solaris-common-gcc" ],
        CC               => "gcc",
        CFLAGS           => add_before(picker(default => "-Wall",
                                              debug   => "-O0 -g",
                                              release => "-O3")),
        cflags           => add_before("-m64", threads("-pthread")),
        lib_cppflags     => add("-DL_ENDIAN"),
        ex_libs          => add(threads("-pthread")),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'x86_64',
        perlasm_scheme   => "elf",
        shared_cflag     => "-fPIC",
        shared_ldflag    => add_before("-shared -static-libgcc"),
        multilib         => "/64",
    },

#### Solaris x86 with Sun C setups
    # There used to be solaris-x86-cc target, but it was removed,
    # primarily because vendor assembler can't assemble our modules
    # with -KPIC flag. As result it, assembly support, was not even
    # available as option. But its lack means lack of side-channel
    # resistant code, which is incompatible with security by today's
    # standards. Fortunately gcc is readily available prepackaged
    # option, which we can firmly point at...
    #
    # On related note, solaris64-x86_64-cc target won't compile code
    # paths utilizing AVX and post-Haswell instruction extensions.
    # Consider switching to solaris64-x86_64-gcc even here...
    #
    "solaris64-x86_64-cc" => {
        inherit_from     => [ "solaris-common-cc" ],
        CC               => "cc",
        CFLAGS           => add_before(picker(debug   => "-g",
                                              release => "-xO5 -xdepend -xbuiltin")),
        cflags           => add_before("-xarch=generic64 -xstrconst -Xa"),
        cppflags         => add(threads("-D_REENTRANT")),
        lib_cppflags     => add("-DL_ENDIAN"),
        thread_scheme    => "pthreads",
        lflags           => add(threads("-mt")),
        ex_libs          => add(threads("-lpthread")),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'x86_64',
        perlasm_scheme   => "elf",
        shared_cflag     => "-KPIC",
        shared_ldflag    => add_before("-G -dy -z text"),
        multilib         => "/64",
    },

#### IRIX 6.x configs
# Only N32 and N64 ABIs are supported.
    "irix-common" => {
        inherit_from     => [ "BASE_unix" ],
        template         => 1,
        cppflags         => threads("-D_SGI_MP_SOURCE"),
        lib_cppflags     => "-DB_ENDIAN",
        ex_libs          => add(threads("-lpthread")),
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "self",
        shared_ldflag    => "-shared -Wl,-Bsymbolic",
        shared_sonameflag=> "-Wl,-soname,",
    },
    "irix-mips3-gcc" => {
        inherit_from     => [ "irix-common" ],
        CC               => "gcc",
        CFLAGS           => picker(debug   => "-g -O0",
                                   release => "-O3"),
        LDFLAGS          => "-static-libgcc",
        cflags           => "-mabi=n32",
        bn_ops           => "RC4_CHAR SIXTY_FOUR_BIT",
        asm_arch         => 'mips64',
        perlasm_scheme   => "n32",
        multilib         => "32",
    },
    "irix-mips3-cc" => {
        inherit_from     => [ "irix-common" ],
        CC               => "cc",
        CFLAGS           => picker(debug   => "-g -O0",
                                   release => "-O2"),
        cflags           => "-n32 -mips3 -use_readonly_const -G0 -rdata_shared",
        bn_ops           => "RC4_CHAR SIXTY_FOUR_BIT",
        asm_arch         => 'mips64',
        perlasm_scheme   => "n32",
        multilib         => "32",
    },
    # N64 ABI builds.
    "irix64-mips4-gcc" => {
        inherit_from     => [ "irix-common" ],
        CC               => "gcc",
        CFLAGS           => picker(debug   => "-g -O0",
                                   release => "-O3"),
        LDFLAGS          => "-static-libgcc",
        cflags           => "-mabi=64 -mips4",
        bn_ops           => "RC4_CHAR SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'mips64',
        perlasm_scheme   => "64",
        multilib         => "64",
    },
    "irix64-mips4-cc" => {
        inherit_from     => [ "irix-common" ],
        CC               => "cc",
        CFLAGS           => picker(debug   => "-g -O0",
                                   release => "-O2"),
        cflags           => "-64 -mips4 -use_readonly_const -G0 -rdata_shared",
        bn_ops           => "RC4_CHAR SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'mips64',
        perlasm_scheme   => "64",
        multilib         => "64",
    },

#### HP MPE/iX http://jazz.external.hp.com/src/openssl/
    "MPE/iX-gcc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => "-O3",
        cppflags         => "-D_POSIX_SOURCE -D_SOCKET_SOURCE",
        includes         => [ "/SYSLOG/PUB" ],
        lib_cppflags     => "-DBN_DIV2W",
        sys_id           => "MPE",
        lflags           => add("-L/SYSLOG/PUB"),
        ex_libs          => add("-lsyslog -lsocket -lcurses"),
        thread_scheme    => "(unknown)",
        bn_ops           => "BN_LLONG",
    },

#### DEC Alpha Tru64 targets. Tru64 is marketing name for OSF/1 version 4
#### and forward. In reality 'uname -s' still returns "OSF1". Originally
#### there were even osf1-* configs targeting prior versions provided,
#### but not anymore...
    "tru64-alpha-gcc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => "-O3",
        cflags           => add("-std=c9x", threads("-pthread")),
        cppflags         => "-D_XOPEN_SOURCE=500 -D_OSF_SOURCE",
        ex_libs          => add("-lrt", threads("-pthread")), # for mlock(2)
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'alpha',
        perlasm_scheme   => "void",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "alpha-osf1-shared",
        shared_extension => ".so",
    },
    "tru64-alpha-cc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        CFLAGS           => "-tune host -fast",
        cflags           => add("-std1 -readonly_strings",
                                threads("-pthread")),
        cppflags         => "-D_XOPEN_SOURCE=500 -D_OSF_SOURCE",
        ex_libs          => add("-lrt", threads("-pthread")), # for mlock(2)
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'alpha',
        perlasm_scheme   => "void",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "alpha-osf1-shared",
        shared_ldflag    => "-msym",
        shared_extension => ".so",
    },

####
#### Variety of LINUX:-)
####
# *-generic* is endian-neutral target, but ./config is free to
# throw in -D[BL]_ENDIAN, whichever appropriate...
    "linux-generic32" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CXX              => "g++",
        CFLAGS           => picker(default => "-Wall",
                                   debug   => "-O0 -g",
                                   release => "-O3"),
        CXXFLAGS         => picker(default => "-Wall",
                                   debug   => "-O0 -g",
                                   release => "-O3"),
        cflags           => threads("-pthread"),
        cxxflags         => combine("-std=c++11", threads("-pthread")),
        lib_cppflags     => "-DOPENSSL_USE_NODELETE",
        ex_libs          => add("-ldl", threads("-pthread")),
        bn_ops           => "BN_LLONG RC4_CHAR",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "linux-shared",
        shared_cflag     => "-fPIC",
        shared_ldflag    => sub { $disabled{pinshared} ? () : "-Wl,-znodelete" },
        enable           => [ "afalgeng" ],
    },
    "linux-latomic" => {
        inherit_from     => [ "linux-generic32" ],
        ex_libs          => add(threads("-latomic")),
    },
    "linux-generic64" => {
        inherit_from     => [ "linux-generic32" ],
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
    },

    "linux-ppc" => {
        inherit_from     => [ "linux-latomic" ],
        asm_arch         => 'ppc32',
        perlasm_scheme   => "linux32",
        lib_cppflags     => add("-DB_ENDIAN"),
    },
    "linux-ppc64" => {
        inherit_from     => [ "linux-generic64" ],
        cflags           => add("-m64"),
        cxxflags         => add("-m64"),
        lib_cppflags     => add("-DB_ENDIAN"),
        asm_arch         => 'ppc64',
        perlasm_scheme   => "linux64",
        multilib         => "64",
    },
    "linux-ppc64le" => {
        inherit_from     => [ "linux-generic64" ],
        cflags           => add("-m64"),
        cxxflags         => add("-m64"),
        lib_cppflags     => add("-DL_ENDIAN"),
        asm_arch         => 'ppc64',
        perlasm_scheme   => "linux64le",
    },

    "linux-armv4" => {
        ################################################################
        # Note that -march is not among compiler options in linux-armv4
        # target description. Not specifying one is intentional to give
        # you choice to:
        #
        # a) rely on your compiler default by not specifying one;
        # b) specify your target platform explicitly for optimal
        # performance, e.g. -march=armv6 or -march=armv7-a;
        # c) build "universal" binary that targets *range* of platforms
        # by specifying minimum and maximum supported architecture;
        #
        # As for c) option. It actually makes no sense to specify
        # maximum to be less than ARMv7, because it's the least
        # requirement for run-time switch between platform-specific
        # code paths. And without run-time switch performance would be
        # equivalent to one for minimum. Secondly, there are some
        # natural limitations that you'd have to accept and respect.
        # Most notably you can *not* build "universal" binary for
        # big-endian platform. This is because ARMv7 processor always
        # picks instructions in little-endian order. Another similar
        # limitation is that -mthumb can't "cross" -march=armv6t2
        # boundary, because that's where it became Thumb-2. Well, this
        # limitation is a bit artificial, because it's not really
        # impossible, but it's deemed too tricky to support. And of
        # course you have to be sure that your binutils are actually
        # up to the task of handling maximum target platform. With all
        # this in mind here is an example of how to configure
        # "universal" build:
        #
        # ./Configure linux-armv4 -march=armv6 -D__ARM_MAX_ARCH__=8
        #
        inherit_from     => [ "linux-latomic" ],
        asm_arch         => 'armv4',
        perlasm_scheme   => "linux32",
    },
    "linux-aarch64" => {
        inherit_from     => [ "linux-generic64" ],
        asm_arch         => 'aarch64',
        perlasm_scheme   => "linux64",
    },
    "linux-arm64ilp32" => {  # https://wiki.linaro.org/Platform/arm64-ilp32
        inherit_from     => [ "linux-generic32" ],
        cflags           => add("-mabi=ilp32"),
        cxxflags         => add("-mabi=ilp32"),
        bn_ops           => "SIXTY_FOUR_BIT RC4_CHAR",
        asm_arch         => 'aarch64',
        perlasm_scheme   => "linux64",
    },

    "linux-mips32" => {
        # Configure script adds minimally required -march for assembly
        # support, if no -march was specified at command line.
        inherit_from     => [ "linux-latomic" ],
        cflags           => add("-mabi=32"),
        cxxflags         => add("-mabi=32"),
        asm_arch         => 'mips32',
        perlasm_scheme   => "o32",
    },
    # mips32 and mips64 below refer to contemporary MIPS Architecture
    # specifications, MIPS32 and MIPS64, rather than to kernel bitness.
    "linux-mips64" => {
        inherit_from     => [ "linux-latomic" ],
        cflags           => add("-mabi=n32"),
        cxxflags         => add("-mabi=n32"),
        bn_ops           => "RC4_CHAR",
        asm_arch         => 'mips64',
        perlasm_scheme   => "n32",
        multilib         => "32",
    },
    "linux64-mips64" => {
        inherit_from     => [ "linux-generic64" ],
        cflags           => add("-mabi=64"),
        cxxflags         => add("-mabi=64"),
        asm_arch         => 'mips64',
        perlasm_scheme   => "64",
        multilib         => "64",
    },

    # riscv64 below refers to contemporary RISCV Architecture
    # specifications,
    "linux64-riscv64" => {
        inherit_from     => [ "linux-generic64"],
        perlasm_scheme   => "linux64",
    },

    # loongarch64 below refers to contemporary LoongArch Architecture
    # specifications,
    "linux64-loongarch64" => {
        inherit_from     => [ "linux-generic64"],
        perlasm_scheme   => "linux64",
    },

    #### IA-32 targets...
    #### These two targets are a bit aged and are to be used on older Linux
    #### machines where gcc doesn't understand -m32 and -m64
    "linux-elf" => {
        inherit_from     => [ "linux-generic32" ],
        CFLAGS           => add(picker(release => "-fomit-frame-pointer")),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => "elf",
    },
    "linux-aout" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => add(picker(default => "-Wall",
                                       debug   => "-O0 -g",
                                       release => "-O3 -fomit-frame-pointer")),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "(unknown)",
        asm_arch         => 'x86',
        perlasm_scheme   => "a.out",
    },

    #### X86 / X86_64 targets
    "linux-x86" => {
        inherit_from     => [ "linux-generic32" ],
        CFLAGS           => add(picker(release => "-fomit-frame-pointer")),
        cflags           => add("-m32"),
        cxxflags         => add("-m32"),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => "elf",
    },
    "linux-x86-clang" => {
        inherit_from     => [ "linux-x86" ],
        CC               => "clang",
        CXX              => "clang++",
        ex_libs          => add(threads("-latomic")),
    },
    "linux-x86_64" => {
        inherit_from     => [ "linux-generic64" ],
        cflags           => add("-m64"),
        cxxflags         => add("-m64"),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'x86_64',
        perlasm_scheme   => "elf",
        multilib         => "64",
    },
    "linux-x86_64-clang" => {
        inherit_from     => [ "linux-x86_64" ],
        CC               => "clang",
        CXX              => "clang++",
    },
    "linux-x32" => {
        inherit_from     => [ "linux-generic32" ],
        cflags           => add("-mx32"),
        cxxflags         => add("-mx32"),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT",
        asm_arch         => 'x86_64',
        perlasm_scheme   => "elf32",
        multilib         => "x32",
    },

    "linux-ia64" => {
        inherit_from     => [ "linux-generic64" ],
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'ia64',
        perlasm_scheme   => 'void',
    },

    "linux64-s390x" => {
        inherit_from     => [ "linux-generic64" ],
        cflags           => add("-m64"),
        cxxflags         => add("-m64"),
        lib_cppflags     => add("-DB_ENDIAN"),
        asm_arch         => 's390x',
        perlasm_scheme   => "64",
        multilib         => "64",
    },
    "linux32-s390x" => {
        #### So called "highgprs" target for z/Architecture CPUs
        # "Highgprs" is kernel feature first implemented in Linux
        # 2.6.32, see /proc/cpuinfo. The idea is to preserve most
        # significant bits of general purpose registers not only
        # upon 32-bit process context switch, but even on
        # asynchronous signal delivery to such process. This makes
        # it possible to deploy 64-bit instructions even in legacy
        # application context and achieve better [or should we say
        # adequate] performance. The build is binary compatible with
        # linux-generic32, and the idea is to be able to install the
        # resulting libcrypto.so alongside generic one, e.g. as
        # /lib/highgprs/libcrypto.so.x.y, for ldconfig and run-time
        # linker to autodiscover. Unfortunately it doesn't work just
        # yet, because of couple of bugs in glibc
        # sysdeps/s390/dl-procinfo.c affecting ldconfig and ld.so.1...
        #
        inherit_from     => [ "linux-generic32" ],
        cflags           => add("-m31 -Wa,-mzarch"),
        cxxflags         => add("-m31 -Wa,-mzarch"),
        lib_cppflags     => add("-DB_ENDIAN"),
        asm_arch         => 's390x',
        perlasm_scheme   => "31",
        multilib         => "/highgprs",
    },

    "linux-alpha-gcc" => {
        inherit_from     => [ "linux-generic64" ],
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'alpha',
        perlasm_scheme   => "void",
    },
    "linux-c64xplus" => {
        inherit_from     => [ "BASE_unix" ],
        # TI_CGT_C6000_7.3.x is a requirement
        CC               => "cl6x",
        CFLAGS           => "-o2 -ox -ms",
        cflags           => "--linux -ea=.s -eo=.o -mv6400+ -pden",
        cxxflags         => "--linux -ea=.s -eo=.o -mv6400+ -pden",
        cppflags         => combine("-DOPENSSL_SMALL_FOOTPRINT",
                                    threads("-D_REENTRANT")),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "pthreads",
        asm_arch         => 'c64xplus',
        perlasm_scheme   => "void",
        dso_scheme       => "dlfcn",
        shared_target    => "linux-shared",
        shared_cflag     => "--pic",
        shared_ldflag    => add("-z --sysv --shared"),
        ranlib           => "true",
    },

#### *BSD
    "BSD-generic32" => {
        # As for thread cflag. Idea is to maintain "collective" set of
        # flags, which would cover all BSD flavors. -pthread applies
        # to them all, but is treated differently. OpenBSD expands is
        # as -D_POSIX_THREAD -lc_r, which is sufficient. FreeBSD 4.x
        # expands it as -lc_r, which has to be accompanied by explicit
        # -D_THREAD_SAFE and sometimes -D_REENTRANT. FreeBSD 5.x
        # expands it as -lc_r, which seems to be sufficient?
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        CFLAGS           => picker(default => "-Wall",
                                   debug   => "-O0 -g",
                                   release => "-O3"),
        cflags           => threads("-pthread"),
        cppflags         => threads("-D_THREAD_SAFE -D_REENTRANT"),
        ex_libs          => add(threads("-pthread")),
        enable           => add("devcryptoeng"),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "bsd-gcc-shared",
        shared_cflag     => "-fPIC",
    },
    "BSD-generic64" => {
        inherit_from     => [ "BSD-generic32" ],
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
    },

    "BSD-x86" => {
        inherit_from     => [ "BSD-generic32" ],
        CFLAGS           => add(picker(release => "-fomit-frame-pointer")),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => "a.out",
    },
    "BSD-x86-elf" => {
        inherit_from     => [ "BSD-x86" ],
        perlasm_scheme   => "elf",
    },

    "BSD-ia64" => {
        inherit_from     => [ "BSD-generic64" ],
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'ia64',
        perlasm_scheme   => 'void',
    },

    "BSD-x86_64" => {
        inherit_from     => [ "BSD-generic64" ],
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'x86_64',
        perlasm_scheme   => "elf",
    },

    # riscv64 below refers to contemporary RISCV Architecture
    # specifications,
    "BSD-riscv64" => {
        inherit_from     => [ "BSD-generic64"],
        perlasm_scheme   => "linux64",
    },

    "bsdi-elf-gcc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => "-fomit-frame-pointer -O3 -Wall",
        lib_cppflags     => "-DPERL5 -DL_ENDIAN",
        ex_libs          => add("-ldl"),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => "elf",
        thread_scheme    => "(unknown)",
        dso_scheme       => "dlfcn",
        shared_target    => "bsd-gcc-shared",
        shared_cflag     => "-fPIC",
    },

#### SCO/Caldera targets.
#
# Originally we had like unixware-*, unixware-*-pentium, unixware-*-p6, etc.
# Now we only have blended unixware-* as it's the only one used by ./config.
# If you want to optimize for particular microarchitecture, bypass ./config
# and './Configure unixware-7 -Kpentium_pro' or whatever appropriate.
# Note that not all targets include assembler support. Mostly because of
# lack of motivation to support out-of-date platforms with out-of-date
# compiler drivers and assemblers.
#
# UnixWare 2.0x fails destest with -O.
    "unixware-2.0" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        cflags           => threads("-Kthread"),
        lib_cppflags     => "-DFILIO_H -DNO_STRINGS_H",
        ex_libs          => add("-lsocket -lnsl -lresolv -lx"),
        thread_scheme    => "uithreads",
    },
    "unixware-2.1" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        CFLAGS           => "-O",
        cflags           => threads("-Kthread"),
        lib_cppflags     => "-DFILIO_H",
        ex_libs          => add("-lsocket -lnsl -lresolv -lx"),
        thread_scheme    => "uithreads",
    },
    "unixware-7" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        CFLAGS           => "-O",
        cflags           => combine("-Kalloca", threads("-Kthread")),
        lib_cppflags     => "-DFILIO_H",
        ex_libs          => add("-lsocket -lnsl"),
        thread_scheme    => "uithreads",
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => "elf-1",
        dso_scheme       => "dlfcn",
        shared_target    => "svr5-shared",
        shared_cflag     => "-Kpic",
    },
    "unixware-7-gcc" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => "-O3 -fomit-frame-pointer -Wall",
        cppflags         => add(threads("-D_REENTRANT")),
        lib_cppflags     => add("-DL_ENDIAN -DFILIO_H"),
        ex_libs          => add("-lsocket -lnsl"),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "pthreads",
        asm_arch         => 'x86',
        perlasm_scheme   => "elf-1",
        dso_scheme       => "dlfcn",
        shared_target    => "gnu-shared",
        shared_cflag     => "-fPIC",
    },
# SCO 5 - Ben Laurie says the -O breaks the SCO cc.
    "sco5-cc" => {
        inherit_from     => [ "BASE_unix" ],
        cc               => "cc",
        cflags           => "-belf",
        ex_libs          => add("-lsocket -lnsl"),
        thread_scheme    => "(unknown)",
        asm_arch         => 'x86',
        perlasm_scheme   => "elf-1",
        dso_scheme       => "dlfcn",
        shared_target    => "svr3-shared",
        shared_cflag     => "-Kpic",
    },
    "sco5-gcc" => {
        inherit_from     => [ "BASE_unix" ],
        cc               => "gcc",
        cflags           => "-O3 -fomit-frame-pointer",
        ex_libs          => add("-lsocket -lnsl"),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "(unknown)",
        asm_arch         => 'x86',
        perlasm_scheme   => "elf-1",
        dso_scheme       => "dlfcn",
        shared_target    => "svr3-shared",
        shared_cflag     => "-fPIC",
    },

#### IBM's AIX.
    # Below targets assume AIX >=5. Caveat lector. If you are accustomed
    # to control compilation "bitness" by setting $OBJECT_MODE environment
    # variable, then you should know that in OpenSSL case it's considered
    # only in ./config. Once configured, build procedure remains "deaf" to
    # current value of $OBJECT_MODE.
    "aix-common" => {
        inherit_from     => [ "BASE_unix" ],
        template         => 1,
        sys_id           => "AIX",
        lib_cppflags     => "-DB_ENDIAN",
        lflags           => "-Wl,-bsvr4",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "aix",
        module_ldflags   => "-Wl,-G,-bsymbolic,-bnoentry",
        shared_ldflag    => "-Wl,-G,-bsymbolic,-bnoentry",
        shared_defflag   => "-Wl,-bE:",
        shared_fipsflag  => "-Wl,-binitfini:_init:_cleanup",
        perl_platform    => 'AIX',
    },
    "aix-gcc" => {
        inherit_from     => [ "aix-common" ],
        CC               => "gcc",
        CFLAGS           => picker(debug   => "-O0 -g",
                                   release => "-O"),
        cflags           => add(threads("-pthread")),
        ex_libs          => add(threads("-pthread")),
        bn_ops           => "BN_LLONG RC4_CHAR",
        asm_arch         => 'ppc32',
        perlasm_scheme   => "aix32",
        shared_ldflag    => add_before("-shared -static-libgcc"),
        AR               => add("-X32"),
        RANLIB           => add("-X32"),
    },
    "aix64-gcc" => {
        inherit_from     => [ "aix-common" ],
        CC               => "gcc",
        CFLAGS           => picker(debug   => "-O0 -g",
                                   release => "-O"),
        cflags           => combine("-maix64", threads("-pthread")),
        ex_libs          => add(threads("-pthread")),
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
        asm_arch         => 'ppc64',
        perlasm_scheme   => "aix64",
        shared_ldflag    => add_before("-shared -static-libgcc"),
        shared_extension => "64.so.\$(SHLIB_VERSION_NUMBER)",
        AR               => add("-X64"),
        RANLIB           => add("-X64"),
    },
    "aix64-gcc-as" => {
        inherit_from     => [ "aix64-gcc" ],
        perlasm_scheme   => "aix64-as",
    },
    "aix-cc" => {
        inherit_from     => [ "aix-common" ],
        CC               => "cc",
        CFLAGS           => picker(debug   => "-O0 -g",
                                   release => "-O"),
        cflags           => combine("-q32 -qmaxmem=16384 -qro -qroconst",
                                    threads("-qthreaded")),
        cppflags         => threads("-D_THREAD_SAFE"),
        ex_libs          => add(threads("-lpthreads")),
        bn_ops           => "BN_LLONG RC4_CHAR",
        asm_arch         => 'ppc32',
        perlasm_scheme   => "aix32",
        shared_cflag     => "-qpic",
        AR               => add("-X32"),
        RANLIB           => add("-X32"),
    },
    "aix64-cc" => {
        inherit_from     => [ "aix-common" ],
        CC               => "cc",
        CFLAGS           => picker(debug   => "-O0 -g",
                                   release => "-O"),
        cflags           => combine("-q64 -qmaxmem=16384 -qro -qroconst",
                                    threads("-qthreaded")),
        cppflags         => threads("-D_THREAD_SAFE"),
        ex_libs          => add(threads("-lpthreads")),
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
        asm_arch         => 'ppc64',
        perlasm_scheme   => "aix64",
        dso_scheme       => "dlfcn",
        shared_cflag     => "-qpic",
        shared_extension => "64.so.\$(SHLIB_VERSION_NUMBER)",
        AR               => add("-X64"),
        RANLIB           => add("-X64"),
    },

# SIEMENS BS2000/OSD: an EBCDIC-based mainframe
    "BS2000-OSD" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "c89",
        CFLAGS           => "-O",
        cflags           => "-XLLML -XLLMK -XL",
        cppflags         => "-DCHARSET_EBCDIC",
        lib_cppflags     => "-DB_ENDIAN",
        ex_libs          => add("-lsocket -lnsl"),
        bn_ops           => "THIRTY_TWO_BIT RC4_CHAR",
        thread_scheme    => "(unknown)",
    },

#### Visual C targets
#
# Win64 targets, WIN64I denotes IA-64/Itanium and WIN64A - AMD64
#
# Note about /wd4090, disable warning C4090. This warning returns false
# positives in some situations. Disabling it altogether masks both
# legitimate and false cases, but as we compile on multiple platforms,
# we rely on other compilers to catch legitimate cases.
#
# Also note that we force threads no matter what.  Configuring "no-threads"
# is ignored.
#
# UNICODE is defined in VC-common and applies to all targets. It used to
# be an opt-in option for VC-WIN32, but not anymore. The original reason
# was because ANSI API was *native* system interface for no longer
# supported Windows 9x. Keep in mind that UNICODE only affects how
# OpenSSL libraries interact with underlying OS, it doesn't affect API
# that OpenSSL presents to application.

    "VC-common" => {
        inherit_from     => [ "BASE_Windows" ],
        template         => 1,
        CC               => "cl",
        CPP              => '"$(CC)" /EP /C',
        CFLAGS           => "/W3 /wd4090 /nologo",
        coutflag         => "/Fo",
        LD               => "link",
        LDFLAGS          => "/nologo /debug",
        ldoutflag        => "/out:",
        ldpostoutflag    => "",
        ld_resp_delim    => "\n",
        bin_lflags       => "setargv.obj",
        makedepcmd       => '"$(CC)" /Zs /showIncludes',
        makedep_scheme   => 'VC',
        AR               => "lib",
        ARFLAGS          => "/nologo",
        aroutflag        => "/out:",
        ar_resp_delim    => "\n",
        RC               => "rc",
        rcoutflag        => "/fo",
        defines          => add("OPENSSL_SYS_WIN32", "WIN32_LEAN_AND_MEAN",
                                "UNICODE", "_UNICODE",
                                "_CRT_SECURE_NO_DEPRECATE",
                                "_WINSOCK_DEPRECATED_NO_WARNINGS"),
        lib_cflags       => add("/Zi /Fdossl_static.pdb"),
        lib_defines      => add("L_ENDIAN"),
        dso_cflags       => "/Zi /Fddso.pdb",
        bin_cflags       => "/Zi /Fdapp.pdb",
        # def_flag made to empty string so a .def file gets generated
        shared_defflag   => '',
        shared_ldflag    => "/dll",
        shared_target    => "win-shared", # meaningless except it gives Configure a hint
        lddefflag        => "/def:",
        ldresflag        => " ",
        ld_implib_flag   => "/implib:",
        thread_scheme    => "winthreads",
        dso_scheme       => "win32",
        perl_platform    => 'Windows::MSVC',
        # additional parameter to build_scheme denotes install-path "flavour"
        build_scheme     => add("VC-common", { separator => undef }),
    },
    "VC-noCE-common" => {
        inherit_from     => [ "VC-common" ],
        template         => 1,
        CFLAGS           => add(picker(debug   => '/Od',
                                       release => '/O2')),
        cflags           => add(picker(default => '/Gs0 /GF /Gy',
                                       debug   =>
                                       sub {
                                           ($disabled{shared} ? "" : "/MDd");
                                       },
                                       release =>
                                       sub {
                                           ($disabled{shared} ? "" : "/MD");
                                       })),
        defines          => add(picker(default => [], # works as type cast
                                       debug   => [ "DEBUG", "_DEBUG" ])),
        lib_cflags       => add(sub { $disabled{shared} ? "/MT /Zl" : () }),
        # Following might/should appears controversial, i.e. defining
        # /MDd without evaluating $disabled{shared}. It works in
        # non-shared build because static library is compiled with /Zl
        # and bares no reference to specific RTL. And it works in
        # shared build because multiple /MDd options are not prohibited.
        # But why /MDd in static build? Well, basically this is just a
        # reference point, which allows to catch eventual errors that
        # would prevent those who want to wrap OpenSSL into own .DLL.
        # Why not /MD in release build then? Well, some are likely to
        # prefer [non-debug] openssl.exe to be free from Micorosoft RTL
        # redistributable.
        bin_cflags       => add(picker(debug   => "/MDd",
                                       release => sub { $disabled{shared} ? "/MT" : () },
                                      )),
        bin_lflags       => add("/subsystem:console /opt:ref"),
        ex_libs          => add(sub {
            my @ex_libs = ();
            push @ex_libs, 'ws2_32.lib' unless $disabled{sock};
            push @ex_libs, 'gdi32.lib advapi32.lib crypt32.lib user32.lib';
            return join(" ", @ex_libs);
        }),
    },
    "VC-WIN64-common" => {
        inherit_from     => [ "VC-noCE-common" ],
        template         => 1,
        ex_libs          => add(sub {
            my @ex_libs = ();
            push @ex_libs, 'bufferoverflowu.lib' if (`cl 2>&1` =~ /14\.00\.4[0-9]{4}\./);
            return join(" ", @_, @ex_libs);
        }),
        bn_ops           => add("SIXTY_FOUR_BIT"),
    },
    "VC-WIN64I" => {
        inherit_from     => [ "VC-WIN64-common" ],
        AS               => "ias",
        ASFLAGS          => "-d debug",
        asoutflag        => "-o ",
        sys_id           => "WIN64I",
        uplink_arch      => 'ia64',
        asm_arch         => 'ia64',
        perlasm_scheme   => "ias",
        multilib         => "-ia64",
    },
    "VC-WIN64A" => {
        inherit_from     => [ "VC-WIN64-common" ],
        AS               => sub { vc_win64a_info()->{AS} },
        ASFLAGS          => sub { vc_win64a_info()->{ASFLAGS} },
        asoutflag        => sub { vc_win64a_info()->{asoutflag} },
        asflags          => sub { vc_win64a_info()->{asflags} },
        sys_id           => "WIN64A",
        uplink_arch      => 'x86_64',
        asm_arch         => 'x86_64',
        perlasm_scheme   => "auto",
        multilib         => "-x64",
    },
    "VC-WIN32" => {
        inherit_from     => [ "VC-noCE-common" ],
        AS               => sub { vc_win32_info()->{AS} },
        ASFLAGS          => sub { vc_win32_info()->{ASFLAGS} },
        asoutflag        => sub { vc_win32_info()->{asoutflag} },
        asflags          => sub { vc_win32_info()->{asflags} },
        sys_id           => "WIN32",
        bn_ops           => add("BN_LLONG"),
        uplink_arch      => 'common',
        asm_arch         => 'x86',
        perlasm_scheme   => sub { vc_win32_info()->{perlasm_scheme} },
        # "WOW" stands for "Windows on Windows", and "VC-WOW" engages
        # some installation path heuristics in windows-makefile.tmpl...
        build_scheme     => add("VC-WOW", { separator => undef }),
    },
    "VC-CE" => {
        inherit_from     => [ "VC-common" ],
        CFLAGS           => add(picker(debug   => "/Od",
                                       release => "/O1i")),
        CPPDEFINES       => picker(debug   => [ "DEBUG", "_DEBUG" ]),
        LDFLAGS          => add("/nologo /opt:ref"),
        cflags           =>
            combine('/GF /Gy',
                    sub { vc_wince_info()->{cflags}; },
                    sub { `cl 2>&1` =~ /Version ([0-9]+)\./ && $1>=14
                              ? ($disabled{shared} ? " /MT" : " /MD")
                              : " /MC"; }),
        cppflags         => sub { vc_wince_info()->{cppflags}; },
        lib_defines      => add("NO_CHMOD", "OPENSSL_SMALL_FOOTPRINT"),
        lib_cppflags     => sub { vc_wince_info()->{cppflags}; },
        includes         =>
            add(combine(sub { defined(env('WCECOMPAT'))
                              ? '$(WCECOMPAT)/include' : (); },
                        sub { defined(env('PORTSDK_LIBPATH'))
                                  ? '$(PORTSDK_LIBPATH)/../../include'
                                  : (); })),
        lflags           => add(combine(sub { vc_wince_info()->{lflags}; },
                                        sub { defined(env('PORTSDK_LIBPATH'))
                                                  ? "/entry:mainCRTstartup" : (); })),
        sys_id           => "WINCE",
        bn_ops           => add("BN_LLONG"),
        ex_libs          => add(sub {
            my @ex_libs = ();
            push @ex_libs, 'ws2.lib' unless $disabled{sock};
            push @ex_libs, 'crypt32.lib';
            if (defined(env('WCECOMPAT'))) {
                my $x = '$(WCECOMPAT)/lib';
                if (-f "$x/env('TARGETCPU')/wcecompatex.lib") {
                    $x .= '/$(TARGETCPU)/wcecompatex.lib';
                } else {
                    $x .= '/wcecompatex.lib';
                }
                push @ex_libs, $x;
            }
            push @ex_libs, '$(PORTSDK_LIBPATH)/portlib.lib'
                if (defined(env('PORTSDK_LIBPATH')));
            push @ex_libs, '/nodefaultlib coredll.lib corelibc.lib'
                if (env('TARGETCPU') =~ /^X86|^ARMV4[IT]/);
            return join(" ", @ex_libs);
        }),
    },

#### MinGW
    "mingw-common" => {
        inherit_from     => [ 'BASE_unix' ],
        template         => 1,
        CC               => "gcc",
        CFLAGS           => picker(default => "-Wall",
                                   debug   => "-g -O0",
                                   release => "-O3"),
        cppflags         => combine("-DUNICODE -D_UNICODE -DWIN32_LEAN_AND_MEAN",
                                    threads("-D_MT")),
        lib_cppflags     => "-DL_ENDIAN",
        ex_libs          => add("-lws2_32 -lgdi32 -lcrypt32"),
        thread_scheme    => "winthreads",
        dso_scheme       => "win32",
        shared_target    => "mingw-shared",
        shared_cppflags  => add("_WINDLL"),
        shared_ldflag    => "-static-libgcc",

        perl_platform    => 'mingw',
    },
    "mingw" => {
        inherit_from     => [ "mingw-common" ],
        CFLAGS           => add(picker(release => "-fomit-frame-pointer")),
        cflags           => "-m32",
        sys_id           => "MINGW32",
        bn_ops           => add("BN_LLONG"),
        asm_arch         => 'x86',
        uplink_arch      => 'x86',
        perlasm_scheme   => "coff",
        shared_rcflag    => "--target=pe-i386",
        multilib         => "",
    },
    "mingw64" => {
        # As for uplink_arch. Applink makes it possible to use
        # .dll compiled with one compiler with application compiled with
        # another compiler. It's possible to engage Applink support in
        # mingw64 build, but it's not done, because until mingw64
        # supports structured exception handling, one can't seriously
        # consider its binaries for using with non-mingw64 run-time
        # environment. And as mingw64 is always consistent with itself,
        # Applink is never engaged and can as well be omitted.
        inherit_from     => [ "mingw-common" ],
        cflags           => "-m64",
        sys_id           => "MINGW64",
        bn_ops           => add("SIXTY_FOUR_BIT"),
        asm_arch         => 'x86_64',
        uplink_arch      => undef,
        perlasm_scheme   => "mingw64",
        shared_rcflag    => "--target=pe-x86-64",
        multilib         => "64",
    },

#### UEFI
    "UEFI" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        CFLAGS           => "-O",
        lib_cppflags     => "-DL_ENDIAN",
        sys_id           => "UEFI",
    },
    "UEFI-x86" => {
        inherit_from     => [ "UEFI" ],
        asm_arch         => 'x86',
        perlasm_scheme   => "win32n",
    },
    "UEFI-x86_64" => {
        inherit_from     => [ "UEFI" ],
        asm_arch         => 'x86_64',
        perlasm_scheme   => "nasm",
    },

#### UWIN
    "UWIN" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "cc",
        CFLAGS           => "-O -Wall",
        lib_cppflags     => "-DTERMIOS -DL_ENDIAN",
        sys_id           => "UWIN",
        bn_ops           => "BN_LLONG",
        dso_scheme       => "win32",
    },

#### Cygwin
    "Cygwin-common" => {
        inherit_from     => [ "BASE_unix" ],
        template         => 1,

        CC               => "gcc",
        CFLAGS           => picker(default => "-Wall",
                                   debug   => "-g -O0",
                                   release => "-O3"),
        lib_cppflags     => "-DTERMIOS -DL_ENDIAN",
        sys_id           => "CYGWIN",
        thread_scheme    => "pthread",
        dso_scheme       => "dlfcn",
        shared_target    => "cygwin-shared",
        shared_cppflags  => "-D_WINDLL",

        perl_platform    => 'Cygwin',
    },
    "Cygwin-x86" => {
        inherit_from     => [ "Cygwin-common" ],
        CFLAGS           => add(picker(release => "-O3 -fomit-frame-pointer")),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => "coff",
    },
    "Cygwin-x86_64" => {
        inherit_from     => [ "Cygwin-common" ],
        CC               => "gcc",
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'x86_64',
        perlasm_scheme   => "mingw64",
    },
    # Backward compatibility for those using this target
    "Cygwin" => {
	inherit_from     => [ "Cygwin-x86" ]
    },
    # In case someone constructs the Cygwin target name themself
    "Cygwin-i386" => {
	inherit_from     => [ "Cygwin-x86" ]
    },
    "Cygwin-i486" => {
	inherit_from     => [ "Cygwin-x86" ]
    },
    "Cygwin-i586" => {
	inherit_from     => [ "Cygwin-x86" ]
    },
    "Cygwin-i686" => {
	inherit_from     => [ "Cygwin-x86" ]
    },

##### MacOS X (a.k.a. Darwin) setup
    "darwin-common" => {
        inherit_from     => [ "BASE_unix" ],
        template         => 1,
        CC               => "cc",
        CFLAGS           => picker(debug   => "-g -O0",
                                   release => "-O3"),
        cppflags         => threads("-D_REENTRANT"),
        lflags           => add("-Wl,-search_paths_first"),
        sys_id           => "MACOSX",
        bn_ops           => "BN_LLONG RC4_CHAR",
        thread_scheme    => "pthreads",
        perlasm_scheme   => "osx32",
        dso_scheme       => "dlfcn",
        ranlib           => "ranlib -c",
        shared_target    => "darwin-shared",
        shared_cflag     => "-fPIC",
        shared_extension => ".\$(SHLIB_VERSION_NUMBER).dylib",
    },
    # Option "freeze" such as -std=gnu9x can't negatively interfere
    # with future defaults for below two targets, because MacOS X
    # for PPC has no future, it was discontinued by vendor in 2009.
    "darwin-ppc-cc" => { inherit_from => [ "darwin-ppc" ] }, # Historic alias
    "darwin-ppc" => {
        inherit_from     => [ "darwin-common" ],
        cflags           => add("-arch ppc -std=gnu9x -Wa,-force_cpusubtype_ALL"),
        lib_cppflags     => add("-DB_ENDIAN"),
        shared_cflag     => add("-fno-common"),
        asm_arch         => 'ppc32',
        perlasm_scheme   => "osx32",
    },
    "darwin64-ppc-cc" => { inherit_from => [ "darwin64-ppc" ] }, # Historic alias
    "darwin64-ppc" => {
        inherit_from     => [ "darwin-common" ],
        cflags           => add("-arch ppc64 -std=gnu9x"),
        lib_cppflags     => add("-DB_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
        asm_arch         => 'ppc64',
        perlasm_scheme   => "osx64",
    },
    "darwin-i386-cc" => { inherit_from => [ "darwin-i386" ] }, # Historic alias
    "darwin-i386" => {
        inherit_from     => [ "darwin-common" ],
        CFLAGS           => add(picker(release => "-fomit-frame-pointer")),
        cflags           => add("-arch i386"),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "BN_LLONG RC4_INT",
        asm_arch         => 'x86',
        perlasm_scheme   => "macosx",
    },
    "darwin64-x86_64-cc" => { inherit_from => [ "darwin64-x86_64" ] }, # Historic alias
    "darwin64-x86_64" => {
        inherit_from     => [ "darwin-common" ],
        CFLAGS           => add("-Wall"),
        cflags           => add("-arch x86_64"),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'x86_64',
        perlasm_scheme   => "macosx",
    },
    "darwin64-arm64-cc" => { inherit_from => [ "darwin64-arm64" ] }, # "Historic" alias
    "darwin64-arm64" => {
        inherit_from     => [ "darwin-common" ],
        CFLAGS           => add("-Wall"),
        cflags           => add("-arch arm64"),
        lib_cppflags     => add("-DL_ENDIAN"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        asm_arch         => 'aarch64',
        perlasm_scheme   => "ios64",
    },

##### GNU Hurd
    "hurd-x86" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => "-O3 -fomit-frame-pointer -Wall",
        cflags           => threads("-pthread"),
        lib_cppflags     => "-DL_ENDIAN",
        ex_libs          => add("-ldl", threads("-pthread")),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => 'elf',
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "linux-shared",
        shared_cflag     => "-fPIC",
    },

##### VxWorks for various targets
    "vxworks-ppc60x" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccppc",
        CFLAGS           => "-O2 -Wall -fstrength-reduce",
        cflags           => "-mrtp -mhard-float -mstrict-align -fno-implicit-fp -fno-builtin -fno-strict-aliasing",
        cppflags         => combine("-D_REENTRANT -DPPC32_fp60x -DCPU=PPC32",
                                    "_DTOOL_FAMILY=gnu -DTOOL=gnu",
                                    "-I\$(WIND_BASE)/target/usr/h",
                                    "-I\$(WIND_BASE)/target/usr/h/wrn/coreip"),
        sys_id           => "VXWORKS",
        lflags           => add("-L \$(WIND_BASE)/target/usr/lib/ppc/PPC32/common"),
        ex_libs          => add("-Wl,--defsym,__wrs_rtp_base=0xe0000000"),
    },
    "vxworks-ppcgen" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccppc",
        CFLAGS           => "-O1 -Wall",
        cflags           => "-mrtp -msoft-float -mstrict-align -fno-builtin -fno-strict-aliasing",
        cppflags         => combine("-D_REENTRANT -DPPC32 -DCPU=PPC32",
                                    "-DTOOL_FAMILY=gnu -DTOOL=gnu",
                                    "-I\$(WIND_BASE)/target/usr/h",
                                    "-I\$(WIND_BASE)/target/usr/h/wrn/coreip"),
        sys_id           => "VXWORKS",
        lflags           => add("-L \$(WIND_BASE)/target/usr/lib/ppc/PPC32/sfcommon"),
        ex_libs          => add("-Wl,--defsym,__wrs_rtp_base=0xe0000000"),
    },
    "vxworks-ppc405" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccppc",
        CFLAGS           => "-g",
        cflags           => "-msoft-float -mlongcall",
        cppflags         => combine("-D_REENTRANT -DPPC32 -DCPU=PPC405",
                                    "-DTOOL_FAMILY=gnu -DTOOL=gnu",
                                    "-I\$(WIND_BASE)/target/h"),
        sys_id           => "VXWORKS",
        lflags           => add("-r"),
    },
    "vxworks-ppc750" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccppc",
        CFLAGS           => "-ansi -fvolatile -Wall \$(DEBUG_FLAG)",
        cflags           => "-nostdinc -fno-builtin -fno-for-scope -fsigned-char -msoft-float -mlongcall",
        cppflags         => combine("-DPPC750 -D_REENTRANT -DCPU=PPC604",
                                    "-I\$(WIND_BASE)/target/h"),
        sys_id           => "VXWORKS",
        lflags           => add("-r"),
    },
    "vxworks-ppc750-debug" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccppc",
        CFLAGS           => "-ansi -fvolatile -Wall -g",
        cflags           => "-nostdinc -fno-builtin -fno-for-scope -fsigned-char -msoft-float -mlongcall",
        cppflags         => combine("-DPPC750 -D_REENTRANT -DCPU=PPC604",
                                    "-DPEDANTIC -DDEBUG",
                                    "-I\$(WIND_BASE)/target/h"),
        sys_id           => "VXWORKS",
        lflags           => add("-r"),
    },
    "vxworks-ppc860" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccppc",
        cflags           => "-nostdinc -msoft-float",
        cppflags         => combine("-DCPU=PPC860 -DNO_STRINGS_H",
                                    "-I\$(WIND_BASE)/target/h"),
        sys_id           => "VXWORKS",
        lflags           => add("-r"),
    },
    "vxworks-simlinux" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccpentium",
        cflags           => "-B\$(WIND_BASE)/host/\$(WIND_HOST_TYPE)/lib/gcc-lib/ -fno-builtin -fno-defer-pop",
        cppflags         => combine("-D_VSB_CONFIG_FILE=\"\$(WIND_BASE)/target/lib/h/config/vsbConfig.h\"",
                                    "-DL_ENDIAN -DCPU=SIMLINUX -DNO_STRINGS_H",
                                    "-DTOOL_FAMILY=gnu -DTOOL=gnu",
                                    "-DOPENSSL_NO_HW_PADLOCK",
                                    "-I\$(WIND_BASE)/target/h",
                                    "-I\$(WIND_BASE)/target/h/wrn/coreip"),
        sys_id           => "VXWORKS",
        lflags           => add("-r"),
        ranlib           => "ranlibpentium",
    },
    "vxworks-mips" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "ccmips",
        CFLAGS           => "-O -G 0",
        cflags           => "-mrtp -mips2 -B\$(WIND_BASE)/host/\$(WIND_HOST_TYPE)/lib/gcc-lib/ -msoft-float -mno-branch-likely -fno-builtin -fno-defer-pop",
        cppflags         => combine("-D_VSB_CONFIG_FILE=\"\$(WIND_BASE)/target/lib/h/config/vsbConfig.h\"",
                                    "-DCPU=MIPS32 -DNO_STRINGS_H",
                                    "-DTOOL_FAMILY=gnu -DTOOL=gnu",
                                    "-DOPENSSL_NO_HW_PADLOCK",
                                    threads("-D_REENTRANT"),
                                    "-I\$(WIND_BASE)/target/h",
                                    "-I\$(WIND_BASE)/target/h/wrn/coreip"),
        sys_id           => "VXWORKS",
        lflags           => add("-L \$(WIND_BASE)/target/usr/lib/mips/MIPSI32/sfcommon"),
        ex_libs          => add("-Wl,--defsym,__wrs_rtp_base=0xe0000000"),
        thread_scheme    => "pthreads",
        asm_arch         => 'mips32',
        perlasm_scheme   => "o32",
        ranlib           => "ranlibmips",
    },

#### uClinux
    "uClinux-dist" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => sub { env('CC') },
        cppflags         => threads("-D_REENTRANT"),
        ex_libs          => add("\$(LDLIBS)"),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "pthreads",
        dso_scheme       => sub { env('LIBSSL_dlfcn') },
        shared_target    => "linux-shared",
        shared_cflag     => "-fPIC",
        ranlib           => sub { env('RANLIB') },
    },
    "uClinux-dist64" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => sub { env('CC') },
        cppflags         => threads("-D_REENTRANT"),
        ex_libs          => add("\$(LDLIBS)"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        thread_scheme    => "pthreads",
        dso_scheme       => sub { env('LIBSSL_dlfcn') },
        shared_target    => "linux-shared",
        shared_cflag     => "-fPIC",
        ranlib           => sub { env('RANLIB') },
    },

);
