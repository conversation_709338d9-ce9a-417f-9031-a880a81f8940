my %targets = (
    "BC-32" => {
        inherit_from     => [ "BASE_Windows" ],
        sys_id           => "WIN32",
        bn_ops           => "BN_LLONG",
        thread_scheme    => "winthreads",
        cc               => "bcc32c",
        CPP              => "cpp32 -oCON -Sc -Sr",
        defines          => add("WIN32_LEAN_AND_MEAN", "OPENSSL_SYS_WIN32",
                                "L_ENDIAN", "DSO_WIN32", "_stricmp=stricmp",
                                "_strnicmp=strnicmp", "_malloca=malloc",
                                "_freea=free", "_setmode=setmode"),
        cflags           => picker(default => add("-q -c",
                                                  threads("-tM"),
                                                  shared("-tR")),
                                   debug   => "-Od -v -vi- -D_DEBUG",
                                   release => "-O2"),
        bin_cflags       => "-tWC",
        lib_cflags       => shared("-tWD -D_WINDLL -D_DLL"),
        coutflag         => "-o",

        # -Sx isn't documented, but 'cpp32 -H -S' explains it:
        #
        # -Sx     Omit preprocessed text in output
        makedepcmd       => "cpp32 -oCON -Sx -Hp",
        makedep_scheme   => "embarcadero",

        LD               => "ilink32",
        LDFLAGS          => picker(default => "-x -Gn -q -w-dup",
                                   debug   => '-j"$(BDS)\lib\win32c\debug" ' .
                                              '-L"$(BDS)\lib\win32c\debug" -v',
                                   release => '-j"$(BDS)\lib\win32c\release" ' .
                                              '-L"$(BDS)\lib\win32c\release"'),
        bin_lflags       => "-ap -Tpe c0x32.obj wildargs.obj",
        ldoutflag        => ",",
        ldpostoutflag    => ",,",
        ld_resp_delim    => " +\n",
        ex_libs          => add(sub {
            my @ex_libs = ("import32.lib",
                           ($disabled{shared}
                            ? ($disabled{threads} ? "cw32.lib" : "cw32mt.lib")
                            : ($disabled{threads} ? "cw32i.lib" : "cw32mti.lib")));
            push @ex_libs, "ws2_32.lib" unless $disabled{sock};
            return join(" ", @ex_libs);
        }),
        AR               => "tlib",
        ARFLAGS          => "/P256 /N /u",
        ar_resp_delim    => " &\n",
        RC               => "brcc32",
        RCFLAGS          => '-i"$(BDS)\include\windows\sdk"',
        rcoutflag        => "-fo",
        shared_target    => "win-shared",
        shared_ldflag    => "-aa -Tpd c0d32.obj",
        lddefflag        => ",",
        ldresflag        => ",",
        ld_implib_rule   => 'implib -a $< $**',
        dso_scheme       => "win32",
        shared_defflag   => '',
        perl_platform    => 'Windows::cppbuilder',
        uplink_arch      => 'common',
    }
);
