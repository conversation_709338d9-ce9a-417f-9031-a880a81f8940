#### iPhoneOS/iOS
#
# It takes recent enough Xcode to use following two targets. It shouldn't
# be a problem by now, but if they don't work, original targets below
# that depend on manual definition of environment variables should still
# work...
#
my %targets = (
    "ios-common" => {
        template         => 1,
        inherit_from     => [ "darwin-common" ],
        sys_id           => "iOS",
        disable          => [ "shared", "async" ],
    },
    "ios-xcrun" => {
        inherit_from     => [ "ios-common" ],
        # It should be possible to go below iOS 6 and even add -arch armv6,
        # thus targeting iPhone pre-3GS, but it's assumed to be irrelevant
        # at this point.
        CC               => "xcrun -sdk iphoneos cc",
        cflags           => add("-arch armv7 -mios-version-min=6.0.0 -fno-common"),
        asm_arch         => 'armv4',
        perlasm_scheme   => "ios32",
    },
    "ios64-xcrun" => {
        inherit_from     => [ "ios-common" ],
        CC               => "xcrun -sdk iphoneos cc",
        cflags           => add("-arch arm64 -mios-version-min=7.0.0 -fno-common"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
        asm_arch         => 'aarch64',
        perlasm_scheme   => "ios64",
    },
    "iossimulator-xcrun" => {
        inherit_from     => [ "ios-common" ],
        CC               => "xcrun -sdk iphonesimulator cc",
    },
# It takes three prior-set environment variables to make it work:
#
# CROSS_COMPILE=/where/toolchain/is/usr/bin/ [note ending slash]
# CROSS_TOP=/where/SDKs/are
# CROSS_SDK=iPhoneOSx.y.sdk
#
# Exact paths vary with Xcode releases, but for couple of last ones
# they would look like this:
#
# CROSS_COMPILE=`xcode-select --print-path`/Toolchains/XcodeDefault.xctoolchain/usr/bin/
# CROSS_TOP=`xcode-select --print-path`/Platforms/iPhoneOS.platform/Developer
# CROSS_SDK=iPhoneOS.sdk
#
    "iphoneos-cross" => {
        inherit_from     => [ "ios-common" ],
        cflags           => add("-isysroot \$(CROSS_TOP)/SDKs/\$(CROSS_SDK) -fno-common"),
    },
    "ios-cross" => {
        inherit_from     => [ "ios-xcrun" ],
        CC               => "cc",
        cflags           => add("-isysroot \$(CROSS_TOP)/SDKs/\$(CROSS_SDK)"),
    },
    "ios64-cross" => {
        inherit_from     => [ "ios64-xcrun" ],
        CC               => "cc",
        cflags           => add("-isysroot \$(CROSS_TOP)/SDKs/\$(CROSS_SDK)"),
    },
);
