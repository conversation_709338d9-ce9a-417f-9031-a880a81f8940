SUBDIRS=lib

# Program init source, that don't have direct linkage with the rest of the
# source, and can therefore not be part of a library.
IF[{- !$disabled{uplink} -}]
  $INITSRC=../ms/applink.c
ENDIF

# Source for the 'openssl' program
$OPENSSLSRC=\
        openssl.c \
        asn1parse.c ca.c ciphers.c crl.c crl2pkcs7.c dgst.c \
        enc.c errstr.c \
        genpkey.c kdf.c mac.c nseq.c passwd.c pkcs7.c \
        pkcs8.c pkey.c pkeyparam.c pkeyutl.c prime.c rand.c req.c \
        s_client.c s_server.c s_time.c sess_id.c smime.c speed.c \
        spkac.c verify.c version.c x509.c rehash.c storeutl.c \
        list.c info.c fipsinstall.c pkcs12.c
IF[{- !$disabled{'ec'} -}]
  $OPENSSLSRC=$OPENSSLSRC ec.c ecparam.c
ENDIF
IF[{- !$disabled{'ocsp'} -}]
  $OPENSSLSRC=$OPENSSLSRC ocsp.c
ENDIF
IF[{- !$disabled{'srp'} -}]
  $OPENSSLSRC=$OPENSSLSRC srp.c
ENDIF
IF[{- !$disabled{'ts'} -}]
  $OPENSSLSRC=$OPENSSLSRC ts.c
ENDIF
IF[{- !$disabled{'dh'} -}]
$OPENSSLSRC=$OPENSSLSRC dhparam.c
ENDIF
IF[{- !$disabled{'dsa'} -}]
$OPENSSLSRC=$OPENSSLSRC dsa.c dsaparam.c gendsa.c
ENDIF
IF[{- !$disabled{'engine'} -}]
$OPENSSLSRC=$OPENSSLSRC engine.c
ENDIF
IF[{- !$disabled{'rsa'} -}]
$OPENSSLSRC=$OPENSSLSRC rsa.c genrsa.c
ENDIF
IF[{- !$disabled{'deprecated-3.0'} -}]
  IF[{- !$disabled{'rsa'} -}]
    $OPENSSLSRC=$OPENSSLSRC rsautl.c
  ENDIF
ENDIF
IF[{- !$disabled{'cms'} -}]
  $OPENSSLSRC=$OPENSSLSRC cms.c
ENDIF
IF[{- !$disabled{'cmp'} -}]
  $OPENSSLSRC=$OPENSSLSRC cmp.c lib/cmp_mock_srv.c
ENDIF
IF[{- !$disabled{'delegated-credential'} -}]
  $OPENSSLSRC=$OPENSSLSRC delecred.c
ENDIF

IF[{- !$disabled{'paillier'} -}]
  $OPENSSLSRC=$OPENSSLSRC paillier.c
ENDIF

IF[{- !$disabled{'ec_elgamal'} -}]
  $OPENSSLSRC=$OPENSSLSRC ec_elgamal.c
ENDIF

IF[{- !$disabled{'bulletproofs'} -}]
  $OPENSSLSRC=$OPENSSLSRC bulletproofs.c
ENDIF

IF[{- !$disabled{'smtc'} -}]
  $OPENSSLSRC=$OPENSSLSRC mod.c
ENDIF

IF[{- !$disabled{'sm2_threshold'} -}]
  $OPENSSLSRC=$OPENSSLSRC sm2_threshold.c
ENDIF

IF[{- !$disabled{'sdf-lib'} -}]
  $OPENSSLSRC=$OPENSSLSRC sdf.c
ENDIF

IF[{- !$disabled{apps} -}]
  PROGRAMS=openssl
  SOURCE[openssl]=$INITSRC $OPENSSLSRC
  INCLUDE[openssl]=.. ../include include
  DEPEND[openssl]=libapps.a ../libssl

  IF[{- !$disabled{'smtc'} -}]
    DEFINE[openssl]=SMTC_MODULE
  ENDIF

  # The nocheck attribute is picked up by progs.pl as a signal not to look
  # at that file; some systems may have locked it as the output file, and
  # therefore don't allow it to be read at the same time, making progs.pl
  # fail.
  SOURCE[openssl]{nocheck}=progs.c
  DEPEND[${OPENSSLSRC/.c/.o} progs.o]=progs.h
  GENERATE[progs.c]=progs.pl "-C" $(APPS_OPENSSL)
  GENERATE[progs.h]=progs.pl "-H" $(APPS_OPENSSL)
  # progs.pl tries to read all 'openssl' sources, including progs.c, so we make
  # sure things are generated in the correct order.
  DEPEND[progs.h]=progs.c
  # Because the files to look through may change (depends on $OPENSSLSRC),
  # always depend on a changed configuration.
  DEPEND[progs.c]=../configdata.pm

  IF[{- $config{target} =~ /^(?:Cygwin|mingw|VC-|BC-)/ -}]
    GENERATE[openssl.rc]=../util/mkrc.pl openssl
    SOURCE[openssl]=openssl.rc
  ENDIF

  SCRIPTS{misc}=CA.pl
  SOURCE[CA.pl]=CA.pl.in
  # linkname tells build files that a symbolic link or copy of this script
  # without extension must be installed as well.  Unix or Unix lookalike only.
  SCRIPTS{misc,linkname=tsget}=tsget.pl
  SOURCE[tsget.pl]=tsget.in
ENDIF
