# Copyright 2021-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

name: Compiler Zoo CI

on: [pull_request, push]

jobs:
  compiler:
    strategy:
      fail-fast: false
      matrix:
        zoo: [
          {
            cc: gcc-9,
            distro: ubuntu-22.04
          }, {
            cc: gcc-10,
            distro: ubuntu-22.04
          }, {
            cc: gcc-11,
            distro: ubuntu-22.04
          }, {
            cc: gcc-12,
            distro: ubuntu-22.04
          }, {
            cc: clang-11,
            distro: ubuntu-22.04
          }, {
            cc: clang-12,
            distro: ubuntu-22.04
          }, {
            cc: clang-13,
            distro: ubuntu-22.04
          }, {
            cc: clang-14,
            distro: ubuntu-22.04
          }
        ]
    # We set per-compiler now to allow testing with both older and newer sets
    # Often, the full range of oldest->newest compilers we want aren't available
    # in a single version of Ubuntu.
    runs-on: ${{ matrix.zoo.distro }}
    steps:
    - name: install packages
      run: |
        sudo apt-get update
        sudo apt-get -yq --force-yes install ${{ matrix.zoo.cc }}
    - uses: actions/checkout@v2

    - name: config
      run: |
        CC=${{ matrix.zoo.cc }} ./config --banner=Configured no-shared \
            -Wall -Werror enable-ntls --strict-warnings

    - name: config dump
      run: ./configdata.pm --dump
    - name: make
      run: make -s -j4
    - name: make test
      run: make test HARNESS_JOBS=${HARNESS_JOBS:-4}
