# Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html
#
# Copyright 2023-2024 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt

name: Static Analysis

#Run once a day
on:
  workflow_dispatch:
  schedule:
    - cron:  '20 0 * * *'

permissions:
  contents: read

jobs:
  coverity:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: tool download
      run: |
        wget https://scan.coverity.com/download/linux64 \
             --post-data "token=${{ secrets.COVERITY_TOKEN }}&project=Tongsuo-Project%2FTongsuo" \
             --progress=dot:giga -O coverity_tool.tgz
    - name: config
      run: |
        CC=gcc ./config --banner=Configured --debug enable-ntls enable-smtc enable-smtc-debug enable-rc5 enable-ssl3 \
        enable-nextprotoneg enable-ssl3-method enable-weak-ssl-ciphers enable-zlib enable-ec_nistp_64_gcc_128 no-shared \
        enable-buildtest-c++ enable-external-tests enable-ec_elgamal enable-twisted_ec_elgamal enable-paillier \
        enable-cert-compression enable-delegated-credential enable-bn-method enable-bulletproofs enable-nizk \
        enable-zkp-gadget enable-sm2_threshold -DPEDANTIC
    - name: config dump
      run: ./configdata.pm --dump
    - name: tool install
      run: tar xzf coverity_tool.tgz
    - name: make
      run: ./cov-analysis*/bin/cov-build --dir cov-int make -s -j4
    - name: archive
      run: tar czvf tongsuo.tgz cov-int
    - name: Coverity upload
      run: |
        curl --form token="${{ secrets.COVERITY_TOKEN }}" \
             --form email=<EMAIL> \
             --form file=@tongsuo.tgz \
             --form version="`date -u -I` `git rev-parse --short HEAD`" \
             --form description="analysis of `git branch --show-current`" \
             https://scan.coverity.com/builds?project=Tongsuo-Project%2FTongsuo
