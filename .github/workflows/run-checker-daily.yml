# Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

name: Run-checker daily
# Jobs run daily

on:
  schedule:
    - cron: '0 6 * * *'
jobs:
  run-checker:
    strategy:
      fail-fast: false
      matrix:
        opt: [
          386,
          no-afalgeng,
          no-asan,
          no-asm,
          no-async,
          no-autoalginit,
          no-autoerrinit,
          no-autoload-config,
          no-buildtest-c++,
          no-bulk,
          no-cached-fetch,
          no-capieng,
          no-chacha,
          no-cmac,
          no-comp,
          enable-crypto-mdebug,
          no-crypto-mdebug,
          enable-crypto-mdebug-backtrace,
          no-crypto-mdebug-backtrace,
          no-deprecated,
          no-des,
          no-devcryptoeng,
          no-dh,
          no-dsa,
          no-dtls1,
          no-dtls1_2,
          no-dtls1_2-method,
          no-dtls1-method,
          no-ecdh,
          no-ecdsa,
          enable-ec_nistp_64_gcc_128,
          enable-ec_sm2p_64_gcc_128
          no-ec_nistp_64_gcc_128,
          enable-egd,
          no-egd,
          no-engine,
          no-external-tests,
          enable-fips,
          enable-fips enable-acvp-tests,
          enable-fips no-tls1_3,
          no-fuzz-afl,
          no-fuzz-libfuzzer,
          enable-heartbeats,
          no-heartbeats,
          no-hw,
          no-hw-padlock,
          no-makedepend,
          no-module,
          no-msan,
          no-multiblock,
          no-nextprotoneg,
          no-ocb,
          no-ocsp,
          no-padlockeng,
          no-pic,
          no-pinshared,
          no-poly1305,
          no-posix-io,
          no-psk,
          no-rc4,
          enable-rc5,
          no-rc5,
          no-rdrand,
          no-rfc3779,
          no-scrypt,
          no-sctp,
          no-secure-memory,
          no-shared,
          no-siphash,
          no-siv,
          no-sm2,
          no-sm3,
          no-sm4,
          no-sock,
          no-sse2,
          no-ssl,
          no-ssl3,
          no-ssl3-method,
          no-ssl-trace,
          no-static-engine no-shared,
          no-stdio,
          no-tls1,
          no-tls1_1,
          no-tls1_1-method,
          no-tls1_2,
          no-tls1_2-method,
          no-tls1-method,
          no-trace,
          no-ubsan,
          no-ui-console,
          enable-unit-test,
          no-uplink,
          no-weak-ssl-ciphers,
          no-zlib,
          enable-zlib-dynamic,
          no-zlib-dynamic,
          enable-ntls,
          enable-ec_elgamal enable-twisted_ec_elgamal,
          enable-bulletproofs,
          enable-bulletproofs enable-nizk enable-zkp-gadget enable-ec_elgamal enable-twisted_ec_elgamal,
          enable-sm2_threshold,
          -DOPENSSL_NO_BUILTIN_OVERFLOW_CHECKING
        ]
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: config
      run: CC=clang ./config --banner=Configured --strict-warnings ${{ matrix.opt }}
    - name: config dump
      run: ./configdata.pm --dump
    - name: make
      run: make -s -j4
    - name: make test
      run: make test HARNESS_JOBS=${HARNESS_JOBS:-4}
