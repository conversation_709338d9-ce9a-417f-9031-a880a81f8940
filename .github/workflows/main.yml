# Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

name: CIFuzz
on: [pull_request, push]
jobs:
  Fuzzing:
    runs-on: ubuntu-latest
    steps:
    - name: Build Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@master
      with:
        dry-run: false
    - name: Run Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@master
      with:
        fuzz-seconds: 600
        dry-run: false
    - name: Upload Crash
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: artifacts
        path: ./out/artifacts
