# Copyright 2021-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

name: Coverage

#Run once a day
on:
  workflow_dispatch:
  schedule:
    - cron:  '49 0 * * *'

jobs:
  coverage:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: package installs
      run: |
        sudo apt-get -yq install lcov
    - name: config
      run: |
        CC=gcc ./config --banner=Configured --debug --coverage no-asm enable-rc5 enable-ssl3 enable-nextprotoneg \
            enable-ssl3-method enable-weak-ssl-ciphers enable-zlib enable-ec_nistp_64_gcc_128 enable-ec_sm2p_64_gcc_128 \
            no-shared enable-buildtest-c++ enable-external-tests -DPEDANTIC -DFUZZING_BUILD_MODE_UNSAFE_FOR_PRODUCTION \
            enable-ntls enable-cert-compression enable-delegated-credential enable-status enable-ec_elgamal \
            enable-paillier enable-sm2_threshold

    - name: config dump
      run: ./configdata.pm --dump
    - name: make
      run: make -s -j4
    - name: make test
      run: make test HARNESS_JOBS=${HARNESS_JOBS:-4}
    - name: generate coverage info
      run: lcov -d . -c -o ./lcov.info
    - name: Coveralls upload
      uses: coverallsapp/github-action@v1.1.2
      with:
        github-token: ${{ secrets.github_token }}
        path-to-lcov: ./lcov.info
